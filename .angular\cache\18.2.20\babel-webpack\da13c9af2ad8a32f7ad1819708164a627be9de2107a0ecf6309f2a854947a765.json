{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function switchMap(project, resultSelector) {\n  return operate((source, subscriber) => {\n    let innerSubscriber = null;\n    let index = 0;\n    let isComplete = false;\n    const checkComplete = () => isComplete && !innerSubscriber && subscriber.complete();\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      innerSubscriber === null || innerSubscriber === void 0 ? void 0 : innerSubscriber.unsubscribe();\n      let innerIndex = 0;\n      const outerIndex = index++;\n      innerFrom(project(value, outerIndex)).subscribe(innerSubscriber = createOperatorSubscriber(subscriber, innerValue => subscriber.next(resultSelector ? resultSelector(value, innerValue, outerIndex, innerIndex++) : innerValue), () => {\n        innerSubscriber = null;\n        checkComplete();\n      }));\n    }, () => {\n      isComplete = true;\n      checkComplete();\n    }));\n  });\n}", "map": {"version": 3, "names": ["innerFrom", "operate", "createOperatorSubscriber", "switchMap", "project", "resultSelector", "source", "subscriber", "innerSubscriber", "index", "isComplete", "checkComplete", "complete", "subscribe", "value", "unsubscribe", "innerIndex", "outerIndex", "innerValue", "next"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/1st-Month<PERSON><PERSON>-<PERSON><PERSON>/monthsary-website/node_modules/rxjs/dist/esm/internal/operators/switchMap.js"], "sourcesContent": ["import { innerFrom } from '../observable/innerFrom';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function switchMap(project, resultSelector) {\n    return operate((source, subscriber) => {\n        let innerSubscriber = null;\n        let index = 0;\n        let isComplete = false;\n        const checkComplete = () => isComplete && !innerSubscriber && subscriber.complete();\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            innerSubscriber === null || innerSubscriber === void 0 ? void 0 : innerSubscriber.unsubscribe();\n            let innerIndex = 0;\n            const outerIndex = index++;\n            innerFrom(project(value, outerIndex)).subscribe((innerSubscriber = createOperatorSubscriber(subscriber, (innerValue) => subscriber.next(resultSelector ? resultSelector(value, innerValue, outerIndex, innerIndex++) : innerValue), () => {\n                innerSubscriber = null;\n                checkComplete();\n            })));\n        }, () => {\n            isComplete = true;\n            checkComplete();\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,yBAAyB;AACnD,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,OAAO,SAASC,SAASA,CAACC,OAAO,EAAEC,cAAc,EAAE;EAC/C,OAAOJ,OAAO,CAAC,CAACK,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,eAAe,GAAG,IAAI;IAC1B,IAAIC,KAAK,GAAG,CAAC;IACb,IAAIC,UAAU,GAAG,KAAK;IACtB,MAAMC,aAAa,GAAGA,CAAA,KAAMD,UAAU,IAAI,CAACF,eAAe,IAAID,UAAU,CAACK,QAAQ,CAAC,CAAC;IACnFN,MAAM,CAACO,SAAS,CAACX,wBAAwB,CAACK,UAAU,EAAGO,KAAK,IAAK;MAC7DN,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACO,WAAW,CAAC,CAAC;MAC/F,IAAIC,UAAU,GAAG,CAAC;MAClB,MAAMC,UAAU,GAAGR,KAAK,EAAE;MAC1BT,SAAS,CAACI,OAAO,CAACU,KAAK,EAAEG,UAAU,CAAC,CAAC,CAACJ,SAAS,CAAEL,eAAe,GAAGN,wBAAwB,CAACK,UAAU,EAAGW,UAAU,IAAKX,UAAU,CAACY,IAAI,CAACd,cAAc,GAAGA,cAAc,CAACS,KAAK,EAAEI,UAAU,EAAED,UAAU,EAAED,UAAU,EAAE,CAAC,GAAGE,UAAU,CAAC,EAAE,MAAM;QACtOV,eAAe,GAAG,IAAI;QACtBG,aAAa,CAAC,CAAC;MACnB,CAAC,CAAE,CAAC;IACR,CAAC,EAAE,MAAM;MACLD,UAAU,GAAG,IAAI;MACjBC,aAAa,CAAC,CAAC;IACnB,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}