{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nconst _forTrack0 = ($index, $item) => $item.id;\nfunction MemoriesComponent_For_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 17);\n    i0.ɵɵelement(2, \"img\", 18)(3, \"div\", 19);\n    i0.ɵɵelementStart(4, \"div\", 20)(5, \"span\", 21);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 22)(8, \"h3\", 23);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 24);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const photo_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", photo_r1.image, i0.ɵɵsanitizeUrl)(\"alt\", photo_r1.caption);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(photo_r1.emoji);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(photo_r1.caption);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(photo_r1.description);\n  }\n}\nfunction MemoriesComponent_For_20_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"p\", 29);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"div\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(day_r3.message);\n  }\n}\nfunction MemoriesComponent_For_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵlistener(\"click\", function MemoriesComponent_For_20_Template_div_click_0_listener() {\n      const day_r3 = i0.ɵɵrestoreView(_r2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.toggleMemory(day_r3.id));\n    });\n    i0.ɵɵelementStart(1, \"div\", 26)(2, \"span\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(4, MemoriesComponent_For_20_div_4_Template, 4, 1, \"div\", 27);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const day_r3 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(day_r3.unlocked ? \"bg-gradient-to-br from-pink-200 to-purple-200 border-pink-300 hover:scale-110 card-hover\" : \"bg-gray-100 border-gray-200 cursor-not-allowed\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(day_r3.unlocked ? \"text-gray-700\" : \"text-gray-400\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", day_r3.unlocked ? day_r3.day : \"\\uD83D\\uDD12\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", day_r3.showMessage && day_r3.unlocked);\n  }\n}\nfunction MemoriesComponent_For_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 31);\n    i0.ɵɵtext(2, \"\\uD83D\\uDC9D\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\", 32);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 33);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const promise_r5 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(promise_r5.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(promise_r5.text);\n  }\n}\nexport class MemoriesComponent {\n  constructor() {\n    this.photoGallery = [{\n      id: 1,\n      emoji: '📸',\n      caption: 'Our first selfie together',\n      description: 'The moment we decided to capture our happiness',\n      image: 'https://images.unsplash.com/photo-1516589178581-6cd7833ae3b2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80'\n    }, {\n      id: 2,\n      emoji: '🌅',\n      caption: 'Watching the sunrise',\n      description: 'Thinking of you as the day begins',\n      image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80'\n    }, {\n      id: 3,\n      emoji: '🍕',\n      caption: 'Our first virtual dinner date',\n      description: 'Distance couldn\\'t stop us from sharing a meal',\n      image: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80'\n    }, {\n      id: 4,\n      emoji: '🎮',\n      caption: 'Gaming together until 3 AM',\n      description: 'Time flies when we\\'re having fun',\n      image: 'https://images.unsplash.com/photo-1511512578047-dfb367046420?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80'\n    }, {\n      id: 5,\n      emoji: '💌',\n      caption: 'The love letter that started it all',\n      description: 'Words that touched my heart forever',\n      image: 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80'\n    }, {\n      id: 6,\n      emoji: '🌙',\n      caption: 'Good night texts every single day',\n      description: 'Never missing a chance to say sweet dreams',\n      image: 'https://images.unsplash.com/photo-1494972308805-463bc619d34e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80'\n    }];\n    this.memoryJar = Array.from({\n      length: 30\n    }, (_, i) => ({\n      id: i + 1,\n      day: i + 1,\n      unlocked: i < 10,\n      // First 10 days unlocked for demo\n      showMessage: false,\n      message: `Day ${i + 1}: Another beautiful day of loving you! 💖`\n    }));\n    this.promises = [{\n      id: 1,\n      title: 'Always Listen',\n      text: 'I promise to always listen to you with my heart, not just my ears.'\n    }, {\n      id: 2,\n      title: 'Support Your Dreams',\n      text: 'I will always be your biggest cheerleader and support your goals.'\n    }, {\n      id: 3,\n      title: 'Make You Laugh',\n      text: 'I promise to find new ways to make you smile every single day.'\n    }, {\n      id: 4,\n      title: 'Be Patient',\n      text: 'I will be patient with you, especially when you are not patient with yourself.'\n    }, {\n      id: 5,\n      title: 'Love You More',\n      text: 'I promise to love you more today than yesterday, but less than tomorrow.'\n    }, {\n      id: 6,\n      title: 'Never Stop Trying',\n      text: 'I will never stop trying to be the best boyfriend you deserve.'\n    }];\n  }\n  toggleMemory(dayId) {\n    const day = this.memoryJar.find(d => d.id === dayId);\n    if (day && day.unlocked) {\n      day.showMessage = !day.showMessage;\n      // Hide other messages\n      this.memoryJar.forEach(d => {\n        if (d.id !== dayId) d.showMessage = false;\n      });\n    }\n  }\n  static {\n    this.ɵfac = function MemoriesComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MemoriesComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: MemoriesComponent,\n      selectors: [[\"app-memories\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 29,\n      vars: 0,\n      consts: [[1, \"py-20\", \"px-6\", \"sm:px-8\", \"lg:px-12\", \"min-h-screen\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\", \"z-0\"], [\"src\", \"https://images.unsplash.com/photo-1529333166437-7750a6dd5a70?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2025&q=80\", \"alt\", \"Romantic memories background\", 1, \"w-full\", \"h-full\", \"object-cover\", \"opacity-8\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-br\", \"from-pink-50/95\", \"to-purple-50/95\"], [1, \"max-w-6xl\", \"mx-auto\", \"relative\", \"z-10\"], [1, \"text-center\", \"mb-20\"], [1, \"font-playfair\", \"text-4xl\", \"sm:text-5xl\", \"lg:text-6xl\", \"text-romantic\", \"mb-6\"], [1, \"font-poppins\", \"text-xl\", \"text-gray-600\", \"max-w-2xl\", \"mx-auto\", \"font-light\"], [1, \"grid\", \"grid-cols-1\", \"sm:grid-cols-2\", \"lg:grid-cols-3\", \"gap-8\", \"mb-20\"], [1, \"glass-effect\", \"rounded-2xl\", \"overflow-hidden\", \"card-hover\", \"border\", \"border-white/20\", \"group\"], [1, \"text-center\", \"mb-16\"], [1, \"font-playfair\", \"text-3xl\", \"sm:text-4xl\", \"text-romantic\", \"mb-6\"], [1, \"font-poppins\", \"text-lg\", \"text-gray-600\", \"max-w-2xl\", \"mx-auto\", \"font-light\", \"mb-12\"], [1, \"grid\", \"grid-cols-5\", \"sm:grid-cols-6\", \"lg:grid-cols-10\", \"gap-4\", \"mb-20\"], [1, \"relative\", \"aspect-square\"], [1, \"grid\", \"grid-cols-1\", \"sm:grid-cols-2\", \"lg:grid-cols-3\", \"gap-8\"], [1, \"glass-effect\", \"rounded-2xl\", \"p-8\", \"text-center\", \"card-hover\", \"border\", \"border-white/20\"], [1, \"relative\", \"h-48\", \"overflow-hidden\"], [1, \"w-full\", \"h-full\", \"object-cover\", \"group-hover:scale-110\", \"transition-transform\", \"duration-700\", 3, \"src\", \"alt\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-t\", \"from-black/40\", \"to-transparent\"], [1, \"absolute\", \"top-4\", \"left-4\"], [1, \"text-3xl\", \"animate-soft-pulse\", \"drop-shadow-lg\"], [1, \"p-6\", \"text-center\"], [1, \"font-playfair\", \"text-xl\", \"font-semibold\", \"text-gray-700\", \"mb-3\"], [1, \"font-poppins\", \"text-gray-600\", \"text-sm\", \"font-light\"], [1, \"relative\", \"aspect-square\", 3, \"click\"], [1, \"w-full\", \"h-full\", \"rounded-xl\", \"flex\", \"items-center\", \"justify-center\", \"text-lg\", \"font-semibold\", \"cursor-pointer\", \"transition-all\", \"duration-300\", \"border-2\"], [\"class\", \"absolute top-full left-1/2 transform -translate-x-1/2 mt-2 bg-white rounded-lg p-4 shadow-lg border border-pink-200 z-10 w-64 text-center\", 4, \"ngIf\"], [1, \"absolute\", \"top-full\", \"left-1/2\", \"transform\", \"-translate-x-1/2\", \"mt-2\", \"bg-white\", \"rounded-lg\", \"p-4\", \"shadow-lg\", \"border\", \"border-pink-200\", \"z-10\", \"w-64\", \"text-center\"], [1, \"font-poppins\", \"text-sm\", \"text-gray-700\", \"font-light\"], [1, \"absolute\", \"-top-2\", \"left-1/2\", \"transform\", \"-translate-x-1/2\", \"w-4\", \"h-4\", \"bg-white\", \"border-l\", \"border-t\", \"border-pink-200\", \"rotate-45\"], [1, \"text-4xl\", \"mb-4\", \"animate-soft-pulse\"], [1, \"font-playfair\", \"text-xl\", \"font-semibold\", \"text-gray-700\", \"mb-4\"], [1, \"font-poppins\", \"text-gray-600\", \"leading-relaxed\", \"font-light\"]],\n      template: function MemoriesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"img\", 2)(3, \"div\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"h1\", 6);\n          i0.ɵɵtext(7, \" Our Precious Memories \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\", 7);\n          i0.ɵɵtext(9, \" Every moment we've shared is a treasure in my heart \\uD83D\\uDCF8 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(10, \"div\", 8);\n          i0.ɵɵrepeaterCreate(11, MemoriesComponent_For_12_Template, 12, 5, \"div\", 9, _forTrack0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"div\", 10)(14, \"h2\", 11);\n          i0.ɵɵtext(15, \" 30 Days of Love \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"p\", 12);\n          i0.ɵɵtext(17, \" A memory for each day we've been together. Click on the unlocked days to see our special moments! \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"div\", 13);\n          i0.ɵɵrepeaterCreate(19, MemoriesComponent_For_20_Template, 5, 6, \"div\", 14, _forTrack0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 10)(22, \"h2\", 11);\n          i0.ɵɵtext(23, \" My Promises to You \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"p\", 12);\n          i0.ɵɵtext(25, \" These are the promises I make to you, today and always \\uD83D\\uDC95 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"div\", 15);\n          i0.ɵɵrepeaterCreate(27, MemoriesComponent_For_28_Template, 7, 2, \"div\", 16, _forTrack0);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵrepeater(ctx.photoGallery);\n          i0.ɵɵadvance(8);\n          i0.ɵɵrepeater(ctx.memoryJar);\n          i0.ɵɵadvance(8);\n          i0.ɵɵrepeater(ctx.promises);\n        }\n      },\n      dependencies: [CommonModule, i1.NgIf],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "photo_r1", "image", "ɵɵsanitizeUrl", "caption", "ɵɵtextInterpolate", "emoji", "description", "day_r3", "message", "ɵɵlistener", "MemoriesComponent_For_20_Template_div_click_0_listener", "ɵɵrestoreView", "_r2", "$implicit", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "to<PERSON><PERSON><PERSON><PERSON>", "id", "ɵɵtemplate", "MemoriesComponent_For_20_div_4_Template", "ɵɵclassMap", "unlocked", "ɵɵtextInterpolate1", "day", "showMessage", "promise_r5", "title", "text", "MemoriesComponent", "constructor", "photoGallery", "memoryJar", "Array", "from", "length", "_", "i", "promises", "dayId", "find", "d", "for<PERSON>ach", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "MemoriesComponent_Template", "rf", "ctx", "ɵɵrepeaterCreate", "MemoriesComponent_For_12_Template", "_forTrack0", "MemoriesComponent_For_20_Template", "MemoriesComponent_For_28_Template", "ɵɵrepeater", "i1", "NgIf", "encapsulation"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\1st-Monthsary-Tangi\\monthsary-website\\src\\app\\pages\\memories\\memories.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n@Component({\n  selector: 'app-memories',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <section class=\"py-20 px-6 sm:px-8 lg:px-12 min-h-screen relative overflow-hidden\">\n      <!-- Beautiful Background -->\n      <div class=\"absolute inset-0 z-0\">\n        <img\n          src=\"https://images.unsplash.com/photo-1529333166437-7750a6dd5a70?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2025&q=80\"\n          alt=\"Romantic memories background\"\n          class=\"w-full h-full object-cover opacity-8\"\n        />\n        <div class=\"absolute inset-0 bg-gradient-to-br from-pink-50/95 to-purple-50/95\"></div>\n      </div>\n\n      <div class=\"max-w-6xl mx-auto relative z-10\">\n        <div class=\"text-center mb-20\">\n          <h1 class=\"font-playfair text-4xl sm:text-5xl lg:text-6xl text-romantic mb-6\">\n            Our Precious Memories\n          </h1>\n          <p class=\"font-poppins text-xl text-gray-600 max-w-2xl mx-auto font-light\">\n            Every moment we've shared is a treasure in my heart 📸\n          </p>\n        </div>\n\n        <!-- Photo Gallery -->\n        <div class=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mb-20\">\n          @for (photo of photoGallery; track photo.id) {\n            <div class=\"glass-effect rounded-2xl overflow-hidden card-hover border border-white/20 group\">\n              <!-- Beautiful Image -->\n              <div class=\"relative h-48 overflow-hidden\">\n                <img\n                  [src]=\"photo.image\"\n                  [alt]=\"photo.caption\"\n                  class=\"w-full h-full object-cover group-hover:scale-110 transition-transform duration-700\"\n                />\n                <div class=\"absolute inset-0 bg-gradient-to-t from-black/40 to-transparent\"></div>\n                <div class=\"absolute top-4 left-4\">\n                  <span class=\"text-3xl animate-soft-pulse drop-shadow-lg\">{{ photo.emoji }}</span>\n                </div>\n              </div>\n\n              <!-- Content -->\n              <div class=\"p-6 text-center\">\n                <h3 class=\"font-playfair text-xl font-semibold text-gray-700 mb-3\">{{ photo.caption }}</h3>\n                <p class=\"font-poppins text-gray-600 text-sm font-light\">{{ photo.description }}</p>\n              </div>\n            </div>\n          }\n        </div>\n\n        <!-- Memory Jar -->\n        <div class=\"text-center mb-16\">\n          <h2 class=\"font-playfair text-3xl sm:text-4xl text-romantic mb-6\">\n            30 Days of Love\n          </h2>\n          <p class=\"font-poppins text-lg text-gray-600 max-w-2xl mx-auto font-light mb-12\">\n            A memory for each day we've been together. Click on the unlocked days to see our special moments!\n          </p>\n        </div>\n\n        <div class=\"grid grid-cols-5 sm:grid-cols-6 lg:grid-cols-10 gap-4 mb-20\">\n          @for (day of memoryJar; track day.id) {\n            <div \n              class=\"relative aspect-square\"\n              (click)=\"toggleMemory(day.id)\">\n              <div \n                class=\"w-full h-full rounded-xl flex items-center justify-center text-lg font-semibold cursor-pointer transition-all duration-300 border-2\"\n                [class]=\"day.unlocked ? 'bg-gradient-to-br from-pink-200 to-purple-200 border-pink-300 hover:scale-110 card-hover' : 'bg-gray-100 border-gray-200 cursor-not-allowed'\">\n                <span [class]=\"day.unlocked ? 'text-gray-700' : 'text-gray-400'\">\n                  {{ day.unlocked ? day.day : '🔒' }}\n                </span>\n              </div>\n              \n              <!-- Memory Message -->\n              <div \n                *ngIf=\"day.showMessage && day.unlocked\"\n                class=\"absolute top-full left-1/2 transform -translate-x-1/2 mt-2 bg-white rounded-lg p-4 shadow-lg border border-pink-200 z-10 w-64 text-center\">\n                <p class=\"font-poppins text-sm text-gray-700 font-light\">{{ day.message }}</p>\n                <div class=\"absolute -top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-white border-l border-t border-pink-200 rotate-45\"></div>\n              </div>\n            </div>\n          }\n        </div>\n\n        <!-- Promises Section -->\n        <div class=\"text-center mb-16\">\n          <h2 class=\"font-playfair text-3xl sm:text-4xl text-romantic mb-6\">\n            My Promises to You\n          </h2>\n          <p class=\"font-poppins text-lg text-gray-600 max-w-2xl mx-auto font-light mb-12\">\n            These are the promises I make to you, today and always 💕\n          </p>\n        </div>\n\n        <div class=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8\">\n          @for (promise of promises; track promise.id) {\n            <div class=\"glass-effect rounded-2xl p-8 text-center card-hover border border-white/20\">\n              <div class=\"text-4xl mb-4 animate-soft-pulse\">💝</div>\n              <h3 class=\"font-playfair text-xl font-semibold text-gray-700 mb-4\">{{ promise.title }}</h3>\n              <p class=\"font-poppins text-gray-600 leading-relaxed font-light\">{{ promise.text }}</p>\n            </div>\n          }\n        </div>\n      </div>\n    </section>\n  `,\n  styles: []\n})\nexport class MemoriesComponent {\n  photoGallery = [\n    {\n      id: 1,\n      emoji: '📸',\n      caption: 'Our first selfie together',\n      description: 'The moment we decided to capture our happiness',\n      image: 'https://images.unsplash.com/photo-1516589178581-6cd7833ae3b2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80'\n    },\n    {\n      id: 2,\n      emoji: '🌅',\n      caption: 'Watching the sunrise',\n      description: 'Thinking of you as the day begins',\n      image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80'\n    },\n    {\n      id: 3,\n      emoji: '🍕',\n      caption: 'Our first virtual dinner date',\n      description: 'Distance couldn\\'t stop us from sharing a meal',\n      image: 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80'\n    },\n    {\n      id: 4,\n      emoji: '🎮',\n      caption: 'Gaming together until 3 AM',\n      description: 'Time flies when we\\'re having fun',\n      image: 'https://images.unsplash.com/photo-1511512578047-dfb367046420?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80'\n    },\n    {\n      id: 5,\n      emoji: '💌',\n      caption: 'The love letter that started it all',\n      description: 'Words that touched my heart forever',\n      image: 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80'\n    },\n    {\n      id: 6,\n      emoji: '🌙',\n      caption: 'Good night texts every single day',\n      description: 'Never missing a chance to say sweet dreams',\n      image: 'https://images.unsplash.com/photo-1494972308805-463bc619d34e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80'\n    }\n  ];\n\n  memoryJar = Array.from({ length: 30 }, (_, i) => ({\n    id: i + 1,\n    day: i + 1,\n    unlocked: i < 10, // First 10 days unlocked for demo\n    showMessage: false,\n    message: `Day ${i + 1}: Another beautiful day of loving you! 💖`\n  }));\n\n  promises = [\n    {\n      id: 1,\n      title: 'Always Listen',\n      text: 'I promise to always listen to you with my heart, not just my ears.'\n    },\n    {\n      id: 2,\n      title: 'Support Your Dreams',\n      text: 'I will always be your biggest cheerleader and support your goals.'\n    },\n    {\n      id: 3,\n      title: 'Make You Laugh',\n      text: 'I promise to find new ways to make you smile every single day.'\n    },\n    {\n      id: 4,\n      title: 'Be Patient',\n      text: 'I will be patient with you, especially when you are not patient with yourself.'\n    },\n    {\n      id: 5,\n      title: 'Love You More',\n      text: 'I promise to love you more today than yesterday, but less than tomorrow.'\n    },\n    {\n      id: 6,\n      title: 'Never Stop Trying',\n      text: 'I will never stop trying to be the best boyfriend you deserve.'\n    }\n  ];\n\n  toggleMemory(dayId: number) {\n    const day = this.memoryJar.find(d => d.id === dayId);\n    if (day && day.unlocked) {\n      day.showMessage = !day.showMessage;\n      // Hide other messages\n      this.memoryJar.forEach(d => {\n        if (d.id !== dayId) d.showMessage = false;\n      });\n    }\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;;;;;IAiChCC,EAFF,CAAAC,cAAA,aAA8F,cAEjD;IAMzCD,EALA,CAAAE,SAAA,cAIE,cACgF;IAEhFF,EADF,CAAAC,cAAA,cAAmC,eACwB;IAAAD,EAAA,CAAAG,MAAA,GAAiB;IAE9EH,EAF8E,CAAAI,YAAA,EAAO,EAC7E,EACF;IAIJJ,EADF,CAAAC,cAAA,cAA6B,aACwC;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC3FJ,EAAA,CAAAC,cAAA,aAAyD;IAAAD,EAAA,CAAAG,MAAA,IAAuB;IAEpFH,EAFoF,CAAAI,YAAA,EAAI,EAChF,EACF;;;;IAfAJ,EAAA,CAAAK,SAAA,GAAmB;IACnBL,EADA,CAAAM,UAAA,QAAAC,QAAA,CAAAC,KAAA,EAAAR,EAAA,CAAAS,aAAA,CAAmB,QAAAF,QAAA,CAAAG,OAAA,CACE;IAKoCV,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAW,iBAAA,CAAAJ,QAAA,CAAAK,KAAA,CAAiB;IAMTZ,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAW,iBAAA,CAAAJ,QAAA,CAAAG,OAAA,CAAmB;IAC7BV,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAAW,iBAAA,CAAAJ,QAAA,CAAAM,WAAA,CAAuB;;;;;IAiChFb,EAHF,CAAAC,cAAA,cAEoJ,YACzF;IAAAD,EAAA,CAAAG,MAAA,GAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAC9EJ,EAAA,CAAAE,SAAA,cAAoI;IACtIF,EAAA,CAAAI,YAAA,EAAM;;;;IAFqDJ,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAW,iBAAA,CAAAG,MAAA,CAAAC,OAAA,CAAiB;;;;;;IAf9Ef,EAAA,CAAAC,cAAA,cAEiC;IAA/BD,EAAA,CAAAgB,UAAA,mBAAAC,uDAAA;MAAA,MAAAH,MAAA,GAAAd,EAAA,CAAAkB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAAG,YAAA,CAAAV,MAAA,CAAAW,EAAA,CAAoB;IAAA,EAAC;IAI5BzB,EAHF,CAAAC,cAAA,cAEyK,WACtG;IAC/DD,EAAA,CAAAG,MAAA,GACF;IACFH,EADE,CAAAI,YAAA,EAAO,EACH;IAGNJ,EAAA,CAAA0B,UAAA,IAAAC,uCAAA,kBAEoJ;IAItJ3B,EAAA,CAAAI,YAAA,EAAM;;;;IAbFJ,EAAA,CAAAK,SAAA,EAAsK;IAAtKL,EAAA,CAAA4B,UAAA,CAAAd,MAAA,CAAAe,QAAA,iJAAsK;IAChK7B,EAAA,CAAAK,SAAA,EAA0D;IAA1DL,EAAA,CAAA4B,UAAA,CAAAd,MAAA,CAAAe,QAAA,qCAA0D;IAC9D7B,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAA8B,kBAAA,MAAAhB,MAAA,CAAAe,QAAA,GAAAf,MAAA,CAAAiB,GAAA,uBACF;IAKC/B,EAAA,CAAAK,SAAA,EAAqC;IAArCL,EAAA,CAAAM,UAAA,SAAAQ,MAAA,CAAAkB,WAAA,IAAAlB,MAAA,CAAAe,QAAA,CAAqC;;;;;IAsBxC7B,EADF,CAAAC,cAAA,cAAwF,cACxC;IAAAD,EAAA,CAAAG,MAAA,mBAAE;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACtDJ,EAAA,CAAAC,cAAA,aAAmE;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC3FJ,EAAA,CAAAC,cAAA,YAAiE;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IACrFH,EADqF,CAAAI,YAAA,EAAI,EACnF;;;;IAF+DJ,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAW,iBAAA,CAAAsB,UAAA,CAAAC,KAAA,CAAmB;IACrBlC,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAW,iBAAA,CAAAsB,UAAA,CAAAE,IAAA,CAAkB;;;AASjG,OAAM,MAAOC,iBAAiB;EA9G9BC,YAAA;IA+GE,KAAAC,YAAY,GAAG,CACb;MACEb,EAAE,EAAE,CAAC;MACLb,KAAK,EAAE,IAAI;MACXF,OAAO,EAAE,2BAA2B;MACpCG,WAAW,EAAE,gDAAgD;MAC7DL,KAAK,EAAE;KACR,EACD;MACEiB,EAAE,EAAE,CAAC;MACLb,KAAK,EAAE,IAAI;MACXF,OAAO,EAAE,sBAAsB;MAC/BG,WAAW,EAAE,mCAAmC;MAChDL,KAAK,EAAE;KACR,EACD;MACEiB,EAAE,EAAE,CAAC;MACLb,KAAK,EAAE,IAAI;MACXF,OAAO,EAAE,+BAA+B;MACxCG,WAAW,EAAE,gDAAgD;MAC7DL,KAAK,EAAE;KACR,EACD;MACEiB,EAAE,EAAE,CAAC;MACLb,KAAK,EAAE,IAAI;MACXF,OAAO,EAAE,4BAA4B;MACrCG,WAAW,EAAE,mCAAmC;MAChDL,KAAK,EAAE;KACR,EACD;MACEiB,EAAE,EAAE,CAAC;MACLb,KAAK,EAAE,IAAI;MACXF,OAAO,EAAE,qCAAqC;MAC9CG,WAAW,EAAE,qCAAqC;MAClDL,KAAK,EAAE;KACR,EACD;MACEiB,EAAE,EAAE,CAAC;MACLb,KAAK,EAAE,IAAI;MACXF,OAAO,EAAE,mCAAmC;MAC5CG,WAAW,EAAE,4CAA4C;MACzDL,KAAK,EAAE;KACR,CACF;IAED,KAAA+B,SAAS,GAAGC,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAE,EAAE,CAACC,CAAC,EAAEC,CAAC,MAAM;MAChDnB,EAAE,EAAEmB,CAAC,GAAG,CAAC;MACTb,GAAG,EAAEa,CAAC,GAAG,CAAC;MACVf,QAAQ,EAAEe,CAAC,GAAG,EAAE;MAAE;MAClBZ,WAAW,EAAE,KAAK;MAClBjB,OAAO,EAAE,OAAO6B,CAAC,GAAG,CAAC;KACtB,CAAC,CAAC;IAEH,KAAAC,QAAQ,GAAG,CACT;MACEpB,EAAE,EAAE,CAAC;MACLS,KAAK,EAAE,eAAe;MACtBC,IAAI,EAAE;KACP,EACD;MACEV,EAAE,EAAE,CAAC;MACLS,KAAK,EAAE,qBAAqB;MAC5BC,IAAI,EAAE;KACP,EACD;MACEV,EAAE,EAAE,CAAC;MACLS,KAAK,EAAE,gBAAgB;MACvBC,IAAI,EAAE;KACP,EACD;MACEV,EAAE,EAAE,CAAC;MACLS,KAAK,EAAE,YAAY;MACnBC,IAAI,EAAE;KACP,EACD;MACEV,EAAE,EAAE,CAAC;MACLS,KAAK,EAAE,eAAe;MACtBC,IAAI,EAAE;KACP,EACD;MACEV,EAAE,EAAE,CAAC;MACLS,KAAK,EAAE,mBAAmB;MAC1BC,IAAI,EAAE;KACP,CACF;;EAEDX,YAAYA,CAACsB,KAAa;IACxB,MAAMf,GAAG,GAAG,IAAI,CAACQ,SAAS,CAACQ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvB,EAAE,KAAKqB,KAAK,CAAC;IACpD,IAAIf,GAAG,IAAIA,GAAG,CAACF,QAAQ,EAAE;MACvBE,GAAG,CAACC,WAAW,GAAG,CAACD,GAAG,CAACC,WAAW;MAClC;MACA,IAAI,CAACO,SAAS,CAACU,OAAO,CAACD,CAAC,IAAG;QACzB,IAAIA,CAAC,CAACvB,EAAE,KAAKqB,KAAK,EAAEE,CAAC,CAAChB,WAAW,GAAG,KAAK;MAC3C,CAAC,CAAC;IACJ;EACF;;;uCAhGWI,iBAAiB;IAAA;EAAA;;;YAAjBA,iBAAiB;MAAAc,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAApD,EAAA,CAAAqD,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAvGxB3D,EAFF,CAAAC,cAAA,iBAAmF,aAE/C;UAMhCD,EALA,CAAAE,SAAA,aAIE,aACoF;UACxFF,EAAA,CAAAI,YAAA,EAAM;UAIFJ,EAFJ,CAAAC,cAAA,aAA6C,aACZ,YACiD;UAC5ED,EAAA,CAAAG,MAAA,8BACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,WAA2E;UACzED,EAAA,CAAAG,MAAA,yEACF;UACFH,EADE,CAAAI,YAAA,EAAI,EACA;UAGNJ,EAAA,CAAAC,cAAA,cAAwE;UACtED,EAAA,CAAA6D,gBAAA,KAAAC,iCAAA,mBAAAC,UAAA,CAqBC;UACH/D,EAAA,CAAAI,YAAA,EAAM;UAIJJ,EADF,CAAAC,cAAA,eAA+B,cACqC;UAChED,EAAA,CAAAG,MAAA,yBACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,aAAiF;UAC/ED,EAAA,CAAAG,MAAA,2GACF;UACFH,EADE,CAAAI,YAAA,EAAI,EACA;UAENJ,EAAA,CAAAC,cAAA,eAAyE;UACvED,EAAA,CAAA6D,gBAAA,KAAAG,iCAAA,mBAAAD,UAAA,CAoBC;UACH/D,EAAA,CAAAI,YAAA,EAAM;UAIJJ,EADF,CAAAC,cAAA,eAA+B,cACqC;UAChED,EAAA,CAAAG,MAAA,4BACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,aAAiF;UAC/ED,EAAA,CAAAG,MAAA,6EACF;UACFH,EADE,CAAAI,YAAA,EAAI,EACA;UAENJ,EAAA,CAAAC,cAAA,eAAkE;UAChED,EAAA,CAAA6D,gBAAA,KAAAI,iCAAA,mBAAAF,UAAA,CAMC;UAGP/D,EAFI,CAAAI,YAAA,EAAM,EACF,EACE;;;UA9EJJ,EAAA,CAAAK,SAAA,IAqBC;UArBDL,EAAA,CAAAkE,UAAA,CAAAN,GAAA,CAAAtB,YAAA,CAqBC;UAcDtC,EAAA,CAAAK,SAAA,GAoBC;UApBDL,EAAA,CAAAkE,UAAA,CAAAN,GAAA,CAAArB,SAAA,CAoBC;UAcDvC,EAAA,CAAAK,SAAA,GAMC;UANDL,EAAA,CAAAkE,UAAA,CAAAN,GAAA,CAAAf,QAAA,CAMC;;;qBApGC9C,YAAY,EAAAoE,EAAA,CAAAC,IAAA;MAAAC,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}