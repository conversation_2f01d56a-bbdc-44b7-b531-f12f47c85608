/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { invalidExpression, invalidTransitionAlias } from '../error_helpers';
export const ANY_STATE = '*';
export function parseTransitionExpr(transitionValue, errors) {
    const expressions = [];
    if (typeof transitionValue == 'string') {
        transitionValue
            .split(/\s*,\s*/)
            .forEach((str) => parseInnerTransitionStr(str, expressions, errors));
    }
    else {
        expressions.push(transitionValue);
    }
    return expressions;
}
function parseInnerTransitionStr(eventStr, expressions, errors) {
    if (eventStr[0] == ':') {
        const result = parseAnimationAlias(eventStr, errors);
        if (typeof result == 'function') {
            expressions.push(result);
            return;
        }
        eventStr = result;
    }
    const match = eventStr.match(/^(\*|[-\w]+)\s*(<?[=-]>)\s*(\*|[-\w]+)$/);
    if (match == null || match.length < 4) {
        errors.push(invalidExpression(eventStr));
        return expressions;
    }
    const fromState = match[1];
    const separator = match[2];
    const toState = match[3];
    expressions.push(makeLambdaFromStates(fromState, toState));
    const isFullAnyStateExpr = fromState == ANY_STATE && toState == ANY_STATE;
    if (separator[0] == '<' && !isFullAnyStateExpr) {
        expressions.push(makeLambdaFromStates(toState, fromState));
    }
    return;
}
function parseAnimationAlias(alias, errors) {
    switch (alias) {
        case ':enter':
            return 'void => *';
        case ':leave':
            return '* => void';
        case ':increment':
            return (fromState, toState) => parseFloat(toState) > parseFloat(fromState);
        case ':decrement':
            return (fromState, toState) => parseFloat(toState) < parseFloat(fromState);
        default:
            errors.push(invalidTransitionAlias(alias));
            return '* => *';
    }
}
// DO NOT REFACTOR ... keep the follow set instantiations
// with the values intact (closure compiler for some reason
// removes follow-up lines that add the values outside of
// the constructor...
const TRUE_BOOLEAN_VALUES = new Set(['true', '1']);
const FALSE_BOOLEAN_VALUES = new Set(['false', '0']);
function makeLambdaFromStates(lhs, rhs) {
    const LHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(lhs) || FALSE_BOOLEAN_VALUES.has(lhs);
    const RHS_MATCH_BOOLEAN = TRUE_BOOLEAN_VALUES.has(rhs) || FALSE_BOOLEAN_VALUES.has(rhs);
    return (fromState, toState) => {
        let lhsMatch = lhs == ANY_STATE || lhs == fromState;
        let rhsMatch = rhs == ANY_STATE || rhs == toState;
        if (!lhsMatch && LHS_MATCH_BOOLEAN && typeof fromState === 'boolean') {
            lhsMatch = fromState ? TRUE_BOOLEAN_VALUES.has(lhs) : FALSE_BOOLEAN_VALUES.has(lhs);
        }
        if (!rhsMatch && RHS_MATCH_BOOLEAN && typeof toState === 'boolean') {
            rhsMatch = toState ? TRUE_BOOLEAN_VALUES.has(rhs) : FALSE_BOOLEAN_VALUES.has(rhs);
        }
        return lhsMatch && rhsMatch;
    };
}
//# sourceMappingURL=data:application/json;base64,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