{"ast": null, "code": "import { Subscription } from '../Subscription';\nexport const animationFrameProvider = {\n  schedule(callback) {\n    let request = requestAnimationFrame;\n    let cancel = cancelAnimationFrame;\n    const {\n      delegate\n    } = animationFrameProvider;\n    if (delegate) {\n      request = delegate.requestAnimationFrame;\n      cancel = delegate.cancelAnimationFrame;\n    }\n    const handle = request(timestamp => {\n      cancel = undefined;\n      callback(timestamp);\n    });\n    return new Subscription(() => cancel === null || cancel === void 0 ? void 0 : cancel(handle));\n  },\n  requestAnimationFrame(...args) {\n    const {\n      delegate\n    } = animationFrameProvider;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.requestAnimationFrame) || requestAnimationFrame)(...args);\n  },\n  cancelAnimationFrame(...args) {\n    const {\n      delegate\n    } = animationFrameProvider;\n    return ((delegate === null || delegate === void 0 ? void 0 : delegate.cancelAnimationFrame) || cancelAnimationFrame)(...args);\n  },\n  delegate: undefined\n};", "map": {"version": 3, "names": ["Subscription", "animationFrameProvider", "schedule", "callback", "request", "requestAnimationFrame", "cancel", "cancelAnimationFrame", "delegate", "handle", "timestamp", "undefined", "args"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/1st-Month<PERSON><PERSON>-<PERSON><PERSON>/monthsary-website/node_modules/rxjs/dist/esm/internal/scheduler/animationFrameProvider.js"], "sourcesContent": ["import { Subscription } from '../Subscription';\nexport const animationFrameProvider = {\n    schedule(callback) {\n        let request = requestAnimationFrame;\n        let cancel = cancelAnimationFrame;\n        const { delegate } = animationFrameProvider;\n        if (delegate) {\n            request = delegate.requestAnimationFrame;\n            cancel = delegate.cancelAnimationFrame;\n        }\n        const handle = request((timestamp) => {\n            cancel = undefined;\n            callback(timestamp);\n        });\n        return new Subscription(() => cancel === null || cancel === void 0 ? void 0 : cancel(handle));\n    },\n    requestAnimationFrame(...args) {\n        const { delegate } = animationFrameProvider;\n        return ((delegate === null || delegate === void 0 ? void 0 : delegate.requestAnimationFrame) || requestAnimationFrame)(...args);\n    },\n    cancelAnimationFrame(...args) {\n        const { delegate } = animationFrameProvider;\n        return ((delegate === null || delegate === void 0 ? void 0 : delegate.cancelAnimationFrame) || cancelAnimationFrame)(...args);\n    },\n    delegate: undefined,\n};\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,OAAO,MAAMC,sBAAsB,GAAG;EAClCC,QAAQA,CAACC,QAAQ,EAAE;IACf,IAAIC,OAAO,GAAGC,qBAAqB;IACnC,IAAIC,MAAM,GAAGC,oBAAoB;IACjC,MAAM;MAAEC;IAAS,CAAC,GAAGP,sBAAsB;IAC3C,IAAIO,QAAQ,EAAE;MACVJ,OAAO,GAAGI,QAAQ,CAACH,qBAAqB;MACxCC,MAAM,GAAGE,QAAQ,CAACD,oBAAoB;IAC1C;IACA,MAAME,MAAM,GAAGL,OAAO,CAAEM,SAAS,IAAK;MAClCJ,MAAM,GAAGK,SAAS;MAClBR,QAAQ,CAACO,SAAS,CAAC;IACvB,CAAC,CAAC;IACF,OAAO,IAAIV,YAAY,CAAC,MAAMM,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACG,MAAM,CAAC,CAAC;EACjG,CAAC;EACDJ,qBAAqBA,CAAC,GAAGO,IAAI,EAAE;IAC3B,MAAM;MAAEJ;IAAS,CAAC,GAAGP,sBAAsB;IAC3C,OAAO,CAAC,CAACO,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACH,qBAAqB,KAAKA,qBAAqB,EAAE,GAAGO,IAAI,CAAC;EACnI,CAAC;EACDL,oBAAoBA,CAAC,GAAGK,IAAI,EAAE;IAC1B,MAAM;MAAEJ;IAAS,CAAC,GAAGP,sBAAsB;IAC3C,OAAO,CAAC,CAACO,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACD,oBAAoB,KAAKA,oBAAoB,EAAE,GAAGK,IAAI,CAAC;EACjI,CAAC;EACDJ,QAAQ,EAAEG;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}