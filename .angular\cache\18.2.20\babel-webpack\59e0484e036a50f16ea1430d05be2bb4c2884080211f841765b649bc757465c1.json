{"ast": null, "code": "/**\n * @license Angular v18.2.13\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nconst Attribute = {\n  /**\n   * The jsaction attribute defines a mapping of a DOM event to a\n   * generic event (aka jsaction), to which the actual event handlers\n   * that implement the behavior of the application are bound. The\n   * value is a semicolon separated list of colon separated pairs of\n   * an optional DOM event name and a jsaction name. If the optional\n   * DOM event name is omitted, 'click' is assumed. The jsaction names\n   * are dot separated pairs of a namespace and a simple jsaction\n   * name.\n   *\n   * See grammar in README.md for expected syntax in the attribute value.\n   */\n  JSACTION: 'jsaction'\n};\n\n/** All properties that are used by jsaction. */\nconst Property = {\n  /**\n   * The parsed value of the jsaction attribute is stored in this\n   * property on the DOM node. The parsed value is an Object. The\n   * property names of the object are the events; the values are the\n   * names of the actions. This property is attached even on nodes\n   * that don't have a jsaction attribute as an optimization, because\n   * property lookup is faster than attribute access.\n   */\n  JSACTION: '__jsaction',\n  /**\n   * The owner property references an a logical owner for a DOM node. JSAction\n   * will follow this reference instead of parentNode when traversing the DOM\n   * to find jsaction attributes. This allows overlaying a logical structure\n   * over a document where the DOM structure can't reflect that structure.\n   */\n  OWNER: '__owner'\n};\n\n/**\n * Map from jsaction annotation to a parsed map from event name to action name.\n */\nconst parseCache = {};\n/**\n * Reads the jsaction parser cache from the given DOM Element.\n */\nfunction get(element) {\n  return element[Property.JSACTION];\n}\n/**\n * Reads the jsaction parser cache for the given DOM element. If no cache is yet present,\n * creates an empty one.\n */\nfunction getDefaulted(element) {\n  const cache = get(element) ?? {};\n  set(element, cache);\n  return cache;\n}\n/**\n * Writes the jsaction parser cache to the given DOM Element.\n */\nfunction set(element, actionMap) {\n  element[Property.JSACTION] = actionMap;\n}\n/**\n * Looks up the parsed action map from the source jsaction attribute value.\n *\n * @param text Unparsed jsaction attribute value.\n * @return Parsed jsaction attribute value, if already present in the cache.\n */\nfunction getParsed(text) {\n  return parseCache[text];\n}\n/**\n * Inserts the parse result for the given source jsaction value into the cache.\n *\n * @param text Unparsed jsaction attribute value.\n * @param parsed Attribute value parsed into the action map.\n */\nfunction setParsed(text, parsed) {\n  parseCache[text] = parsed;\n}\n/**\n * Clears the jsaction parser cache from the given DOM Element.\n *\n * @param element .\n */\nfunction clear(element) {\n  if (Property.JSACTION in element) {\n    delete element[Property.JSACTION];\n  }\n}\n\n/*\n * Names of events that are special to jsaction. These are not all\n * event types that are legal to use in either HTML or the addEvent()\n * API, but these are the ones that are treated specially. All other\n * DOM events can be used in either addEvent() or in the value of the\n * jsaction attribute. Beware of browser specific events or events\n * that don't bubble though: If they are not mentioned here, then\n * event contract doesn't work around their peculiarities.\n */\nconst EventType = {\n  /**\n   * Mouse middle click, introduced in Chrome 55 and not yet supported on\n   * other browsers.\n   */\n  AUXCLICK: 'auxclick',\n  /**\n   * The change event fired by browsers when the `value` attribute of input,\n   * select, and textarea elements are changed.\n   */\n  CHANGE: 'change',\n  /**\n   * The click event. In addEvent() refers to all click events, in the\n   * jsaction attribute it refers to the unmodified click and Enter/Space\n   * keypress events.  In the latter case, a jsaction click will be triggered,\n   * for accessibility reasons.  See clickmod and clickonly, below.\n   */\n  CLICK: 'click',\n  /**\n   * Specifies the jsaction for a modified click event (i.e. a mouse\n   * click with the modifier key Cmd/Ctrl pressed). This event isn't\n   * separately enabled in addEvent(), because in the DOM, it's just a\n   * click event.\n   */\n  CLICKMOD: 'clickmod',\n  /**\n   * Specifies the jsaction for a click-only event.  Click-only doesn't take\n   * into account the case where an element with focus receives an Enter/Space\n   * keypress.  This event isn't separately enabled in addEvent().\n   */\n  CLICKONLY: 'clickonly',\n  /**\n   * The dblclick event.\n   */\n  DBLCLICK: 'dblclick',\n  /**\n   * Focus doesn't bubble, but you can use it in addEvent() and\n   * jsaction anyway. EventContract does the right thing under the\n   * hood.\n   */\n  FOCUS: 'focus',\n  /**\n   * This event only exists in IE. For addEvent() and jsaction, use\n   * focus instead; EventContract does the right thing even though\n   * focus doesn't bubble.\n   */\n  FOCUSIN: 'focusin',\n  /**\n   * Analog to focus.\n   */\n  BLUR: 'blur',\n  /**\n   * Analog to focusin.\n   */\n  FOCUSOUT: 'focusout',\n  /**\n   * Submit doesn't bubble, so it cannot be used with event\n   * contract. However, the browser helpfully fires a click event on\n   * the submit button of a form (even if the form is not submitted by\n   * a click on the submit button). So you should handle click on the\n   * submit button instead.\n   */\n  SUBMIT: 'submit',\n  /**\n   * The keydown event. In addEvent() and non-click jsaction it represents the\n   * regular DOM keydown event. It represents click actions in non-Gecko\n   * browsers.\n   */\n  KEYDOWN: 'keydown',\n  /**\n   * The keypress event. In addEvent() and non-click jsaction it represents the\n   * regular DOM keypress event. It represents click actions in Gecko browsers.\n   */\n  KEYPRESS: 'keypress',\n  /**\n   * The keyup event. In addEvent() and non-click jsaction it represents the\n   * regular DOM keyup event. It represents click actions in non-Gecko\n   * browsers.\n   */\n  KEYUP: 'keyup',\n  /**\n   * The mouseup event. Can either be used directly or used implicitly to\n   * capture mouseup events. In addEvent(), it represents a regular DOM\n   * mouseup event.\n   */\n  MOUSEUP: 'mouseup',\n  /**\n   * The mousedown event. Can either be used directly or used implicitly to\n   * capture mouseenter events. In addEvent(), it represents a regular DOM\n   * mouseover event.\n   */\n  MOUSEDOWN: 'mousedown',\n  /**\n   * The mouseover event. Can either be used directly or used implicitly to\n   * capture mouseenter events. In addEvent(), it represents a regular DOM\n   * mouseover event.\n   */\n  MOUSEOVER: 'mouseover',\n  /**\n   * The mouseout event. Can either be used directly or used implicitly to\n   * capture mouseover events. In addEvent(), it represents a regular DOM\n   * mouseout event.\n   */\n  MOUSEOUT: 'mouseout',\n  /**\n   * The mouseenter event. Does not bubble and fires individually on each\n   * element being entered within a DOM tree.\n   */\n  MOUSEENTER: 'mouseenter',\n  /**\n   * The mouseleave event. Does not bubble and fires individually on each\n   * element being entered within a DOM tree.\n   */\n  MOUSELEAVE: 'mouseleave',\n  /**\n   * The mousemove event.\n   */\n  MOUSEMOVE: 'mousemove',\n  /**\n   * The pointerup event. Can either be used directly or used implicitly to\n   * capture pointerup events. In addEvent(), it represents a regular DOM\n   * pointerup event.\n   */\n  POINTERUP: 'pointerup',\n  /**\n   * The pointerdown event. Can either be used directly or used implicitly to\n   * capture pointerenter events. In addEvent(), it represents a regular DOM\n   * mouseover event.\n   */\n  POINTERDOWN: 'pointerdown',\n  /**\n   * The pointerover event. Can either be used directly or used implicitly to\n   * capture pointerenter events. In addEvent(), it represents a regular DOM\n   * pointerover event.\n   */\n  POINTEROVER: 'pointerover',\n  /**\n   * The pointerout event. Can either be used directly or used implicitly to\n   * capture pointerover events. In addEvent(), it represents a regular DOM\n   * pointerout event.\n   */\n  POINTEROUT: 'pointerout',\n  /**\n   * The pointerenter event. Does not bubble and fires individually on each\n   * element being entered within a DOM tree.\n   */\n  POINTERENTER: 'pointerenter',\n  /**\n   * The pointerleave event. Does not bubble and fires individually on each\n   * element being entered within a DOM tree.\n   */\n  POINTERLEAVE: 'pointerleave',\n  /**\n   * The pointermove event.\n   */\n  POINTERMOVE: 'pointermove',\n  /**\n   * The pointercancel event.\n   */\n  POINTERCANCEL: 'pointercancel',\n  /**\n   * The gotpointercapture event is fired when\n   * Element.setPointerCapture(pointerId) is called on a mouse input, or\n   * implicitly when a touch input begins.\n   */\n  GOTPOINTERCAPTURE: 'gotpointercapture',\n  /**\n   * The lostpointercapture event is fired when\n   * Element.releasePointerCapture(pointerId) is called, or implicitly after a\n   * touch input ends.\n   */\n  LOSTPOINTERCAPTURE: 'lostpointercapture',\n  /**\n   * The error event. The error event doesn't bubble, but you can use it in\n   * addEvent() and jsaction anyway. EventContract does the right thing under\n   * the hood (except in IE8 which does not use error events).\n   */\n  ERROR: 'error',\n  /**\n   * The load event. The load event doesn't bubble, but you can use it in\n   * addEvent() and jsaction anyway. EventContract does the right thing\n   * under the hood.\n   */\n  LOAD: 'load',\n  /**\n   * The unload event.\n   */\n  UNLOAD: 'unload',\n  /**\n   * The touchstart event. Bubbles, will only ever fire in browsers with\n   * touch support.\n   */\n  TOUCHSTART: 'touchstart',\n  /**\n   * The touchend event. Bubbles, will only ever fire in browsers with\n   * touch support.\n   */\n  TOUCHEND: 'touchend',\n  /**\n   * The touchmove event. Bubbles, will only ever fire in browsers with\n   * touch support.\n   */\n  TOUCHMOVE: 'touchmove',\n  /**\n   * The input event.\n   */\n  INPUT: 'input',\n  /**\n   * The scroll event.\n   */\n  SCROLL: 'scroll',\n  /**\n   * The toggle event. The toggle event doesn't bubble, but you can use it in\n   * addEvent() and jsaction anyway. EventContract does the right thing\n   * under the hood.\n   */\n  TOGGLE: 'toggle',\n  /**\n   * A custom event. The actual custom event type is declared as the 'type'\n   * field in the event details. Supported in Firefox 6+, IE 9+, and all Chrome\n   * versions.\n   *\n   * This is an internal name. Users should use jsaction's fireCustomEvent to\n   * fire custom events instead of relying on this type to create them.\n   */\n  CUSTOM: '_custom'\n};\n/** All event types that do not bubble or capture and need a polyfill. */\nconst MOUSE_SPECIAL_EVENT_TYPES = [EventType.MOUSEENTER, EventType.MOUSELEAVE, 'pointerenter', 'pointerleave'];\n/** All event types that are registered in the bubble phase. */\nconst BUBBLE_EVENT_TYPES = [EventType.CLICK, EventType.DBLCLICK, EventType.FOCUSIN, EventType.FOCUSOUT, EventType.KEYDOWN, EventType.KEYUP, EventType.KEYPRESS, EventType.MOUSEOVER, EventType.MOUSEOUT, EventType.SUBMIT, EventType.TOUCHSTART, EventType.TOUCHEND, EventType.TOUCHMOVE, 'touchcancel', 'auxclick', 'change', 'compositionstart', 'compositionupdate', 'compositionend', 'beforeinput', 'input', 'select', 'copy', 'cut', 'paste', 'mousedown', 'mouseup', 'wheel', 'contextmenu', 'dragover', 'dragenter', 'dragleave', 'drop', 'dragstart', 'dragend', 'pointerdown', 'pointermove', 'pointerup', 'pointercancel', 'pointerover', 'pointerout', 'gotpointercapture', 'lostpointercapture',\n// Video events.\n'ended', 'loadedmetadata',\n// Page visibility events.\n'pagehide', 'pageshow', 'visibilitychange',\n// Content visibility events.\n'beforematch'];\n/** All event types that are registered in the capture phase. */\nconst CAPTURE_EVENT_TYPES = [EventType.FOCUS, EventType.BLUR, EventType.ERROR, EventType.LOAD, EventType.TOGGLE];\n/**\n * Whether or not an event type should be registered in the capture phase.\n * @param eventType\n * @returns bool\n */\nconst isCaptureEventType = eventType => CAPTURE_EVENT_TYPES.indexOf(eventType) >= 0;\n/** All event types that are registered early.  */\nconst EARLY_EVENT_TYPES = BUBBLE_EVENT_TYPES.concat(CAPTURE_EVENT_TYPES);\n/**\n * Whether or not an event type is registered in the early contract.\n */\nconst isEarlyEventType = eventType => EARLY_EVENT_TYPES.indexOf(eventType) >= 0;\n\n/**\n * If on a Macintosh with an extended keyboard, the Enter key located in the\n * numeric pad has a different ASCII code.\n */\nconst MAC_ENTER = 3;\n/** The Enter key. */\nconst ENTER = 13;\n/** The Space key. */\nconst SPACE = 32;\n/** Special keycodes used by jsaction for the generic click action. */\nconst KeyCode = {\n  MAC_ENTER,\n  ENTER,\n  SPACE\n};\n\n/**\n * Gets a browser event type, if it would differ from the JSAction event type.\n */\nfunction getBrowserEventType(eventType) {\n  // Mouseenter and mouseleave events are not handled directly because they\n  // are not available everywhere. In browsers where they are available, they\n  // don't bubble and aren't visible at the container boundary. Instead, we\n  // synthesize the mouseenter and mouseleave events from mouseover and\n  // mouseout events, respectively. Cf. eventcontract.js.\n  if (eventType === EventType.MOUSEENTER) {\n    return EventType.MOUSEOVER;\n  } else if (eventType === EventType.MOUSELEAVE) {\n    return EventType.MOUSEOUT;\n  } else if (eventType === EventType.POINTERENTER) {\n    return EventType.POINTEROVER;\n  } else if (eventType === EventType.POINTERLEAVE) {\n    return EventType.POINTEROUT;\n  }\n  return eventType;\n}\n/**\n * Registers the event handler function with the given DOM element for\n * the given event type.\n *\n * @param element The element.\n * @param eventType The event type.\n * @param handler The handler function to install.\n * @return Information needed to uninstall the event handler eventually.\n */\nfunction addEventListener(element, eventType, handler) {\n  // All event handlers are registered in the bubbling\n  // phase.\n  //\n  // All browsers support focus and blur, but these events only are propagated\n  // in the capture phase. Very legacy browsers do not support focusin or\n  // focusout.\n  //\n  // It would be a bad idea to register all event handlers in the\n  // capture phase because then regular onclick handlers would not be\n  // executed at all on events that trigger a jsaction. That's not\n  // entirely what we want, at least for now.\n  //\n  // Error and load events (i.e. on images) do not bubble so they are also\n  // handled in the capture phase.\n  let capture = false;\n  if (isCaptureEventType(eventType)) {\n    capture = true;\n  }\n  element.addEventListener(eventType, handler, capture);\n  return {\n    eventType,\n    handler,\n    capture\n  };\n}\n/**\n * Removes the event handler for the given event from the element.\n * the given event type.\n *\n * @param element The element.\n * @param info The information needed to deregister the handler, as returned by\n *     addEventListener(), above.\n */\nfunction removeEventListener(element, info) {\n  if (element.removeEventListener) {\n    element.removeEventListener(info.eventType, info.handler, info.capture);\n    // `detachEvent` is an old DOM API.\n    // tslint:disable-next-line:no-any\n  } else if (element.detachEvent) {\n    // `detachEvent` is an old DOM API.\n    // tslint:disable-next-line:no-any\n    element.detachEvent(`on${info.eventType}`, info.handler);\n  }\n}\n/**\n * Cancels propagation of an event.\n * @param e The event to cancel propagation for.\n */\nfunction stopPropagation(e) {\n  e.stopPropagation ? e.stopPropagation() : e.cancelBubble = true;\n}\n/**\n * Prevents the default action of an event.\n * @param e The event to prevent the default action for.\n */\nfunction preventDefault(e) {\n  e.preventDefault ? e.preventDefault() : e.returnValue = false;\n}\n/**\n * Gets the target Element of the event. In Firefox, a text node may appear as\n * the target of the event, in which case we return the parent element of the\n * text node.\n * @param e The event to get the target of.\n * @return The target element.\n */\nfunction getTarget(e) {\n  let el = e.target;\n  // In Firefox, the event may have a text node as its target. We always\n  // want the parent Element the text node belongs to, however.\n  if (!el.getAttribute && el.parentNode) {\n    el = el.parentNode;\n  }\n  return el;\n}\n/**\n * Whether we are on a Mac. Not pulling in useragent just for this.\n */\nlet isMac = typeof navigator !== 'undefined' && /Macintosh/.test(navigator.userAgent);\n/**\n * Determines and returns whether the given event (which is assumed to be a\n * click event) is a middle click.\n * NOTE: There is not a consistent way to identify middle click\n * http://www.unixpapa.com/js/mouse.html\n */\nfunction isMiddleClick(e) {\n  return (\n    // `which` is an old DOM API.\n    // tslint:disable-next-line:no-any\n    e.which === 2 ||\n    // `which` is an old DOM API.\n    // tslint:disable-next-line:no-any\n    e.which == null &&\n    // `button` is an old DOM API.\n    // tslint:disable-next-line:no-any\n    e.button === 4 // middle click for IE\n  );\n}\n/**\n * Determines and returns whether the given event (which is assumed\n * to be a click event) is modified. A middle click is considered a modified\n * click to retain the default browser action, which opens a link in a new tab.\n * @param e The event.\n * @return Whether the given event is modified.\n */\nfunction isModifiedClickEvent(e) {\n  return (\n    // `metaKey` is an old DOM API.\n    // tslint:disable-next-line:no-any\n    isMac && e.metaKey ||\n    // `ctrlKey` is an old DOM API.\n    // tslint:disable-next-line:no-any\n    !isMac && e.ctrlKey || isMiddleClick(e) ||\n    // `shiftKey` is an old DOM API.\n    // tslint:disable-next-line:no-any\n    e.shiftKey\n  );\n}\n/** Whether we are on WebKit (e.g., Chrome). */\nconst isWebKit = typeof navigator !== 'undefined' && !/Opera/.test(navigator.userAgent) && /WebKit/.test(navigator.userAgent);\n/** Whether we are on IE. */\nconst isIe = typeof navigator !== 'undefined' && (/MSIE/.test(navigator.userAgent) || /Trident/.test(navigator.userAgent));\n/** Whether we are on Gecko (e.g., Firefox). */\nconst isGecko = typeof navigator !== 'undefined' && !/Opera|WebKit/.test(navigator.userAgent) && /Gecko/.test(navigator.product);\n/**\n * Determines and returns whether the given element is a valid target for\n * keypress/keydown DOM events that act like regular DOM clicks.\n * @param el The element.\n * @return Whether the given element is a valid action key target.\n */\nfunction isValidActionKeyTarget(el) {\n  if (!('getAttribute' in el)) {\n    return false;\n  }\n  if (isTextControl(el)) {\n    return false;\n  }\n  if (isNativelyActivatable(el)) {\n    return false;\n  }\n  // `isContentEditable` is an old DOM API.\n  // tslint:disable-next-line:no-any\n  if (el.isContentEditable) {\n    return false;\n  }\n  return true;\n}\n/**\n * Whether an event has a modifier key activated.\n * @param e The event.\n * @return True, if a modifier key is activated.\n */\nfunction hasModifierKey(e) {\n  return (\n    // `ctrlKey` is an old DOM API.\n    // tslint:disable-next-line:no-any\n    e.ctrlKey ||\n    // `shiftKey` is an old DOM API.\n    // tslint:disable-next-line:no-any\n    e.shiftKey ||\n    // `altKey` is an old DOM API.\n    // tslint:disable-next-line:no-any\n    e.altKey ||\n    // `metaKey` is an old DOM API.\n    // tslint:disable-next-line:no-any\n    e.metaKey\n  );\n}\n/**\n * Determines and returns whether the given event has a target that already\n * has event handlers attached because it is a native HTML control. Used to\n * determine if preventDefault should be called when isActionKeyEvent is true.\n * @param e The event.\n * @return If preventDefault should be called.\n */\nfunction shouldCallPreventDefaultOnNativeHtmlControl(e) {\n  const el = getTarget(e);\n  const tagName = el.tagName.toUpperCase();\n  const role = (el.getAttribute('role') || '').toUpperCase();\n  if (tagName === 'BUTTON' || role === 'BUTTON') {\n    return true;\n  }\n  if (!isNativeHTMLControl(el)) {\n    return false;\n  }\n  if (tagName === 'A') {\n    return false;\n  }\n  /**\n   * Fix for physical d-pads on feature phone platforms; the native event\n   * (ie. isTrusted: true) needs to fire to show the OPTION list. See\n   * b/135288469 for more info.\n   */\n  if (tagName === 'SELECT') {\n    return false;\n  }\n  if (processSpace(el)) {\n    return false;\n  }\n  if (isTextControl(el)) {\n    return false;\n  }\n  return true;\n}\n/**\n * Determines and returns whether the given event acts like a regular DOM click,\n * and should be handled instead of the click.  If this returns true, the caller\n * will call preventDefault() to prevent a possible duplicate event.\n * This is represented by a keypress (keydown on Gecko browsers) on Enter or\n * Space key.\n * @param e The event.\n * @return True, if the event emulates a DOM click.\n */\nfunction isActionKeyEvent(e) {\n  let key =\n  // `which` is an old DOM API.\n  // tslint:disable-next-line:no-any\n  e.which ||\n  // `keyCode` is an old DOM API.\n  // tslint:disable-next-line:no-any\n  e.keyCode;\n  if (!key && e.key) {\n    key = ACTION_KEY_TO_KEYCODE[e.key];\n  }\n  if (isWebKit && key === KeyCode.MAC_ENTER) {\n    key = KeyCode.ENTER;\n  }\n  if (key !== KeyCode.ENTER && key !== KeyCode.SPACE) {\n    return false;\n  }\n  const el = getTarget(e);\n  if (e.type !== EventType.KEYDOWN || !isValidActionKeyTarget(el) || hasModifierKey(e)) {\n    return false;\n  }\n  // For <input type=\"checkbox\">, we must only handle the browser's native click\n  // event, so that the browser can toggle the checkbox.\n  if (processSpace(el) && key === KeyCode.SPACE) {\n    return false;\n  }\n  // If this element is non-focusable, ignore stray keystrokes (b/18337209)\n  // Sscreen readers can move without tab focus, so any tabIndex is focusable.\n  // See B/21809604\n  if (!isFocusable(el)) {\n    return false;\n  }\n  const type = (el.getAttribute('role') || el.type || el.tagName).toUpperCase();\n  const isSpecificTriggerKey = IDENTIFIER_TO_KEY_TRIGGER_MAPPING[type] % key === 0;\n  const isDefaultTriggerKey = !(type in IDENTIFIER_TO_KEY_TRIGGER_MAPPING) && key === KeyCode.ENTER;\n  const hasType = el.tagName.toUpperCase() !== 'INPUT' || !!el.type;\n  return (isSpecificTriggerKey || isDefaultTriggerKey) && hasType;\n}\n/**\n * Checks whether a DOM element can receive keyboard focus.\n * This code is based on goog.dom.isFocusable, but simplified since we shouldn't\n * care about visibility if we're already handling a keyboard event.\n */\nfunction isFocusable(el) {\n  return (el.tagName in NATIVELY_FOCUSABLE_ELEMENTS || hasSpecifiedTabIndex(el)) && !el.disabled;\n}\n/**\n * @param element Element to check.\n * @return Whether the element has a specified tab index.\n */\nfunction hasSpecifiedTabIndex(element) {\n  // IE returns 0 for an unset tabIndex, so we must use getAttributeNode(),\n  // which returns an object with a 'specified' property if tabIndex is\n  // specified.  This works on other browsers, too.\n  const attrNode = element.getAttributeNode('tabindex'); // Must be lowercase!\n  return attrNode != null && attrNode.specified;\n}\n/** Element tagnames that are focusable by default. */\nconst NATIVELY_FOCUSABLE_ELEMENTS = {\n  'A': 1,\n  'INPUT': 1,\n  'TEXTAREA': 1,\n  'SELECT': 1,\n  'BUTTON': 1\n};\n/** @return True, if the Space key was pressed. */\nfunction isSpaceKeyEvent(e) {\n  const key =\n  // `which` is an old DOM API.\n  // tslint:disable-next-line:no-any\n  e.which ||\n  // `keyCode` is an old DOM API.\n  // tslint:disable-next-line:no-any\n  e.keyCode;\n  const el = getTarget(e);\n  const elementName = (el.type || el.tagName).toUpperCase();\n  return key === KeyCode.SPACE && elementName !== 'CHECKBOX';\n}\n/**\n * Determines whether the event corresponds to a non-bubbling mouse\n * event type (mouseenter, mouseleave, pointerenter, and pointerleave).\n *\n * During mouseover (mouseenter) and pointerover (pointerenter), the\n * relatedTarget is the element being entered from. During mouseout (mouseleave)\n * and pointerout (pointerleave), the relatedTarget is the element being exited\n * to.\n *\n * In both cases, if relatedTarget is outside target, then the corresponding\n * special event has occurred, otherwise it hasn't.\n *\n * @param e The mouseover/mouseout event.\n * @param type The type of the mouse special event.\n * @param element The element on which the jsaction for the\n *     mouseenter/mouseleave event is defined.\n * @return True if the event is a mouseenter/mouseleave event.\n */\nfunction isMouseSpecialEvent(e, type, element) {\n  // `relatedTarget` is an old DOM API.\n  // tslint:disable-next-line:no-any\n  const related = e.relatedTarget;\n  return (e.type === EventType.MOUSEOVER && type === EventType.MOUSEENTER || e.type === EventType.MOUSEOUT && type === EventType.MOUSELEAVE || e.type === EventType.POINTEROVER && type === EventType.POINTERENTER || e.type === EventType.POINTEROUT && type === EventType.POINTERLEAVE) && (!related || related !== element && !element.contains(related));\n}\n/**\n * Creates a new EventLike object for a mouseenter/mouseleave event that's\n * derived from the original corresponding mouseover/mouseout event.\n * @param e The event.\n * @param target The element on which the jsaction for the mouseenter/mouseleave\n *     event is defined.\n * @return A modified event-like object copied from the event object passed into\n *     this function.\n */\nfunction createMouseSpecialEvent(e, target) {\n  // We have to create a copy of the event object because we need to mutate\n  // its fields. We do this for the special mouse events because the event\n  // target needs to be retargeted to the action element rather than the real\n  // element (since we are simulating the special mouse events with mouseover/\n  // mouseout).\n  //\n  // Since we're making a copy anyways, we might as well attempt to convert\n  // this event into a pseudo-real mouseenter/mouseleave event by adjusting\n  // its type.\n  //\n  // tslint:disable-next-line:no-any\n  const copy = {};\n  for (const property in e) {\n    if (property === 'srcElement' || property === 'target') {\n      continue;\n    }\n    const key = property;\n    // Making a copy requires iterating through all properties of `Event`.\n    // tslint:disable-next-line:no-dict-access-on-struct-type\n    const value = e[key];\n    if (typeof value === 'function') {\n      continue;\n    }\n    // Value should be the expected type, but the value of `key` is not known\n    // statically.\n    // tslint:disable-next-line:no-any\n    copy[key] = value;\n  }\n  if (e.type === EventType.MOUSEOVER) {\n    copy['type'] = EventType.MOUSEENTER;\n  } else if (e.type === EventType.MOUSEOUT) {\n    copy['type'] = EventType.MOUSELEAVE;\n  } else if (e.type === EventType.POINTEROVER) {\n    copy['type'] = EventType.POINTERENTER;\n  } else {\n    copy['type'] = EventType.POINTERLEAVE;\n  }\n  copy['target'] = copy['srcElement'] = target;\n  copy['bubbles'] = false;\n  return copy;\n}\n/**\n * Returns touch data extracted from the touch event: clientX, clientY, screenX\n * and screenY. If the event has no touch information at all, the returned\n * value is null.\n *\n * The fields of this Object are unquoted.\n *\n * @param event A touch event.\n */\nfunction getTouchData(event) {\n  const touch = event.changedTouches && event.changedTouches[0] || event.touches && event.touches[0];\n  if (!touch) {\n    return null;\n  }\n  return {\n    clientX: touch.clientX,\n    clientY: touch.clientY,\n    screenX: touch.screenX,\n    screenY: touch.screenY\n  };\n}\n/**\n * Creates a new EventLike object for a \"click\" event that's derived from the\n * original corresponding \"touchend\" event for a fast-click implementation.\n *\n * It takes a touch event, adds common fields found in a click event and\n * changes the type to 'click', so that the resulting event looks more like\n * a real click event.\n *\n * @param event A touch event.\n * @return A modified event-like object copied from the event object passed into\n *     this function.\n */\nfunction recreateTouchEventAsClick(event) {\n  const click = {};\n  click['originalEventType'] = event.type;\n  click['type'] = EventType.CLICK;\n  for (const property in event) {\n    if (property === 'type' || property === 'srcElement') {\n      continue;\n    }\n    const key = property;\n    // Making a copy requires iterating through all properties of `TouchEvent`.\n    // tslint:disable-next-line:no-dict-access-on-struct-type\n    const value = event[key];\n    if (typeof value === 'function') {\n      continue;\n    }\n    // Value should be the expected type, but the value of `key` is not known\n    // statically.\n    // tslint:disable-next-line:no-any\n    click[key] = value;\n  }\n  // Ensure that the event has the most recent timestamp. This timestamp\n  // may be used in the future to validate or cancel subsequent click events.\n  click['timeStamp'] = Date.now();\n  // Emulate preventDefault and stopPropagation behavior\n  click['defaultPrevented'] = false;\n  click['preventDefault'] = syntheticPreventDefault;\n  click['_propagationStopped'] = false;\n  click['stopPropagation'] = syntheticStopPropagation;\n  // Emulate click coordinates using touch info\n  const touch = getTouchData(event);\n  if (touch) {\n    click['clientX'] = touch.clientX;\n    click['clientY'] = touch.clientY;\n    click['screenX'] = touch.screenX;\n    click['screenY'] = touch.screenY;\n  }\n  return click;\n}\n/**\n * An implementation of \"preventDefault\" for a synthesized event. Simply\n * sets \"defaultPrevented\" property to true.\n */\nfunction syntheticPreventDefault() {\n  this.defaultPrevented = true;\n}\n/**\n * An implementation of \"stopPropagation\" for a synthesized event. It simply\n * sets a synthetic non-standard \"_propagationStopped\" property to true.\n */\nfunction syntheticStopPropagation() {\n  this._propagationStopped = true;\n}\n/**\n * Mapping of KeyboardEvent.key values to\n * KeyCode values.\n */\nconst ACTION_KEY_TO_KEYCODE = {\n  'Enter': KeyCode.ENTER,\n  ' ': KeyCode.SPACE\n};\n/**\n * Mapping of HTML element identifiers (ARIA role, type, or tagName) to the\n * keys (enter and/or space) that should activate them. A value of zero means\n * that both should activate them.\n */\nconst IDENTIFIER_TO_KEY_TRIGGER_MAPPING = {\n  'A': KeyCode.ENTER,\n  'BUTTON': 0,\n  'CHECKBOX': KeyCode.SPACE,\n  'COMBOBOX': KeyCode.ENTER,\n  'FILE': 0,\n  'GRIDCELL': KeyCode.ENTER,\n  'LINK': KeyCode.ENTER,\n  'LISTBOX': KeyCode.ENTER,\n  'MENU': 0,\n  'MENUBAR': 0,\n  'MENUITEM': 0,\n  'MENUITEMCHECKBOX': 0,\n  'MENUITEMRADIO': 0,\n  'OPTION': 0,\n  'RADIO': KeyCode.SPACE,\n  'RADIOGROUP': KeyCode.SPACE,\n  'RESET': 0,\n  'SUBMIT': 0,\n  'SWITCH': KeyCode.SPACE,\n  'TAB': 0,\n  'TREE': KeyCode.ENTER,\n  'TREEITEM': KeyCode.ENTER\n};\n/**\n * Returns whether or not to process space based on the type of the element;\n * checks to make sure that type is not null.\n * @param element The element.\n * @return Whether or not to process space based on type.\n */\nfunction processSpace(element) {\n  const type = (element.getAttribute('type') || element.tagName).toUpperCase();\n  return type in PROCESS_SPACE;\n}\n/**\n * Returns whether or not the given element is a text control.\n * @param el The element.\n * @return Whether or not the given element is a text control.\n */\nfunction isTextControl(el) {\n  const type = (el.getAttribute('type') || el.tagName).toUpperCase();\n  return type in TEXT_CONTROLS;\n}\n/**\n * Returns if the given element is a native HTML control.\n * @param el The element.\n * @return If the given element is a native HTML control.\n */\nfunction isNativeHTMLControl(el) {\n  return el.tagName.toUpperCase() in NATIVE_HTML_CONTROLS;\n}\n/**\n * Returns if the given element is natively activatable. Browsers emit click\n * events for natively activatable elements, even when activated via keyboard.\n * For these elements, we don't need to raise a11y click events.\n * @param el The element.\n * @return If the given element is a native HTML control.\n */\nfunction isNativelyActivatable(el) {\n  return el.tagName.toUpperCase() === 'BUTTON' || !!el.type && el.type.toUpperCase() === 'FILE';\n}\n/**\n * HTML <input> types (not ARIA roles) which will auto-trigger a click event for\n * the Space key, with side-effects. We will not call preventDefault if space is\n * pressed, nor will we raise a11y click events.  For all other elements, we can\n * suppress the default event (which has no desired side-effects) and handle the\n * keydown ourselves.\n */\nconst PROCESS_SPACE = {\n  'CHECKBOX': true,\n  'FILE': true,\n  'OPTION': true,\n  'RADIO': true\n};\n/** TagNames and Input types for which to not process enter/space as click. */\nconst TEXT_CONTROLS = {\n  'COLOR': true,\n  'DATE': true,\n  'DATETIME': true,\n  'DATETIME-LOCAL': true,\n  'EMAIL': true,\n  'MONTH': true,\n  'NUMBER': true,\n  'PASSWORD': true,\n  'RANGE': true,\n  'SEARCH': true,\n  'TEL': true,\n  'TEXT': true,\n  'TEXTAREA': true,\n  'TIME': true,\n  'URL': true,\n  'WEEK': true\n};\n/** TagNames that are native HTML controls. */\nconst NATIVE_HTML_CONTROLS = {\n  'A': true,\n  'AREA': true,\n  'BUTTON': true,\n  'DIALOG': true,\n  'IMG': true,\n  'INPUT': true,\n  'LINK': true,\n  'MENU': true,\n  'OPTGROUP': true,\n  'OPTION': true,\n  'PROGRESS': true,\n  'SELECT': true,\n  'TEXTAREA': true\n};\n/** Exported for testing. */\nconst testing = {\n  setIsMac(value) {\n    isMac = value;\n  }\n};\n\n/**\n * Whether the user agent is running on iOS.\n */\nconst isIos = typeof navigator !== 'undefined' && /iPhone|iPad|iPod/.test(navigator.userAgent);\n/**\n * A class representing a container node and all the event handlers\n * installed on it. Used so that handlers can be cleaned up if the\n * container is removed from the contract.\n */\nclass EventContractContainer {\n  /**\n   * @param element The container Element.\n   */\n  constructor(element) {\n    this.element = element;\n    /**\n     * Array of event handlers and their corresponding event types that are\n     * installed on this container.\n     *\n     */\n    this.handlerInfos = [];\n  }\n  /**\n   * Installs the provided installer on the element owned by this container,\n   * and maintains a reference to resulting handler in order to remove it\n   * later if desired.\n   */\n  addEventListener(eventType, getHandler) {\n    // In iOS, event bubbling doesn't happen automatically in any DOM element,\n    // unless it has an onclick attribute or DOM event handler attached to it.\n    // This breaks JsAction in some cases. See \"Making Elements Clickable\"\n    // section at http://goo.gl/2VoGnB.\n    //\n    // A workaround for this issue is to change the CSS cursor style to 'pointer'\n    // for the container element, which magically turns on event bubbling. This\n    // solution is described in the comments section at http://goo.gl/6pEO1z.\n    //\n    // We use a navigator.userAgent check here as this problem is present both\n    // on Mobile Safari and thin WebKit wrappers, such as Chrome for iOS.\n    if (isIos) {\n      this.element.style.cursor = 'pointer';\n    }\n    this.handlerInfos.push(addEventListener(this.element, eventType, getHandler(this.element)));\n  }\n  /**\n   * Removes all the handlers installed on this container.\n   */\n  cleanUp() {\n    for (let i = 0; i < this.handlerInfos.length; i++) {\n      removeEventListener(this.element, this.handlerInfos[i]);\n    }\n    this.handlerInfos = [];\n  }\n}\nconst Char = {\n  /**\n   * The separator between the namespace and the action name in the\n   * jsaction attribute value.\n   */\n  NAMESPACE_ACTION_SEPARATOR: '.',\n  /**\n   * The separator between the event name and action in the jsaction\n   * attribute value.\n   */\n  EVENT_ACTION_SEPARATOR: ':'\n};\n\n/** Added for readability when accessing stable property names. */\nfunction getEventType(eventInfo) {\n  return eventInfo.eventType;\n}\n/** Added for readability when accessing stable property names. */\nfunction setEventType(eventInfo, eventType) {\n  eventInfo.eventType = eventType;\n}\n/** Added for readability when accessing stable property names. */\nfunction getEvent(eventInfo) {\n  return eventInfo.event;\n}\n/** Added for readability when accessing stable property names. */\nfunction setEvent(eventInfo, event) {\n  eventInfo.event = event;\n}\n/** Added for readability when accessing stable property names. */\nfunction getTargetElement(eventInfo) {\n  return eventInfo.targetElement;\n}\n/** Added for readability when accessing stable property names. */\nfunction setTargetElement(eventInfo, targetElement) {\n  eventInfo.targetElement = targetElement;\n}\n/** Added for readability when accessing stable property names. */\nfunction getContainer(eventInfo) {\n  return eventInfo.eic;\n}\n/** Added for readability when accessing stable property names. */\nfunction setContainer(eventInfo, container) {\n  eventInfo.eic = container;\n}\n/** Added for readability when accessing stable property names. */\nfunction getTimestamp(eventInfo) {\n  return eventInfo.timeStamp;\n}\n/** Added for readability when accessing stable property names. */\nfunction setTimestamp(eventInfo, timestamp) {\n  eventInfo.timeStamp = timestamp;\n}\n/** Added for readability when accessing stable property names. */\nfunction getAction(eventInfo) {\n  return eventInfo.eia;\n}\n/** Added for readability when accessing stable property names. */\nfunction setAction(eventInfo, actionName, actionElement) {\n  eventInfo.eia = [actionName, actionElement];\n}\n/** Added for readability when accessing stable property names. */\nfunction unsetAction(eventInfo) {\n  eventInfo.eia = undefined;\n}\n/** Added for readability when accessing stable property names. */\nfunction getActionName(actionInfo) {\n  return actionInfo[0];\n}\n/** Added for readability when accessing stable property names. */\nfunction getActionElement(actionInfo) {\n  return actionInfo[1];\n}\n/** Added for readability when accessing stable property names. */\nfunction getIsReplay(eventInfo) {\n  return eventInfo.eirp;\n}\n/** Added for readability when accessing stable property names. */\nfunction setIsReplay(eventInfo, replay) {\n  eventInfo.eirp = replay;\n}\n/** Added for readability when accessing stable property names. */\nfunction getA11yClickKey(eventInfo) {\n  return eventInfo.eiack;\n}\n/** Added for readability when accessing stable property names. */\nfunction setA11yClickKey(eventInfo, a11yClickKey) {\n  eventInfo.eiack = a11yClickKey;\n}\n/** Added for readability when accessing stable property names. */\nfunction getResolved(eventInfo) {\n  return eventInfo.eir;\n}\n/** Added for readability when accessing stable property names. */\nfunction setResolved(eventInfo, resolved) {\n  eventInfo.eir = resolved;\n}\n/** Clones an `EventInfo` */\nfunction cloneEventInfo(eventInfo) {\n  return {\n    eventType: eventInfo.eventType,\n    event: eventInfo.event,\n    targetElement: eventInfo.targetElement,\n    eic: eventInfo.eic,\n    eia: eventInfo.eia,\n    timeStamp: eventInfo.timeStamp,\n    eirp: eventInfo.eirp,\n    eiack: eventInfo.eiack,\n    eir: eventInfo.eir\n  };\n}\n/**\n * Utility function for creating an `EventInfo`.\n *\n * This can be used from code-size sensitive compilation units, as taking\n * parameters vs. an `Object` literal reduces code size.\n */\nfunction createEventInfoFromParameters(eventType, event, targetElement, container, timestamp, action, isReplay, a11yClickKey) {\n  return {\n    eventType,\n    event,\n    targetElement,\n    eic: container,\n    timeStamp: timestamp,\n    eia: action,\n    eirp: isReplay,\n    eiack: a11yClickKey\n  };\n}\n/**\n * Utility function for creating an `EventInfo`.\n *\n * This should be used in compilation units that are less sensitive to code\n * size.\n */\nfunction createEventInfo({\n  eventType,\n  event,\n  targetElement,\n  container,\n  timestamp,\n  action,\n  isReplay,\n  a11yClickKey\n}) {\n  return {\n    eventType,\n    event,\n    targetElement,\n    eic: container,\n    timeStamp: timestamp,\n    eia: action ? [action.name, action.element] : undefined,\n    eirp: isReplay,\n    eiack: a11yClickKey\n  };\n}\n/**\n * Utility class around an `EventInfo`.\n *\n * This should be used in compilation units that are less sensitive to code\n * size.\n */\nclass EventInfoWrapper {\n  constructor(eventInfo) {\n    this.eventInfo = eventInfo;\n  }\n  getEventType() {\n    return getEventType(this.eventInfo);\n  }\n  setEventType(eventType) {\n    setEventType(this.eventInfo, eventType);\n  }\n  getEvent() {\n    return getEvent(this.eventInfo);\n  }\n  setEvent(event) {\n    setEvent(this.eventInfo, event);\n  }\n  getTargetElement() {\n    return getTargetElement(this.eventInfo);\n  }\n  setTargetElement(targetElement) {\n    setTargetElement(this.eventInfo, targetElement);\n  }\n  getContainer() {\n    return getContainer(this.eventInfo);\n  }\n  setContainer(container) {\n    setContainer(this.eventInfo, container);\n  }\n  getTimestamp() {\n    return getTimestamp(this.eventInfo);\n  }\n  setTimestamp(timestamp) {\n    setTimestamp(this.eventInfo, timestamp);\n  }\n  getAction() {\n    const action = getAction(this.eventInfo);\n    if (!action) return undefined;\n    return {\n      name: action[0],\n      element: action[1]\n    };\n  }\n  setAction(action) {\n    if (!action) {\n      unsetAction(this.eventInfo);\n      return;\n    }\n    setAction(this.eventInfo, action.name, action.element);\n  }\n  getIsReplay() {\n    return getIsReplay(this.eventInfo);\n  }\n  setIsReplay(replay) {\n    setIsReplay(this.eventInfo, replay);\n  }\n  getResolved() {\n    return getResolved(this.eventInfo);\n  }\n  setResolved(resolved) {\n    setResolved(this.eventInfo, resolved);\n  }\n  clone() {\n    return new EventInfoWrapper(cloneEventInfo(this.eventInfo));\n  }\n}\n\n/**\n * Since maps from event to action are immutable we can use a single map\n * to represent the empty map.\n */\nconst EMPTY_ACTION_MAP = {};\n/**\n * This regular expression matches a semicolon.\n */\nconst REGEXP_SEMICOLON = /\\s*;\\s*/;\n/** If no event type is defined, defaults to `click`. */\nconst DEFAULT_EVENT_TYPE = EventType.CLICK;\n/** Resolves actions for Events. */\nclass ActionResolver {\n  constructor({\n    syntheticMouseEventSupport = false,\n    clickModSupport = true\n  } = {}) {\n    this.a11yClickSupport = false;\n    this.clickModSupport = true;\n    this.updateEventInfoForA11yClick = undefined;\n    this.preventDefaultForA11yClick = undefined;\n    this.populateClickOnlyAction = undefined;\n    this.syntheticMouseEventSupport = syntheticMouseEventSupport;\n    this.clickModSupport = clickModSupport;\n  }\n  resolveEventType(eventInfo) {\n    // We distinguish modified and plain clicks in order to support the\n    // default browser behavior of modified clicks on links; usually to\n    // open the URL of the link in new tab or new window on ctrl/cmd\n    // click. A DOM 'click' event is mapped to the jsaction 'click'\n    // event iff there is no modifier present on the event. If there is\n    // a modifier, it's mapped to 'clickmod' instead.\n    //\n    // It's allowed to omit the event in the jsaction attribute. In that\n    // case, 'click' is assumed. Thus the following two are equivalent:\n    //\n    //   <a href=\"someurl\" jsaction=\"gna.fu\">\n    //   <a href=\"someurl\" jsaction=\"click:gna.fu\">\n    //\n    // For unmodified clicks, EventContract invokes the jsaction\n    // 'gna.fu'. For modified clicks, EventContract won't find a\n    // suitable action and leave the event to be handled by the\n    // browser.\n    //\n    // In order to also invoke a jsaction handler for a modifier click,\n    // 'clickmod' needs to be used:\n    //\n    //   <a href=\"someurl\" jsaction=\"clickmod:gna.fu\">\n    //\n    // EventContract invokes the jsaction 'gna.fu' for modified\n    // clicks. Unmodified clicks are left to the browser.\n    //\n    // In order to set up the event contract to handle both clickonly and\n    // clickmod, only addEvent(EventType.CLICK) is necessary.\n    //\n    // In order to set up the event contract to handle click,\n    // addEvent() is necessary for CLICK, KEYDOWN, and KEYPRESS event types.  If\n    // a11y click support is enabled, addEvent() will set up the appropriate key\n    // event handler automatically.\n    if (this.clickModSupport && getEventType(eventInfo) === EventType.CLICK && isModifiedClickEvent(getEvent(eventInfo))) {\n      setEventType(eventInfo, EventType.CLICKMOD);\n    } else if (this.a11yClickSupport) {\n      this.updateEventInfoForA11yClick(eventInfo);\n    }\n  }\n  resolveAction(eventInfo) {\n    if (getResolved(eventInfo)) {\n      return;\n    }\n    this.populateAction(eventInfo, getTargetElement(eventInfo));\n    setResolved(eventInfo, true);\n  }\n  resolveParentAction(eventInfo) {\n    const action = getAction(eventInfo);\n    const actionElement = action && getActionElement(action);\n    unsetAction(eventInfo);\n    const parentNode = actionElement && this.getParentNode(actionElement);\n    if (!parentNode) {\n      return;\n    }\n    this.populateAction(eventInfo, parentNode);\n  }\n  /**\n   * Searches for a jsaction that the DOM event maps to and creates an\n   * object containing event information used for dispatching by\n   * jsaction.Dispatcher. This method populates the `action` and `actionElement`\n   * fields of the EventInfo object passed in by finding the first\n   * jsaction attribute above the target Node of the event, and below\n   * the container Node, that specifies a jsaction for the event\n   * type. If no such jsaction is found, then action is undefined.\n   *\n   * @param eventInfo `EventInfo` to set `action` and `actionElement` if an\n   *    action is found on any `Element` in the path of the `Event`.\n   */\n  populateAction(eventInfo, currentTarget) {\n    let actionElement = currentTarget;\n    while (actionElement && actionElement !== getContainer(eventInfo)) {\n      if (actionElement.nodeType === Node.ELEMENT_NODE) {\n        this.populateActionOnElement(actionElement, eventInfo);\n      }\n      if (getAction(eventInfo)) {\n        // An event is handled by at most one jsaction. Thus we stop at the\n        // first matching jsaction specified in a jsaction attribute up the\n        // ancestor chain of the event target node.\n        break;\n      }\n      actionElement = this.getParentNode(actionElement);\n    }\n    const action = getAction(eventInfo);\n    if (!action) {\n      // No action found.\n      return;\n    }\n    if (this.a11yClickSupport) {\n      this.preventDefaultForA11yClick(eventInfo);\n    }\n    // We attempt to handle the mouseenter/mouseleave events here by\n    // detecting whether the mouseover/mouseout events correspond to\n    // entering/leaving an element.\n    if (this.syntheticMouseEventSupport) {\n      if (getEventType(eventInfo) === EventType.MOUSEENTER || getEventType(eventInfo) === EventType.MOUSELEAVE || getEventType(eventInfo) === EventType.POINTERENTER || getEventType(eventInfo) === EventType.POINTERLEAVE) {\n        // We attempt to handle the mouseenter/mouseleave events here by\n        // detecting whether the mouseover/mouseout events correspond to\n        // entering/leaving an element.\n        if (isMouseSpecialEvent(getEvent(eventInfo), getEventType(eventInfo), getActionElement(action))) {\n          // If both mouseover/mouseout and mouseenter/mouseleave events are\n          // enabled, two separate handlers for mouseover/mouseout are\n          // registered. Both handlers will see the same event instance\n          // so we create a copy to avoid interfering with the dispatching of\n          // the mouseover/mouseout event.\n          const copiedEvent = createMouseSpecialEvent(getEvent(eventInfo), getActionElement(action));\n          setEvent(eventInfo, copiedEvent);\n          // Since the mouseenter/mouseleave events do not bubble, the target\n          // of the event is technically the `actionElement` (the node with the\n          // `jsaction` attribute)\n          setTargetElement(eventInfo, getActionElement(action));\n        } else {\n          unsetAction(eventInfo);\n        }\n      }\n    }\n  }\n  /**\n   * Walk to the parent node, unless the node has a different owner in\n   * which case we walk to the owner. Attempt to walk to host of a\n   * shadow root if needed.\n   */\n  getParentNode(element) {\n    const owner = element[Property.OWNER];\n    if (owner) {\n      return owner;\n    }\n    const parentNode = element.parentNode;\n    if (parentNode?.nodeName === '#document-fragment') {\n      return parentNode?.host ?? null;\n    }\n    return parentNode;\n  }\n  /**\n   * Accesses the jsaction map on a node and retrieves the name of the\n   * action the given event is mapped to, if any. It parses the\n   * attribute value and stores it in a property on the node for\n   * subsequent retrieval without re-parsing and re-accessing the\n   * attribute.\n   *\n   * @param actionElement The DOM node to retrieve the jsaction map from.\n   * @param eventInfo `EventInfo` to set `action` and `actionElement` if an\n   *    action is found on the `actionElement`.\n   */\n  populateActionOnElement(actionElement, eventInfo) {\n    const actionMap = this.parseActions(actionElement);\n    const actionName = actionMap[getEventType(eventInfo)];\n    if (actionName !== undefined) {\n      setAction(eventInfo, actionName, actionElement);\n    }\n    if (this.a11yClickSupport) {\n      this.populateClickOnlyAction(actionElement, eventInfo, actionMap);\n    }\n  }\n  /**\n   * Parses and caches an element's jsaction element into a map.\n   *\n   * This is primarily for internal use.\n   *\n   * @param actionElement The DOM node to retrieve the jsaction map from.\n   * @return Map from event to qualified name of the jsaction bound to it.\n   */\n  parseActions(actionElement) {\n    let actionMap = get(actionElement);\n    if (!actionMap) {\n      const jsactionAttribute = actionElement.getAttribute(Attribute.JSACTION);\n      if (!jsactionAttribute) {\n        actionMap = EMPTY_ACTION_MAP;\n        set(actionElement, actionMap);\n      } else {\n        actionMap = getParsed(jsactionAttribute);\n        if (!actionMap) {\n          actionMap = {};\n          const values = jsactionAttribute.split(REGEXP_SEMICOLON);\n          for (let idx = 0; idx < values.length; idx++) {\n            const value = values[idx];\n            if (!value) {\n              continue;\n            }\n            const colon = value.indexOf(Char.EVENT_ACTION_SEPARATOR);\n            const hasColon = colon !== -1;\n            const type = hasColon ? value.substr(0, colon).trim() : DEFAULT_EVENT_TYPE;\n            const action = hasColon ? value.substr(colon + 1).trim() : value;\n            actionMap[type] = action;\n          }\n          setParsed(jsactionAttribute, actionMap);\n        }\n        set(actionElement, actionMap);\n      }\n    }\n    return actionMap;\n  }\n  addA11yClickSupport(updateEventInfoForA11yClick, preventDefaultForA11yClick, populateClickOnlyAction) {\n    this.a11yClickSupport = true;\n    this.updateEventInfoForA11yClick = updateEventInfoForA11yClick;\n    this.preventDefaultForA11yClick = preventDefaultForA11yClick;\n    this.populateClickOnlyAction = populateClickOnlyAction;\n  }\n}\n\n/**\n * @fileoverview An enum to control who can call certain jsaction APIs.\n */\nvar Restriction;\n(function (Restriction) {\n  Restriction[Restriction[\"I_AM_THE_JSACTION_FRAMEWORK\"] = 0] = \"I_AM_THE_JSACTION_FRAMEWORK\";\n})(Restriction || (Restriction = {}));\n\n/**\n * Receives a DOM event, determines the jsaction associated with the source\n * element of the DOM event, and invokes the handler associated with the\n * jsaction.\n */\nclass Dispatcher {\n  /**\n   * Options are:\n   *   - `eventReplayer`: When the event contract dispatches replay events\n   *      to the Dispatcher, the Dispatcher collects them and in the next tick\n   *      dispatches them to the `eventReplayer`. Defaults to dispatching to `dispatchDelegate`.\n   * @param dispatchDelegate A function that should handle dispatching an `EventInfoWrapper` to handlers.\n   */\n  constructor(dispatchDelegate, {\n    actionResolver,\n    eventReplayer\n  } = {}) {\n    this.dispatchDelegate = dispatchDelegate;\n    /** Whether the event replay is scheduled. */\n    this.eventReplayScheduled = false;\n    /** The queue of events. */\n    this.replayEventInfoWrappers = [];\n    this.actionResolver = actionResolver;\n    this.eventReplayer = eventReplayer;\n  }\n  /**\n   * Receives an event or the event queue from the EventContract. The event\n   * queue is copied and it attempts to replay.\n   * If event info is passed in it looks for an action handler that can handle\n   * the given event.  If there is no handler registered queues the event and\n   * checks if a loader is registered for the given namespace. If so, calls it.\n   *\n   * Alternatively, if in global dispatch mode, calls all registered global\n   * handlers for the appropriate event type.\n   *\n   * The three functionalities of this call are deliberately not split into\n   * three methods (and then declared as an abstract interface), because the\n   * interface is used by EventContract, which lives in a different jsbinary.\n   * Therefore the interface between the three is defined entirely in terms that\n   * are invariant under jscompiler processing (Function and Array, as opposed\n   * to a custom type with method names).\n   *\n   * @param eventInfo The info for the event that triggered this call or the\n   *     queue of events from EventContract.\n   */\n  dispatch(eventInfo) {\n    const eventInfoWrapper = new EventInfoWrapper(eventInfo);\n    this.actionResolver?.resolveEventType(eventInfo);\n    this.actionResolver?.resolveAction(eventInfo);\n    const action = eventInfoWrapper.getAction();\n    if (action && shouldPreventDefaultBeforeDispatching(action.element, eventInfoWrapper)) {\n      preventDefault(eventInfoWrapper.getEvent());\n    }\n    if (this.eventReplayer && eventInfoWrapper.getIsReplay()) {\n      this.scheduleEventInfoWrapperReplay(eventInfoWrapper);\n      return;\n    }\n    this.dispatchDelegate(eventInfoWrapper);\n  }\n  /**\n   * Schedules an `EventInfoWrapper` for replay. The replaying will happen in its own\n   * stack once the current flow cedes control. This is done to mimic\n   * browser event handling.\n   */\n  scheduleEventInfoWrapperReplay(eventInfoWrapper) {\n    this.replayEventInfoWrappers.push(eventInfoWrapper);\n    if (this.eventReplayScheduled) {\n      return;\n    }\n    this.eventReplayScheduled = true;\n    Promise.resolve().then(() => {\n      this.eventReplayScheduled = false;\n      this.eventReplayer(this.replayEventInfoWrappers);\n    });\n  }\n}\n/**\n * Creates an `EventReplayer` that calls the `replay` function for every `eventInfoWrapper` in\n * the queue.\n */\nfunction createEventReplayer(replay) {\n  return eventInfoWrappers => {\n    for (const eventInfoWrapper of eventInfoWrappers) {\n      replay(eventInfoWrapper);\n    }\n  };\n}\n/**\n * Returns true if the default action of this event should be prevented before\n * this event is dispatched.\n */\nfunction shouldPreventDefaultBeforeDispatching(actionElement, eventInfoWrapper) {\n  // Prevent browser from following <a> node links if a jsaction is present\n  // and we are dispatching the action now. Note that the targetElement may be\n  // a child of an anchor that has a jsaction attached. For that reason, we\n  // need to check the actionElement rather than the targetElement.\n  return actionElement.tagName === 'A' && (eventInfoWrapper.getEventType() === EventType.CLICK || eventInfoWrapper.getEventType() === EventType.CLICKMOD);\n}\n/**\n * Registers deferred functionality for an EventContract and a Jsaction\n * Dispatcher.\n */\nfunction registerDispatcher$2(eventContract, dispatcher) {\n  eventContract.ecrd(eventInfo => {\n    dispatcher.dispatch(eventInfo);\n  }, Restriction.I_AM_THE_JSACTION_FRAMEWORK);\n}\n\n/** An internal symbol used to indicate whether propagation should be stopped or not. */\nconst PROPAGATION_STOPPED_SYMBOL = Symbol.for('propagationStopped');\n/** Extra event phases beyond what the browser provides. */\nconst EventPhase = {\n  REPLAY: 101\n};\nconst PREVENT_DEFAULT_ERROR_MESSAGE_DETAILS = ' Because event replay occurs after browser dispatch, `preventDefault` would have no ' + 'effect. You can check whether an event is being replayed by accessing the event phase: ' + '`event.eventPhase === EventPhase.REPLAY`.';\nconst PREVENT_DEFAULT_ERROR_MESSAGE = `\\`preventDefault\\` called during event replay.`;\nconst COMPOSED_PATH_ERROR_MESSAGE_DETAILS = ' Because event replay occurs after browser ' + 'dispatch, `composedPath()` will be empty. Iterate parent nodes from `event.target` or ' + '`event.currentTarget` if you need to check elements in the event path.';\nconst COMPOSED_PATH_ERROR_MESSAGE = `\\`composedPath\\` called during event replay.`;\n/**\n * A dispatcher that uses browser-based `Event` semantics, for example bubbling, `stopPropagation`,\n * `currentTarget`, etc.\n */\nclass EventDispatcher {\n  constructor(dispatchDelegate, clickModSupport = true) {\n    this.dispatchDelegate = dispatchDelegate;\n    this.clickModSupport = clickModSupport;\n    this.actionResolver = new ActionResolver({\n      clickModSupport\n    });\n    this.dispatcher = new Dispatcher(eventInfoWrapper => {\n      this.dispatchToDelegate(eventInfoWrapper);\n    }, {\n      actionResolver: this.actionResolver\n    });\n  }\n  /**\n   * The entrypoint for the `EventContract` dispatch.\n   */\n  dispatch(eventInfo) {\n    this.dispatcher.dispatch(eventInfo);\n  }\n  /** Internal method that does basic disaptching. */\n  dispatchToDelegate(eventInfoWrapper) {\n    if (eventInfoWrapper.getIsReplay()) {\n      prepareEventForReplay(eventInfoWrapper);\n    }\n    prepareEventForBubbling(eventInfoWrapper);\n    while (eventInfoWrapper.getAction()) {\n      prepareEventForDispatch(eventInfoWrapper);\n      // If this is a capture event, ONLY dispatch if the action element is the target.\n      if (isCaptureEventType(eventInfoWrapper.getEventType()) && eventInfoWrapper.getAction().element !== eventInfoWrapper.getTargetElement()) {\n        return;\n      }\n      this.dispatchDelegate(eventInfoWrapper.getEvent(), eventInfoWrapper.getAction().name);\n      if (propagationStopped(eventInfoWrapper)) {\n        return;\n      }\n      this.actionResolver.resolveParentAction(eventInfoWrapper.eventInfo);\n    }\n  }\n}\nfunction prepareEventForBubbling(eventInfoWrapper) {\n  const event = eventInfoWrapper.getEvent();\n  const originalStopPropagation = eventInfoWrapper.getEvent().stopPropagation.bind(event);\n  const stopPropagation = () => {\n    event[PROPAGATION_STOPPED_SYMBOL] = true;\n    originalStopPropagation();\n  };\n  patchEventInstance(event, 'stopPropagation', stopPropagation);\n  patchEventInstance(event, 'stopImmediatePropagation', stopPropagation);\n}\nfunction propagationStopped(eventInfoWrapper) {\n  const event = eventInfoWrapper.getEvent();\n  return !!event[PROPAGATION_STOPPED_SYMBOL];\n}\nfunction prepareEventForReplay(eventInfoWrapper) {\n  const event = eventInfoWrapper.getEvent();\n  const target = eventInfoWrapper.getTargetElement();\n  const originalPreventDefault = event.preventDefault.bind(event);\n  patchEventInstance(event, 'target', target);\n  patchEventInstance(event, 'eventPhase', EventPhase.REPLAY);\n  patchEventInstance(event, 'preventDefault', () => {\n    originalPreventDefault();\n    throw new Error(PREVENT_DEFAULT_ERROR_MESSAGE + (ngDevMode ? PREVENT_DEFAULT_ERROR_MESSAGE_DETAILS : ''));\n  });\n  patchEventInstance(event, 'composedPath', () => {\n    throw new Error(COMPOSED_PATH_ERROR_MESSAGE + (ngDevMode ? COMPOSED_PATH_ERROR_MESSAGE_DETAILS : ''));\n  });\n}\nfunction prepareEventForDispatch(eventInfoWrapper) {\n  const event = eventInfoWrapper.getEvent();\n  const currentTarget = eventInfoWrapper.getAction()?.element;\n  if (currentTarget) {\n    patchEventInstance(event, 'currentTarget', currentTarget, {\n      // `currentTarget` is going to get reassigned every dispatch.\n      configurable: true\n    });\n  }\n}\n/**\n * Patch `Event` instance during non-standard `Event` dispatch. This patches just the `Event`\n * instance that the browser created, it does not patch global properties or methods.\n *\n * This is necessary because dispatching an `Event` outside of browser dispatch results in\n * incorrect properties and methods that need to be polyfilled or do not work.\n *\n * JSAction dispatch adds two extra \"phases\" to event dispatch:\n * 1. Event delegation - the event is being dispatched by a delegating event handler on a container\n *    (typically `window.document.documentElement`), to a delegated event handler on some child\n *    element. Certain `Event` properties will be unintuitive, such as `currentTarget`, which would\n *    be the container rather than the child element. Bubbling would also not work. In order to\n *    emulate the browser, these properties and methods on the `Event` are patched.\n * 2. Event replay - the event is being dispatched by the framework once the handlers have been\n *    loaded (during hydration, or late-loaded). Certain `Event` properties can be unset by the\n *    browser because the `Event` is no longer actively being dispatched, such as `target`. Other\n *    methods have no effect because the `Event` has already been dispatched, such as\n *    `preventDefault`. Bubbling would also not work. These properties and methods are patched,\n *    either to fill in information that the browser may have removed, or to throw errors in methods\n *    that no longer behave as expected.\n */\nfunction patchEventInstance(event, property, value, {\n  configurable = false\n} = {}) {\n  Object.defineProperty(event, property, {\n    value,\n    configurable\n  });\n}\n/**\n * Registers deferred functionality for an EventContract and a Jsaction\n * Dispatcher.\n */\nfunction registerDispatcher$1(eventContract, dispatcher) {\n  eventContract.ecrd(eventInfo => {\n    dispatcher.dispatch(eventInfo);\n  }, Restriction.I_AM_THE_JSACTION_FRAMEWORK);\n}\n\n/**\n * EarlyEventContract intercepts events in the bubbling phase at the\n * boundary of the document body. This mapping will be passed to the\n * late-loaded EventContract.\n */\nclass EarlyEventContract {\n  constructor(dataContainer = window, container = window.document.documentElement) {\n    this.dataContainer = dataContainer;\n    dataContainer._ejsa = createEarlyJsactionData(container);\n  }\n  /**\n   * Installs a list of event types for container .\n   */\n  addEvents(types, capture) {\n    addEvents(this.dataContainer._ejsa, types, capture);\n  }\n}\n/** Creates an `EarlyJsactionData` object. */\nfunction createEarlyJsactionData(container) {\n  const q = [];\n  const d = eventInfo => {\n    q.push(eventInfo);\n  };\n  const h = event => {\n    d(createEventInfoFromParameters(event.type, event, event.target, container, Date.now()));\n  };\n  return {\n    c: container,\n    q,\n    et: [],\n    etc: [],\n    d,\n    h\n  };\n}\n/** Add all the events to the container stored in the `EarlyJsactionData`. */\nfunction addEvents(earlyJsactionData, types, capture) {\n  for (let i = 0; i < types.length; i++) {\n    const eventType = types[i];\n    const eventTypes = capture ? earlyJsactionData.etc : earlyJsactionData.et;\n    eventTypes.push(eventType);\n    earlyJsactionData.c.addEventListener(eventType, earlyJsactionData.h, capture);\n  }\n}\n/** Get the queued `EventInfo` objects that were dispatched before a dispatcher was registered. */\nfunction getQueuedEventInfos(earlyJsactionData) {\n  return earlyJsactionData?.q ?? [];\n}\n/** Register a different dispatcher function on the `EarlyJsactionData`. */\nfunction registerDispatcher(earlyJsactionData, dispatcher) {\n  if (!earlyJsactionData) {\n    return;\n  }\n  earlyJsactionData.d = dispatcher;\n}\n/** Removes all event listener handlers. */\nfunction removeAllEventListeners(earlyJsactionData) {\n  if (!earlyJsactionData) {\n    return;\n  }\n  removeEventListeners(earlyJsactionData.c, earlyJsactionData.et, earlyJsactionData.h);\n  removeEventListeners(earlyJsactionData.c, earlyJsactionData.etc, earlyJsactionData.h, true);\n}\nfunction removeEventListeners(container, eventTypes, earlyEventHandler, capture) {\n  for (let i = 0; i < eventTypes.length; i++) {\n    container.removeEventListener(eventTypes[i], earlyEventHandler, /* useCapture */capture);\n  }\n}\n\n/**\n * @define Support for the non-bubbling mouseenter and mouseleave events.  This\n * flag can be overridden in a build rule.\n */\nconst MOUSE_SPECIAL_SUPPORT = false;\n\n/**\n * @fileoverview Implements the local event handling contract. This\n * allows DOM objects in a container that enters into this contract to\n * define event handlers which are executed in a local context.\n *\n * One EventContract instance can manage the contract for multiple\n * containers, which are added using the addContainer() method.\n *\n * Events can be registered using the addEvent() method.\n *\n * A Dispatcher is added using the registerDispatcher() method. Until there is\n * a dispatcher, events are queued. The idea is that the EventContract\n * class is inlined in the HTML of the top level page and instantiated\n * right after the start of <body>. The Dispatcher class is contained\n * in the external deferred js, and instantiated and registered with\n * EventContract when the external javascript in the page loads. The\n * external javascript will also register the jsaction handlers, which\n * then pick up the queued events at the time of registration.\n *\n * Since this class is meant to be inlined in the main page HTML, the\n * size of the binary compiled from this file MUST be kept as small as\n * possible and thus its dependencies to a minimum.\n */\n/**\n * EventContract intercepts events in the bubbling phase at the\n * boundary of a container element, and maps them to generic actions\n * which are specified using the custom jsaction attribute in\n * HTML. Behavior of the application is then specified in terms of\n * handler for such actions, cf. jsaction.Dispatcher in dispatcher.js.\n *\n * This has several benefits: (1) No DOM event handlers need to be\n * registered on the specific elements in the UI. (2) The set of\n * events that the application has to handle can be specified in terms\n * of the semantics of the application, rather than in terms of DOM\n * events. (3) Invocation of handlers can be delayed and handlers can\n * be delay loaded in a generic way.\n */\nclass EventContract {\n  static {\n    this.MOUSE_SPECIAL_SUPPORT = MOUSE_SPECIAL_SUPPORT;\n  }\n  constructor(containerManager) {\n    /**\n     * The DOM events which this contract covers. Used to prevent double\n     * registration of event types. The value of the map is the\n     * internally created DOM event handler function that handles the\n     * DOM events. See addEvent().\n     *\n     */\n    this.eventHandlers = {};\n    this.browserEventTypeToExtraEventTypes = {};\n    /**\n     * The dispatcher function. Events are passed to this function for\n     * handling once it was set using the registerDispatcher() method. This is\n     * done because the function is passed from another jsbinary, so passing the\n     * instance and invoking the method here would require to leave the method\n     * unobfuscated.\n     */\n    this.dispatcher = null;\n    /**\n     * The list of suspended `EventInfo` that will be dispatched\n     * as soon as the `Dispatcher` is registered.\n     */\n    this.queuedEventInfos = [];\n    this.containerManager = containerManager;\n  }\n  handleEvent(eventType, event, container) {\n    const eventInfo = createEventInfoFromParameters(/* eventType= */eventType, /* event= */event, /* targetElement= */event.target, /* container= */container, /* timestamp= */Date.now());\n    this.handleEventInfo(eventInfo);\n  }\n  /**\n   * Handle an `EventInfo`.\n   */\n  handleEventInfo(eventInfo) {\n    if (!this.dispatcher) {\n      // All events are queued when the dispatcher isn't yet loaded.\n      setIsReplay(eventInfo, true);\n      this.queuedEventInfos?.push(eventInfo);\n      return;\n    }\n    this.dispatcher(eventInfo);\n  }\n  /**\n   * Enables jsaction handlers to be called for the event type given by\n   * name.\n   *\n   * If the event is already registered, this does nothing.\n   *\n   * @param prefixedEventType If supplied, this event is used in\n   *     the actual browser event registration instead of the name that is\n   *     exposed to jsaction. Use this if you e.g. want users to be able\n   *     to subscribe to jsaction=\"transitionEnd:foo\" while the underlying\n   *     event is webkitTransitionEnd in one browser and mozTransitionEnd\n   *     in another.\n   */\n  addEvent(eventType, prefixedEventType) {\n    if (eventType in this.eventHandlers || !this.containerManager) {\n      return;\n    }\n    if (!EventContract.MOUSE_SPECIAL_SUPPORT && MOUSE_SPECIAL_EVENT_TYPES.indexOf(eventType) >= 0) {\n      return;\n    }\n    const eventHandler = (eventType, event, container) => {\n      this.handleEvent(eventType, event, container);\n    };\n    // Store the callback to allow us to replay events.\n    this.eventHandlers[eventType] = eventHandler;\n    const browserEventType = getBrowserEventType(prefixedEventType || eventType);\n    if (browserEventType !== eventType) {\n      const eventTypes = this.browserEventTypeToExtraEventTypes[browserEventType] || [];\n      eventTypes.push(eventType);\n      this.browserEventTypeToExtraEventTypes[browserEventType] = eventTypes;\n    }\n    this.containerManager.addEventListener(browserEventType, element => {\n      return event => {\n        eventHandler(eventType, event, element);\n      };\n    });\n  }\n  /**\n   * Gets the queued early events and replay them using the appropriate handler\n   * in the provided event contract. Once all the events are replayed, it cleans\n   * up the early contract.\n   */\n  replayEarlyEvents(earlyJsactionData = window._ejsa) {\n    // Check if the early contract is present and prevent calling this function\n    // more than once.\n    if (!earlyJsactionData) {\n      return;\n    }\n    // Replay the early contract events.\n    this.replayEarlyEventInfos(earlyJsactionData.q);\n    // Clean up the early contract.\n    removeAllEventListeners(earlyJsactionData);\n    delete window._ejsa;\n  }\n  /**\n   * Replays all the early `EventInfo` objects, dispatching them through the normal\n   * `EventContract` flow.\n   */\n  replayEarlyEventInfos(earlyEventInfos) {\n    for (let i = 0; i < earlyEventInfos.length; i++) {\n      const earlyEventInfo = earlyEventInfos[i];\n      const eventTypes = this.getEventTypesForBrowserEventType(earlyEventInfo.eventType);\n      for (let j = 0; j < eventTypes.length; j++) {\n        const eventInfo = cloneEventInfo(earlyEventInfo);\n        // EventInfo eventType maps to JSAction's internal event type,\n        // rather than the browser event type.\n        setEventType(eventInfo, eventTypes[j]);\n        this.handleEventInfo(eventInfo);\n      }\n    }\n  }\n  /**\n   * Returns all JSAction event types that have been registered for a given\n   * browser event type.\n   */\n  getEventTypesForBrowserEventType(browserEventType) {\n    const eventTypes = [];\n    if (this.eventHandlers[browserEventType]) {\n      eventTypes.push(browserEventType);\n    }\n    if (this.browserEventTypeToExtraEventTypes[browserEventType]) {\n      eventTypes.push(...this.browserEventTypeToExtraEventTypes[browserEventType]);\n    }\n    return eventTypes;\n  }\n  /**\n   * Returns the event handler function for a given event type.\n   */\n  handler(eventType) {\n    return this.eventHandlers[eventType];\n  }\n  /**\n   * Cleans up the event contract. This resets all of the `EventContract`'s\n   * internal state. Users are responsible for not using this `EventContract`\n   * after it has been cleaned up.\n   */\n  cleanUp() {\n    this.containerManager.cleanUp();\n    this.containerManager = null;\n    this.eventHandlers = {};\n    this.browserEventTypeToExtraEventTypes = {};\n    this.dispatcher = null;\n    this.queuedEventInfos = [];\n  }\n  /**\n   * Register a dispatcher function. Event info of each event mapped to\n   * a jsaction is passed for handling to this callback. The queued\n   * events are passed as well to the dispatcher for later replaying\n   * once the dispatcher is registered. Clears the event queue to null.\n   *\n   * @param dispatcher The dispatcher function.\n   * @param restriction\n   */\n  registerDispatcher(dispatcher, restriction) {\n    this.ecrd(dispatcher, restriction);\n  }\n  /**\n   * Unrenamed alias for registerDispatcher. Necessary for any codebases that\n   * split the `EventContract` and `Dispatcher` code into different compilation\n   * units.\n   */\n  ecrd(dispatcher, restriction) {\n    this.dispatcher = dispatcher;\n    if (this.queuedEventInfos?.length) {\n      for (let i = 0; i < this.queuedEventInfos.length; i++) {\n        this.handleEventInfo(this.queuedEventInfos[i]);\n      }\n      this.queuedEventInfos = null;\n    }\n  }\n}\n\n/**\n * Creates an `EarlyJsactionData`, adds events to it, and populates it on a nested object on\n * the window.\n */\nfunction bootstrapAppScopedEarlyEventContract(container, appId, bubbleEventTypes, captureEventTypes, dataContainer = window) {\n  const earlyJsactionData = createEarlyJsactionData(container);\n  if (!dataContainer._ejsas) {\n    dataContainer._ejsas = {};\n  }\n  dataContainer._ejsas[appId] = earlyJsactionData;\n  addEvents(earlyJsactionData, bubbleEventTypes);\n  addEvents(earlyJsactionData, captureEventTypes, /* capture= */true);\n}\n/** Get the queued `EventInfo` objects that were dispatched before a dispatcher was registered. */\nfunction getAppScopedQueuedEventInfos(appId, dataContainer = window) {\n  return getQueuedEventInfos(dataContainer._ejsas?.[appId]);\n}\n/**\n * Registers a dispatcher function on the `EarlyJsactionData` present on the nested object on the\n * window.\n */\nfunction registerAppScopedDispatcher(restriction, appId, dispatcher, dataContainer = window) {\n  registerDispatcher(dataContainer._ejsas?.[appId], dispatcher);\n}\n/** Removes all event listener handlers. */\nfunction removeAllAppScopedEventListeners(appId, dataContainer = window) {\n  removeAllEventListeners(dataContainer._ejsas?.[appId]);\n}\n/** Clear the early event contract. */\nfunction clearAppScopedEarlyEventContract(appId, dataContainer = window) {\n  if (!dataContainer._ejsas) {\n    return;\n  }\n  dataContainer._ejsas[appId] = undefined;\n}\nexport { Attribute, EventContract, EventContractContainer, EventDispatcher, EventInfoWrapper, EventPhase, bootstrapAppScopedEarlyEventContract, clearAppScopedEarlyEventContract, getDefaulted as getActionCache, getAppScopedQueuedEventInfos, isCaptureEventType, isEarlyEventType, registerAppScopedDispatcher, registerDispatcher$1 as registerDispatcher, removeAllAppScopedEventListeners };", "map": {"version": 3, "names": ["Attribute", "JSACTION", "Property", "OWNER", "parseCache", "get", "element", "getDefaulted", "cache", "set", "actionMap", "getParsed", "text", "setParsed", "parsed", "clear", "EventType", "AUXCLICK", "CHANGE", "CLICK", "CLICKMOD", "CLICKONLY", "DBLCLICK", "FOCUS", "FOCUSIN", "BLUR", "FOCUSOUT", "SUBMIT", "KEYDOWN", "KEYPRESS", "KEYUP", "MOUSEUP", "MOUSEDOWN", "MOUSEOVER", "MOUSEOUT", "MOUSEENTER", "MOUSELEAVE", "MOUSEMOVE", "POINTERUP", "POINTERDOWN", "POINTEROVER", "POINTEROUT", "POINTERENTER", "POINTERLEAVE", "POINTERMOVE", "POINTERCANCEL", "GOTPOINTERCAPTURE", "LOSTPOINTERCAPTURE", "ERROR", "LOAD", "UNLOAD", "TOUCHSTART", "TOUCHEND", "TOUCHMOVE", "INPUT", "SCROLL", "TOGGLE", "CUSTOM", "MOUSE_SPECIAL_EVENT_TYPES", "BUBBLE_EVENT_TYPES", "CAPTURE_EVENT_TYPES", "isCaptureEventType", "eventType", "indexOf", "EARLY_EVENT_TYPES", "concat", "isEarlyEventType", "MAC_ENTER", "ENTER", "SPACE", "KeyCode", "getBrowserEventType", "addEventListener", "handler", "capture", "removeEventListener", "info", "detachEvent", "stopPropagation", "e", "cancelBubble", "preventDefault", "returnValue", "get<PERSON><PERSON><PERSON>", "el", "target", "getAttribute", "parentNode", "isMac", "navigator", "test", "userAgent", "isMiddleClick", "which", "button", "isModifiedClickEvent", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "isWebKit", "isIe", "isGecko", "product", "isValidActionKeyTarget", "isTextControl", "isNativelyActivatable", "isContentEditable", "hasModifierKey", "altKey", "shouldCallPreventDefaultOnNativeHtmlControl", "tagName", "toUpperCase", "role", "isNativeHTMLControl", "processSpace", "isActionKeyEvent", "key", "keyCode", "ACTION_KEY_TO_KEYCODE", "type", "isFocusable", "isSpecificTriggerKey", "IDENTIFIER_TO_KEY_TRIGGER_MAPPING", "isDefaultTriggerKey", "hasType", "NATIVELY_FOCUSABLE_ELEMENTS", "hasSpecifiedTabIndex", "disabled", "attrNode", "getAttributeNode", "specified", "isSpaceKeyEvent", "elementName", "isMouseSpecialEvent", "related", "relatedTarget", "contains", "createMouseSpecialEvent", "copy", "property", "value", "getTouchData", "event", "touch", "changedTouches", "touches", "clientX", "clientY", "screenX", "screenY", "recreateTouchEventAsClick", "click", "Date", "now", "syntheticPreventDefault", "syntheticStopPropagation", "defaultPrevented", "_propagationStopped", "PROCESS_SPACE", "TEXT_CONTROLS", "NATIVE_HTML_CONTROLS", "testing", "setIsMac", "isIos", "EventContractContainer", "constructor", "handlerInfos", "<PERSON><PERSON><PERSON><PERSON>", "style", "cursor", "push", "cleanUp", "i", "length", "Char", "NAMESPACE_ACTION_SEPARATOR", "EVENT_ACTION_SEPARATOR", "getEventType", "eventInfo", "setEventType", "getEvent", "setEvent", "getTargetElement", "targetElement", "setTargetElement", "getContainer", "eic", "<PERSON><PERSON><PERSON><PERSON>", "container", "getTimestamp", "timeStamp", "setTimestamp", "timestamp", "getAction", "eia", "setAction", "actionName", "actionElement", "unsetAction", "undefined", "getActionName", "actionInfo", "getActionElement", "getIsReplay", "eirp", "setIsReplay", "replay", "getA11yClickKey", "eiack", "setA11yClickKey", "a11yClickKey", "getResolved", "eir", "setResolved", "resolved", "cloneEventInfo", "createEventInfoFromParameters", "action", "isReplay", "createEventInfo", "name", "EventInfoWrapper", "clone", "EMPTY_ACTION_MAP", "REGEXP_SEMICOLON", "DEFAULT_EVENT_TYPE", "ActionResolver", "syntheticMouseEventSupport", "clickModSupport", "a11yClickSupport", "updateEventInfoForA11yClick", "preventDefaultForA11yClick", "populateClickOnlyAction", "resolveEventType", "resolveAction", "populateAction", "resolveParentAction", "getParentNode", "currentTarget", "nodeType", "Node", "ELEMENT_NODE", "populateActionOnElement", "copiedEvent", "owner", "nodeName", "host", "parseActions", "jsactionAttribute", "values", "split", "idx", "colon", "hasColon", "substr", "trim", "addA11yClickSupport", "Restriction", "Di<PERSON>atcher", "dispatchDelegate", "actionResolver", "eventReplayer", "eventReplayScheduled", "replayEventInfoWrappers", "dispatch", "eventInfoWrapper", "shouldPreventDefaultBeforeDispatching", "scheduleEventInfoWrapperReplay", "Promise", "resolve", "then", "createEventReplayer", "eventInfoWrappers", "registerDispatcher$2", "eventContract", "dispatcher", "ecrd", "I_AM_THE_JSACTION_FRAMEWORK", "PROPAGATION_STOPPED_SYMBOL", "Symbol", "for", "EventPhase", "REPLAY", "PREVENT_DEFAULT_ERROR_MESSAGE_DETAILS", "PREVENT_DEFAULT_ERROR_MESSAGE", "COMPOSED_PATH_ERROR_MESSAGE_DETAILS", "COMPOSED_PATH_ERROR_MESSAGE", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dispatchToDelegate", "prepareEventForReplay", "prepareEventForBubbling", "prepareEventForDispatch", "propagationStopped", "originalStopPropagation", "bind", "patchEventInstance", "originalPreventDefault", "Error", "ngDevMode", "configurable", "Object", "defineProperty", "registerDispatcher$1", "EarlyEventContract", "dataContainer", "window", "document", "documentElement", "_ejsa", "createEarlyJsactionData", "addEvents", "types", "q", "d", "h", "c", "et", "etc", "earlyJsactionData", "eventTypes", "getQueuedEventInfos", "registerDispatcher", "removeAllEventListeners", "removeEventListeners", "earlyEventHandler", "MOUSE_SPECIAL_SUPPORT", "EventContract", "containerManager", "eventHandlers", "browserEventTypeToExtraEventTypes", "queuedEventInfos", "handleEvent", "handleEventInfo", "addEvent", "prefixedEventType", "<PERSON><PERSON><PERSON><PERSON>", "browserEventType", "replay<PERSON>ar<PERSON>E<PERSON>s", "replayEarlyEventInfos", "earlyEventInfos", "earlyEventInfo", "getEventTypesForBrowserEventType", "j", "restriction", "bootstrapAppScopedEarlyEventContract", "appId", "bubbleEventTypes", "captureEventTypes", "_ejsas", "getAppScopedQueuedEventInfos", "registerAppScopedDispatcher", "removeAllAppScopedEventListeners", "clearAppScopedEarlyEventContract", "getActionCache"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/monthsary-website/node_modules/@angular/core/fesm2022/primitives/event-dispatch.mjs"], "sourcesContent": ["/**\n * @license Angular v18.2.13\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nconst Attribute = {\n    /**\n     * The jsaction attribute defines a mapping of a DOM event to a\n     * generic event (aka jsaction), to which the actual event handlers\n     * that implement the behavior of the application are bound. The\n     * value is a semicolon separated list of colon separated pairs of\n     * an optional DOM event name and a jsaction name. If the optional\n     * DOM event name is omitted, 'click' is assumed. The jsaction names\n     * are dot separated pairs of a namespace and a simple jsaction\n     * name.\n     *\n     * See grammar in README.md for expected syntax in the attribute value.\n     */\n    JSACTION: 'jsaction',\n};\n\n/** All properties that are used by jsaction. */\nconst Property = {\n    /**\n     * The parsed value of the jsaction attribute is stored in this\n     * property on the DOM node. The parsed value is an Object. The\n     * property names of the object are the events; the values are the\n     * names of the actions. This property is attached even on nodes\n     * that don't have a jsaction attribute as an optimization, because\n     * property lookup is faster than attribute access.\n     */\n    JSACTION: '__jsaction',\n    /**\n     * The owner property references an a logical owner for a DOM node. JSAction\n     * will follow this reference instead of parentNode when traversing the DOM\n     * to find jsaction attributes. This allows overlaying a logical structure\n     * over a document where the DOM structure can't reflect that structure.\n     */\n    OWNER: '__owner',\n};\n\n/**\n * Map from jsaction annotation to a parsed map from event name to action name.\n */\nconst parseCache = {};\n/**\n * Reads the jsaction parser cache from the given DOM Element.\n */\nfunction get(element) {\n    return element[Property.JSACTION];\n}\n/**\n * Reads the jsaction parser cache for the given DOM element. If no cache is yet present,\n * creates an empty one.\n */\nfunction getDefaulted(element) {\n    const cache = get(element) ?? {};\n    set(element, cache);\n    return cache;\n}\n/**\n * Writes the jsaction parser cache to the given DOM Element.\n */\nfunction set(element, actionMap) {\n    element[Property.JSACTION] = actionMap;\n}\n/**\n * Looks up the parsed action map from the source jsaction attribute value.\n *\n * @param text Unparsed jsaction attribute value.\n * @return Parsed jsaction attribute value, if already present in the cache.\n */\nfunction getParsed(text) {\n    return parseCache[text];\n}\n/**\n * Inserts the parse result for the given source jsaction value into the cache.\n *\n * @param text Unparsed jsaction attribute value.\n * @param parsed Attribute value parsed into the action map.\n */\nfunction setParsed(text, parsed) {\n    parseCache[text] = parsed;\n}\n/**\n * Clears the jsaction parser cache from the given DOM Element.\n *\n * @param element .\n */\nfunction clear(element) {\n    if (Property.JSACTION in element) {\n        delete element[Property.JSACTION];\n    }\n}\n\n/*\n * Names of events that are special to jsaction. These are not all\n * event types that are legal to use in either HTML or the addEvent()\n * API, but these are the ones that are treated specially. All other\n * DOM events can be used in either addEvent() or in the value of the\n * jsaction attribute. Beware of browser specific events or events\n * that don't bubble though: If they are not mentioned here, then\n * event contract doesn't work around their peculiarities.\n */\nconst EventType = {\n    /**\n     * Mouse middle click, introduced in Chrome 55 and not yet supported on\n     * other browsers.\n     */\n    AUXCLICK: 'auxclick',\n    /**\n     * The change event fired by browsers when the `value` attribute of input,\n     * select, and textarea elements are changed.\n     */\n    CHANGE: 'change',\n    /**\n     * The click event. In addEvent() refers to all click events, in the\n     * jsaction attribute it refers to the unmodified click and Enter/Space\n     * keypress events.  In the latter case, a jsaction click will be triggered,\n     * for accessibility reasons.  See clickmod and clickonly, below.\n     */\n    CLICK: 'click',\n    /**\n     * Specifies the jsaction for a modified click event (i.e. a mouse\n     * click with the modifier key Cmd/Ctrl pressed). This event isn't\n     * separately enabled in addEvent(), because in the DOM, it's just a\n     * click event.\n     */\n    CLICKMOD: 'clickmod',\n    /**\n     * Specifies the jsaction for a click-only event.  Click-only doesn't take\n     * into account the case where an element with focus receives an Enter/Space\n     * keypress.  This event isn't separately enabled in addEvent().\n     */\n    CLICKONLY: 'clickonly',\n    /**\n     * The dblclick event.\n     */\n    DBLCLICK: 'dblclick',\n    /**\n     * Focus doesn't bubble, but you can use it in addEvent() and\n     * jsaction anyway. EventContract does the right thing under the\n     * hood.\n     */\n    FOCUS: 'focus',\n    /**\n     * This event only exists in IE. For addEvent() and jsaction, use\n     * focus instead; EventContract does the right thing even though\n     * focus doesn't bubble.\n     */\n    FOCUSIN: 'focusin',\n    /**\n     * Analog to focus.\n     */\n    BLUR: 'blur',\n    /**\n     * Analog to focusin.\n     */\n    FOCUSOUT: 'focusout',\n    /**\n     * Submit doesn't bubble, so it cannot be used with event\n     * contract. However, the browser helpfully fires a click event on\n     * the submit button of a form (even if the form is not submitted by\n     * a click on the submit button). So you should handle click on the\n     * submit button instead.\n     */\n    SUBMIT: 'submit',\n    /**\n     * The keydown event. In addEvent() and non-click jsaction it represents the\n     * regular DOM keydown event. It represents click actions in non-Gecko\n     * browsers.\n     */\n    KEYDOWN: 'keydown',\n    /**\n     * The keypress event. In addEvent() and non-click jsaction it represents the\n     * regular DOM keypress event. It represents click actions in Gecko browsers.\n     */\n    KEYPRESS: 'keypress',\n    /**\n     * The keyup event. In addEvent() and non-click jsaction it represents the\n     * regular DOM keyup event. It represents click actions in non-Gecko\n     * browsers.\n     */\n    KEYUP: 'keyup',\n    /**\n     * The mouseup event. Can either be used directly or used implicitly to\n     * capture mouseup events. In addEvent(), it represents a regular DOM\n     * mouseup event.\n     */\n    MOUSEUP: 'mouseup',\n    /**\n     * The mousedown event. Can either be used directly or used implicitly to\n     * capture mouseenter events. In addEvent(), it represents a regular DOM\n     * mouseover event.\n     */\n    MOUSEDOWN: 'mousedown',\n    /**\n     * The mouseover event. Can either be used directly or used implicitly to\n     * capture mouseenter events. In addEvent(), it represents a regular DOM\n     * mouseover event.\n     */\n    MOUSEOVER: 'mouseover',\n    /**\n     * The mouseout event. Can either be used directly or used implicitly to\n     * capture mouseover events. In addEvent(), it represents a regular DOM\n     * mouseout event.\n     */\n    MOUSEOUT: 'mouseout',\n    /**\n     * The mouseenter event. Does not bubble and fires individually on each\n     * element being entered within a DOM tree.\n     */\n    MOUSEENTER: 'mouseenter',\n    /**\n     * The mouseleave event. Does not bubble and fires individually on each\n     * element being entered within a DOM tree.\n     */\n    MOUSELEAVE: 'mouseleave',\n    /**\n     * The mousemove event.\n     */\n    MOUSEMOVE: 'mousemove',\n    /**\n     * The pointerup event. Can either be used directly or used implicitly to\n     * capture pointerup events. In addEvent(), it represents a regular DOM\n     * pointerup event.\n     */\n    POINTERUP: 'pointerup',\n    /**\n     * The pointerdown event. Can either be used directly or used implicitly to\n     * capture pointerenter events. In addEvent(), it represents a regular DOM\n     * mouseover event.\n     */\n    POINTERDOWN: 'pointerdown',\n    /**\n     * The pointerover event. Can either be used directly or used implicitly to\n     * capture pointerenter events. In addEvent(), it represents a regular DOM\n     * pointerover event.\n     */\n    POINTEROVER: 'pointerover',\n    /**\n     * The pointerout event. Can either be used directly or used implicitly to\n     * capture pointerover events. In addEvent(), it represents a regular DOM\n     * pointerout event.\n     */\n    POINTEROUT: 'pointerout',\n    /**\n     * The pointerenter event. Does not bubble and fires individually on each\n     * element being entered within a DOM tree.\n     */\n    POINTERENTER: 'pointerenter',\n    /**\n     * The pointerleave event. Does not bubble and fires individually on each\n     * element being entered within a DOM tree.\n     */\n    POINTERLEAVE: 'pointerleave',\n    /**\n     * The pointermove event.\n     */\n    POINTERMOVE: 'pointermove',\n    /**\n     * The pointercancel event.\n     */\n    POINTERCANCEL: 'pointercancel',\n    /**\n     * The gotpointercapture event is fired when\n     * Element.setPointerCapture(pointerId) is called on a mouse input, or\n     * implicitly when a touch input begins.\n     */\n    GOTPOINTERCAPTURE: 'gotpointercapture',\n    /**\n     * The lostpointercapture event is fired when\n     * Element.releasePointerCapture(pointerId) is called, or implicitly after a\n     * touch input ends.\n     */\n    LOSTPOINTERCAPTURE: 'lostpointercapture',\n    /**\n     * The error event. The error event doesn't bubble, but you can use it in\n     * addEvent() and jsaction anyway. EventContract does the right thing under\n     * the hood (except in IE8 which does not use error events).\n     */\n    ERROR: 'error',\n    /**\n     * The load event. The load event doesn't bubble, but you can use it in\n     * addEvent() and jsaction anyway. EventContract does the right thing\n     * under the hood.\n     */\n    LOAD: 'load',\n    /**\n     * The unload event.\n     */\n    UNLOAD: 'unload',\n    /**\n     * The touchstart event. Bubbles, will only ever fire in browsers with\n     * touch support.\n     */\n    TOUCHSTART: 'touchstart',\n    /**\n     * The touchend event. Bubbles, will only ever fire in browsers with\n     * touch support.\n     */\n    TOUCHEND: 'touchend',\n    /**\n     * The touchmove event. Bubbles, will only ever fire in browsers with\n     * touch support.\n     */\n    TOUCHMOVE: 'touchmove',\n    /**\n     * The input event.\n     */\n    INPUT: 'input',\n    /**\n     * The scroll event.\n     */\n    SCROLL: 'scroll',\n    /**\n     * The toggle event. The toggle event doesn't bubble, but you can use it in\n     * addEvent() and jsaction anyway. EventContract does the right thing\n     * under the hood.\n     */\n    TOGGLE: 'toggle',\n    /**\n     * A custom event. The actual custom event type is declared as the 'type'\n     * field in the event details. Supported in Firefox 6+, IE 9+, and all Chrome\n     * versions.\n     *\n     * This is an internal name. Users should use jsaction's fireCustomEvent to\n     * fire custom events instead of relying on this type to create them.\n     */\n    CUSTOM: '_custom',\n};\n/** All event types that do not bubble or capture and need a polyfill. */\nconst MOUSE_SPECIAL_EVENT_TYPES = [\n    EventType.MOUSEENTER,\n    EventType.MOUSELEAVE,\n    'pointerenter',\n    'pointerleave',\n];\n/** All event types that are registered in the bubble phase. */\nconst BUBBLE_EVENT_TYPES = [\n    EventType.CLICK,\n    EventType.DBLCLICK,\n    EventType.FOCUSIN,\n    EventType.FOCUSOUT,\n    EventType.KEYDOWN,\n    EventType.KEYUP,\n    EventType.KEYPRESS,\n    EventType.MOUSEOVER,\n    EventType.MOUSEOUT,\n    EventType.SUBMIT,\n    EventType.TOUCHSTART,\n    EventType.TOUCHEND,\n    EventType.TOUCHMOVE,\n    'touchcancel',\n    'auxclick',\n    'change',\n    'compositionstart',\n    'compositionupdate',\n    'compositionend',\n    'beforeinput',\n    'input',\n    'select',\n    'copy',\n    'cut',\n    'paste',\n    'mousedown',\n    'mouseup',\n    'wheel',\n    'contextmenu',\n    'dragover',\n    'dragenter',\n    'dragleave',\n    'drop',\n    'dragstart',\n    'dragend',\n    'pointerdown',\n    'pointermove',\n    'pointerup',\n    'pointercancel',\n    'pointerover',\n    'pointerout',\n    'gotpointercapture',\n    'lostpointercapture',\n    // Video events.\n    'ended',\n    'loadedmetadata',\n    // Page visibility events.\n    'pagehide',\n    'pageshow',\n    'visibilitychange',\n    // Content visibility events.\n    'beforematch',\n];\n/** All event types that are registered in the capture phase. */\nconst CAPTURE_EVENT_TYPES = [\n    EventType.FOCUS,\n    EventType.BLUR,\n    EventType.ERROR,\n    EventType.LOAD,\n    EventType.TOGGLE,\n];\n/**\n * Whether or not an event type should be registered in the capture phase.\n * @param eventType\n * @returns bool\n */\nconst isCaptureEventType = (eventType) => CAPTURE_EVENT_TYPES.indexOf(eventType) >= 0;\n/** All event types that are registered early.  */\nconst EARLY_EVENT_TYPES = BUBBLE_EVENT_TYPES.concat(CAPTURE_EVENT_TYPES);\n/**\n * Whether or not an event type is registered in the early contract.\n */\nconst isEarlyEventType = (eventType) => EARLY_EVENT_TYPES.indexOf(eventType) >= 0;\n\n/**\n * If on a Macintosh with an extended keyboard, the Enter key located in the\n * numeric pad has a different ASCII code.\n */\nconst MAC_ENTER = 3;\n/** The Enter key. */\nconst ENTER = 13;\n/** The Space key. */\nconst SPACE = 32;\n/** Special keycodes used by jsaction for the generic click action. */\nconst KeyCode = { MAC_ENTER, ENTER, SPACE };\n\n/**\n * Gets a browser event type, if it would differ from the JSAction event type.\n */\nfunction getBrowserEventType(eventType) {\n    // Mouseenter and mouseleave events are not handled directly because they\n    // are not available everywhere. In browsers where they are available, they\n    // don't bubble and aren't visible at the container boundary. Instead, we\n    // synthesize the mouseenter and mouseleave events from mouseover and\n    // mouseout events, respectively. Cf. eventcontract.js.\n    if (eventType === EventType.MOUSEENTER) {\n        return EventType.MOUSEOVER;\n    }\n    else if (eventType === EventType.MOUSELEAVE) {\n        return EventType.MOUSEOUT;\n    }\n    else if (eventType === EventType.POINTERENTER) {\n        return EventType.POINTEROVER;\n    }\n    else if (eventType === EventType.POINTERLEAVE) {\n        return EventType.POINTEROUT;\n    }\n    return eventType;\n}\n/**\n * Registers the event handler function with the given DOM element for\n * the given event type.\n *\n * @param element The element.\n * @param eventType The event type.\n * @param handler The handler function to install.\n * @return Information needed to uninstall the event handler eventually.\n */\nfunction addEventListener(element, eventType, handler) {\n    // All event handlers are registered in the bubbling\n    // phase.\n    //\n    // All browsers support focus and blur, but these events only are propagated\n    // in the capture phase. Very legacy browsers do not support focusin or\n    // focusout.\n    //\n    // It would be a bad idea to register all event handlers in the\n    // capture phase because then regular onclick handlers would not be\n    // executed at all on events that trigger a jsaction. That's not\n    // entirely what we want, at least for now.\n    //\n    // Error and load events (i.e. on images) do not bubble so they are also\n    // handled in the capture phase.\n    let capture = false;\n    if (isCaptureEventType(eventType)) {\n        capture = true;\n    }\n    element.addEventListener(eventType, handler, capture);\n    return { eventType, handler, capture };\n}\n/**\n * Removes the event handler for the given event from the element.\n * the given event type.\n *\n * @param element The element.\n * @param info The information needed to deregister the handler, as returned by\n *     addEventListener(), above.\n */\nfunction removeEventListener(element, info) {\n    if (element.removeEventListener) {\n        element.removeEventListener(info.eventType, info.handler, info.capture);\n        // `detachEvent` is an old DOM API.\n        // tslint:disable-next-line:no-any\n    }\n    else if (element.detachEvent) {\n        // `detachEvent` is an old DOM API.\n        // tslint:disable-next-line:no-any\n        element.detachEvent(`on${info.eventType}`, info.handler);\n    }\n}\n/**\n * Cancels propagation of an event.\n * @param e The event to cancel propagation for.\n */\nfunction stopPropagation(e) {\n    e.stopPropagation ? e.stopPropagation() : (e.cancelBubble = true);\n}\n/**\n * Prevents the default action of an event.\n * @param e The event to prevent the default action for.\n */\nfunction preventDefault(e) {\n    e.preventDefault ? e.preventDefault() : (e.returnValue = false);\n}\n/**\n * Gets the target Element of the event. In Firefox, a text node may appear as\n * the target of the event, in which case we return the parent element of the\n * text node.\n * @param e The event to get the target of.\n * @return The target element.\n */\nfunction getTarget(e) {\n    let el = e.target;\n    // In Firefox, the event may have a text node as its target. We always\n    // want the parent Element the text node belongs to, however.\n    if (!el.getAttribute && el.parentNode) {\n        el = el.parentNode;\n    }\n    return el;\n}\n/**\n * Whether we are on a Mac. Not pulling in useragent just for this.\n */\nlet isMac = typeof navigator !== 'undefined' && /Macintosh/.test(navigator.userAgent);\n/**\n * Determines and returns whether the given event (which is assumed to be a\n * click event) is a middle click.\n * NOTE: There is not a consistent way to identify middle click\n * http://www.unixpapa.com/js/mouse.html\n */\nfunction isMiddleClick(e) {\n    return (\n    // `which` is an old DOM API.\n    // tslint:disable-next-line:no-any\n    e.which === 2 ||\n        // `which` is an old DOM API.\n        // tslint:disable-next-line:no-any\n        (e.which == null &&\n            // `button` is an old DOM API.\n            // tslint:disable-next-line:no-any\n            e.button === 4) // middle click for IE\n    );\n}\n/**\n * Determines and returns whether the given event (which is assumed\n * to be a click event) is modified. A middle click is considered a modified\n * click to retain the default browser action, which opens a link in a new tab.\n * @param e The event.\n * @return Whether the given event is modified.\n */\nfunction isModifiedClickEvent(e) {\n    return (\n    // `metaKey` is an old DOM API.\n    // tslint:disable-next-line:no-any\n    (isMac && e.metaKey) ||\n        // `ctrlKey` is an old DOM API.\n        // tslint:disable-next-line:no-any\n        (!isMac && e.ctrlKey) ||\n        isMiddleClick(e) ||\n        // `shiftKey` is an old DOM API.\n        // tslint:disable-next-line:no-any\n        e.shiftKey);\n}\n/** Whether we are on WebKit (e.g., Chrome). */\nconst isWebKit = typeof navigator !== 'undefined' &&\n    !/Opera/.test(navigator.userAgent) &&\n    /WebKit/.test(navigator.userAgent);\n/** Whether we are on IE. */\nconst isIe = typeof navigator !== 'undefined' &&\n    (/MSIE/.test(navigator.userAgent) || /Trident/.test(navigator.userAgent));\n/** Whether we are on Gecko (e.g., Firefox). */\nconst isGecko = typeof navigator !== 'undefined' &&\n    !/Opera|WebKit/.test(navigator.userAgent) &&\n    /Gecko/.test(navigator.product);\n/**\n * Determines and returns whether the given element is a valid target for\n * keypress/keydown DOM events that act like regular DOM clicks.\n * @param el The element.\n * @return Whether the given element is a valid action key target.\n */\nfunction isValidActionKeyTarget(el) {\n    if (!('getAttribute' in el)) {\n        return false;\n    }\n    if (isTextControl(el)) {\n        return false;\n    }\n    if (isNativelyActivatable(el)) {\n        return false;\n    }\n    // `isContentEditable` is an old DOM API.\n    // tslint:disable-next-line:no-any\n    if (el.isContentEditable) {\n        return false;\n    }\n    return true;\n}\n/**\n * Whether an event has a modifier key activated.\n * @param e The event.\n * @return True, if a modifier key is activated.\n */\nfunction hasModifierKey(e) {\n    return (\n    // `ctrlKey` is an old DOM API.\n    // tslint:disable-next-line:no-any\n    e.ctrlKey ||\n        // `shiftKey` is an old DOM API.\n        // tslint:disable-next-line:no-any\n        e.shiftKey ||\n        // `altKey` is an old DOM API.\n        // tslint:disable-next-line:no-any\n        e.altKey ||\n        // `metaKey` is an old DOM API.\n        // tslint:disable-next-line:no-any\n        e.metaKey);\n}\n/**\n * Determines and returns whether the given event has a target that already\n * has event handlers attached because it is a native HTML control. Used to\n * determine if preventDefault should be called when isActionKeyEvent is true.\n * @param e The event.\n * @return If preventDefault should be called.\n */\nfunction shouldCallPreventDefaultOnNativeHtmlControl(e) {\n    const el = getTarget(e);\n    const tagName = el.tagName.toUpperCase();\n    const role = (el.getAttribute('role') || '').toUpperCase();\n    if (tagName === 'BUTTON' || role === 'BUTTON') {\n        return true;\n    }\n    if (!isNativeHTMLControl(el)) {\n        return false;\n    }\n    if (tagName === 'A') {\n        return false;\n    }\n    /**\n     * Fix for physical d-pads on feature phone platforms; the native event\n     * (ie. isTrusted: true) needs to fire to show the OPTION list. See\n     * b/135288469 for more info.\n     */\n    if (tagName === 'SELECT') {\n        return false;\n    }\n    if (processSpace(el)) {\n        return false;\n    }\n    if (isTextControl(el)) {\n        return false;\n    }\n    return true;\n}\n/**\n * Determines and returns whether the given event acts like a regular DOM click,\n * and should be handled instead of the click.  If this returns true, the caller\n * will call preventDefault() to prevent a possible duplicate event.\n * This is represented by a keypress (keydown on Gecko browsers) on Enter or\n * Space key.\n * @param e The event.\n * @return True, if the event emulates a DOM click.\n */\nfunction isActionKeyEvent(e) {\n    let key = \n    // `which` is an old DOM API.\n    // tslint:disable-next-line:no-any\n    e.which ||\n        // `keyCode` is an old DOM API.\n        // tslint:disable-next-line:no-any\n        e.keyCode;\n    if (!key && e.key) {\n        key = ACTION_KEY_TO_KEYCODE[e.key];\n    }\n    if (isWebKit && key === KeyCode.MAC_ENTER) {\n        key = KeyCode.ENTER;\n    }\n    if (key !== KeyCode.ENTER && key !== KeyCode.SPACE) {\n        return false;\n    }\n    const el = getTarget(e);\n    if (e.type !== EventType.KEYDOWN || !isValidActionKeyTarget(el) || hasModifierKey(e)) {\n        return false;\n    }\n    // For <input type=\"checkbox\">, we must only handle the browser's native click\n    // event, so that the browser can toggle the checkbox.\n    if (processSpace(el) && key === KeyCode.SPACE) {\n        return false;\n    }\n    // If this element is non-focusable, ignore stray keystrokes (b/18337209)\n    // Sscreen readers can move without tab focus, so any tabIndex is focusable.\n    // See B/21809604\n    if (!isFocusable(el)) {\n        return false;\n    }\n    const type = (el.getAttribute('role') ||\n        el.type ||\n        el.tagName).toUpperCase();\n    const isSpecificTriggerKey = IDENTIFIER_TO_KEY_TRIGGER_MAPPING[type] % key === 0;\n    const isDefaultTriggerKey = !(type in IDENTIFIER_TO_KEY_TRIGGER_MAPPING) && key === KeyCode.ENTER;\n    const hasType = el.tagName.toUpperCase() !== 'INPUT' || !!el.type;\n    return (isSpecificTriggerKey || isDefaultTriggerKey) && hasType;\n}\n/**\n * Checks whether a DOM element can receive keyboard focus.\n * This code is based on goog.dom.isFocusable, but simplified since we shouldn't\n * care about visibility if we're already handling a keyboard event.\n */\nfunction isFocusable(el) {\n    return ((el.tagName in NATIVELY_FOCUSABLE_ELEMENTS || hasSpecifiedTabIndex(el)) &&\n        !el.disabled);\n}\n/**\n * @param element Element to check.\n * @return Whether the element has a specified tab index.\n */\nfunction hasSpecifiedTabIndex(element) {\n    // IE returns 0 for an unset tabIndex, so we must use getAttributeNode(),\n    // which returns an object with a 'specified' property if tabIndex is\n    // specified.  This works on other browsers, too.\n    const attrNode = element.getAttributeNode('tabindex'); // Must be lowercase!\n    return attrNode != null && attrNode.specified;\n}\n/** Element tagnames that are focusable by default. */\nconst NATIVELY_FOCUSABLE_ELEMENTS = {\n    'A': 1,\n    'INPUT': 1,\n    'TEXTAREA': 1,\n    'SELECT': 1,\n    'BUTTON': 1,\n};\n/** @return True, if the Space key was pressed. */\nfunction isSpaceKeyEvent(e) {\n    const key = \n    // `which` is an old DOM API.\n    // tslint:disable-next-line:no-any\n    e.which ||\n        // `keyCode` is an old DOM API.\n        // tslint:disable-next-line:no-any\n        e.keyCode;\n    const el = getTarget(e);\n    const elementName = (el.type || el.tagName).toUpperCase();\n    return key === KeyCode.SPACE && elementName !== 'CHECKBOX';\n}\n/**\n * Determines whether the event corresponds to a non-bubbling mouse\n * event type (mouseenter, mouseleave, pointerenter, and pointerleave).\n *\n * During mouseover (mouseenter) and pointerover (pointerenter), the\n * relatedTarget is the element being entered from. During mouseout (mouseleave)\n * and pointerout (pointerleave), the relatedTarget is the element being exited\n * to.\n *\n * In both cases, if relatedTarget is outside target, then the corresponding\n * special event has occurred, otherwise it hasn't.\n *\n * @param e The mouseover/mouseout event.\n * @param type The type of the mouse special event.\n * @param element The element on which the jsaction for the\n *     mouseenter/mouseleave event is defined.\n * @return True if the event is a mouseenter/mouseleave event.\n */\nfunction isMouseSpecialEvent(e, type, element) {\n    // `relatedTarget` is an old DOM API.\n    // tslint:disable-next-line:no-any\n    const related = e.relatedTarget;\n    return (((e.type === EventType.MOUSEOVER && type === EventType.MOUSEENTER) ||\n        (e.type === EventType.MOUSEOUT && type === EventType.MOUSELEAVE) ||\n        (e.type === EventType.POINTEROVER && type === EventType.POINTERENTER) ||\n        (e.type === EventType.POINTEROUT && type === EventType.POINTERLEAVE)) &&\n        (!related || (related !== element && !element.contains(related))));\n}\n/**\n * Creates a new EventLike object for a mouseenter/mouseleave event that's\n * derived from the original corresponding mouseover/mouseout event.\n * @param e The event.\n * @param target The element on which the jsaction for the mouseenter/mouseleave\n *     event is defined.\n * @return A modified event-like object copied from the event object passed into\n *     this function.\n */\nfunction createMouseSpecialEvent(e, target) {\n    // We have to create a copy of the event object because we need to mutate\n    // its fields. We do this for the special mouse events because the event\n    // target needs to be retargeted to the action element rather than the real\n    // element (since we are simulating the special mouse events with mouseover/\n    // mouseout).\n    //\n    // Since we're making a copy anyways, we might as well attempt to convert\n    // this event into a pseudo-real mouseenter/mouseleave event by adjusting\n    // its type.\n    //\n    // tslint:disable-next-line:no-any\n    const copy = {};\n    for (const property in e) {\n        if (property === 'srcElement' || property === 'target') {\n            continue;\n        }\n        const key = property;\n        // Making a copy requires iterating through all properties of `Event`.\n        // tslint:disable-next-line:no-dict-access-on-struct-type\n        const value = e[key];\n        if (typeof value === 'function') {\n            continue;\n        }\n        // Value should be the expected type, but the value of `key` is not known\n        // statically.\n        // tslint:disable-next-line:no-any\n        copy[key] = value;\n    }\n    if (e.type === EventType.MOUSEOVER) {\n        copy['type'] = EventType.MOUSEENTER;\n    }\n    else if (e.type === EventType.MOUSEOUT) {\n        copy['type'] = EventType.MOUSELEAVE;\n    }\n    else if (e.type === EventType.POINTEROVER) {\n        copy['type'] = EventType.POINTERENTER;\n    }\n    else {\n        copy['type'] = EventType.POINTERLEAVE;\n    }\n    copy['target'] = copy['srcElement'] = target;\n    copy['bubbles'] = false;\n    return copy;\n}\n/**\n * Returns touch data extracted from the touch event: clientX, clientY, screenX\n * and screenY. If the event has no touch information at all, the returned\n * value is null.\n *\n * The fields of this Object are unquoted.\n *\n * @param event A touch event.\n */\nfunction getTouchData(event) {\n    const touch = (event.changedTouches && event.changedTouches[0]) || (event.touches && event.touches[0]);\n    if (!touch) {\n        return null;\n    }\n    return {\n        clientX: touch.clientX,\n        clientY: touch.clientY,\n        screenX: touch.screenX,\n        screenY: touch.screenY,\n    };\n}\n/**\n * Creates a new EventLike object for a \"click\" event that's derived from the\n * original corresponding \"touchend\" event for a fast-click implementation.\n *\n * It takes a touch event, adds common fields found in a click event and\n * changes the type to 'click', so that the resulting event looks more like\n * a real click event.\n *\n * @param event A touch event.\n * @return A modified event-like object copied from the event object passed into\n *     this function.\n */\nfunction recreateTouchEventAsClick(event) {\n    const click = {};\n    click['originalEventType'] = event.type;\n    click['type'] = EventType.CLICK;\n    for (const property in event) {\n        if (property === 'type' || property === 'srcElement') {\n            continue;\n        }\n        const key = property;\n        // Making a copy requires iterating through all properties of `TouchEvent`.\n        // tslint:disable-next-line:no-dict-access-on-struct-type\n        const value = event[key];\n        if (typeof value === 'function') {\n            continue;\n        }\n        // Value should be the expected type, but the value of `key` is not known\n        // statically.\n        // tslint:disable-next-line:no-any\n        click[key] = value;\n    }\n    // Ensure that the event has the most recent timestamp. This timestamp\n    // may be used in the future to validate or cancel subsequent click events.\n    click['timeStamp'] = Date.now();\n    // Emulate preventDefault and stopPropagation behavior\n    click['defaultPrevented'] = false;\n    click['preventDefault'] = syntheticPreventDefault;\n    click['_propagationStopped'] = false;\n    click['stopPropagation'] = syntheticStopPropagation;\n    // Emulate click coordinates using touch info\n    const touch = getTouchData(event);\n    if (touch) {\n        click['clientX'] = touch.clientX;\n        click['clientY'] = touch.clientY;\n        click['screenX'] = touch.screenX;\n        click['screenY'] = touch.screenY;\n    }\n    return click;\n}\n/**\n * An implementation of \"preventDefault\" for a synthesized event. Simply\n * sets \"defaultPrevented\" property to true.\n */\nfunction syntheticPreventDefault() {\n    this.defaultPrevented = true;\n}\n/**\n * An implementation of \"stopPropagation\" for a synthesized event. It simply\n * sets a synthetic non-standard \"_propagationStopped\" property to true.\n */\nfunction syntheticStopPropagation() {\n    this._propagationStopped = true;\n}\n/**\n * Mapping of KeyboardEvent.key values to\n * KeyCode values.\n */\nconst ACTION_KEY_TO_KEYCODE = {\n    'Enter': KeyCode.ENTER,\n    ' ': KeyCode.SPACE,\n};\n/**\n * Mapping of HTML element identifiers (ARIA role, type, or tagName) to the\n * keys (enter and/or space) that should activate them. A value of zero means\n * that both should activate them.\n */\nconst IDENTIFIER_TO_KEY_TRIGGER_MAPPING = {\n    'A': KeyCode.ENTER,\n    'BUTTON': 0,\n    'CHECKBOX': KeyCode.SPACE,\n    'COMBOBOX': KeyCode.ENTER,\n    'FILE': 0,\n    'GRIDCELL': KeyCode.ENTER,\n    'LINK': KeyCode.ENTER,\n    'LISTBOX': KeyCode.ENTER,\n    'MENU': 0,\n    'MENUBAR': 0,\n    'MENUITEM': 0,\n    'MENUITEMCHECKBOX': 0,\n    'MENUITEMRADIO': 0,\n    'OPTION': 0,\n    'RADIO': KeyCode.SPACE,\n    'RADIOGROUP': KeyCode.SPACE,\n    'RESET': 0,\n    'SUBMIT': 0,\n    'SWITCH': KeyCode.SPACE,\n    'TAB': 0,\n    'TREE': KeyCode.ENTER,\n    'TREEITEM': KeyCode.ENTER,\n};\n/**\n * Returns whether or not to process space based on the type of the element;\n * checks to make sure that type is not null.\n * @param element The element.\n * @return Whether or not to process space based on type.\n */\nfunction processSpace(element) {\n    const type = (element.getAttribute('type') || element.tagName).toUpperCase();\n    return type in PROCESS_SPACE;\n}\n/**\n * Returns whether or not the given element is a text control.\n * @param el The element.\n * @return Whether or not the given element is a text control.\n */\nfunction isTextControl(el) {\n    const type = (el.getAttribute('type') || el.tagName).toUpperCase();\n    return type in TEXT_CONTROLS;\n}\n/**\n * Returns if the given element is a native HTML control.\n * @param el The element.\n * @return If the given element is a native HTML control.\n */\nfunction isNativeHTMLControl(el) {\n    return el.tagName.toUpperCase() in NATIVE_HTML_CONTROLS;\n}\n/**\n * Returns if the given element is natively activatable. Browsers emit click\n * events for natively activatable elements, even when activated via keyboard.\n * For these elements, we don't need to raise a11y click events.\n * @param el The element.\n * @return If the given element is a native HTML control.\n */\nfunction isNativelyActivatable(el) {\n    return (el.tagName.toUpperCase() === 'BUTTON' ||\n        (!!el.type && el.type.toUpperCase() === 'FILE'));\n}\n/**\n * HTML <input> types (not ARIA roles) which will auto-trigger a click event for\n * the Space key, with side-effects. We will not call preventDefault if space is\n * pressed, nor will we raise a11y click events.  For all other elements, we can\n * suppress the default event (which has no desired side-effects) and handle the\n * keydown ourselves.\n */\nconst PROCESS_SPACE = {\n    'CHECKBOX': true,\n    'FILE': true,\n    'OPTION': true,\n    'RADIO': true,\n};\n/** TagNames and Input types for which to not process enter/space as click. */\nconst TEXT_CONTROLS = {\n    'COLOR': true,\n    'DATE': true,\n    'DATETIME': true,\n    'DATETIME-LOCAL': true,\n    'EMAIL': true,\n    'MONTH': true,\n    'NUMBER': true,\n    'PASSWORD': true,\n    'RANGE': true,\n    'SEARCH': true,\n    'TEL': true,\n    'TEXT': true,\n    'TEXTAREA': true,\n    'TIME': true,\n    'URL': true,\n    'WEEK': true,\n};\n/** TagNames that are native HTML controls. */\nconst NATIVE_HTML_CONTROLS = {\n    'A': true,\n    'AREA': true,\n    'BUTTON': true,\n    'DIALOG': true,\n    'IMG': true,\n    'INPUT': true,\n    'LINK': true,\n    'MENU': true,\n    'OPTGROUP': true,\n    'OPTION': true,\n    'PROGRESS': true,\n    'SELECT': true,\n    'TEXTAREA': true,\n};\n/** Exported for testing. */\nconst testing = {\n    setIsMac(value) {\n        isMac = value;\n    },\n};\n\n/**\n * Whether the user agent is running on iOS.\n */\nconst isIos = typeof navigator !== 'undefined' && /iPhone|iPad|iPod/.test(navigator.userAgent);\n/**\n * A class representing a container node and all the event handlers\n * installed on it. Used so that handlers can be cleaned up if the\n * container is removed from the contract.\n */\nclass EventContractContainer {\n    /**\n     * @param element The container Element.\n     */\n    constructor(element) {\n        this.element = element;\n        /**\n         * Array of event handlers and their corresponding event types that are\n         * installed on this container.\n         *\n         */\n        this.handlerInfos = [];\n    }\n    /**\n     * Installs the provided installer on the element owned by this container,\n     * and maintains a reference to resulting handler in order to remove it\n     * later if desired.\n     */\n    addEventListener(eventType, getHandler) {\n        // In iOS, event bubbling doesn't happen automatically in any DOM element,\n        // unless it has an onclick attribute or DOM event handler attached to it.\n        // This breaks JsAction in some cases. See \"Making Elements Clickable\"\n        // section at http://goo.gl/2VoGnB.\n        //\n        // A workaround for this issue is to change the CSS cursor style to 'pointer'\n        // for the container element, which magically turns on event bubbling. This\n        // solution is described in the comments section at http://goo.gl/6pEO1z.\n        //\n        // We use a navigator.userAgent check here as this problem is present both\n        // on Mobile Safari and thin WebKit wrappers, such as Chrome for iOS.\n        if (isIos) {\n            this.element.style.cursor = 'pointer';\n        }\n        this.handlerInfos.push(addEventListener(this.element, eventType, getHandler(this.element)));\n    }\n    /**\n     * Removes all the handlers installed on this container.\n     */\n    cleanUp() {\n        for (let i = 0; i < this.handlerInfos.length; i++) {\n            removeEventListener(this.element, this.handlerInfos[i]);\n        }\n        this.handlerInfos = [];\n    }\n}\n\nconst Char = {\n    /**\n     * The separator between the namespace and the action name in the\n     * jsaction attribute value.\n     */\n    NAMESPACE_ACTION_SEPARATOR: '.',\n    /**\n     * The separator between the event name and action in the jsaction\n     * attribute value.\n     */\n    EVENT_ACTION_SEPARATOR: ':',\n};\n\n/** Added for readability when accessing stable property names. */\nfunction getEventType(eventInfo) {\n    return eventInfo.eventType;\n}\n/** Added for readability when accessing stable property names. */\nfunction setEventType(eventInfo, eventType) {\n    eventInfo.eventType = eventType;\n}\n/** Added for readability when accessing stable property names. */\nfunction getEvent(eventInfo) {\n    return eventInfo.event;\n}\n/** Added for readability when accessing stable property names. */\nfunction setEvent(eventInfo, event) {\n    eventInfo.event = event;\n}\n/** Added for readability when accessing stable property names. */\nfunction getTargetElement(eventInfo) {\n    return eventInfo.targetElement;\n}\n/** Added for readability when accessing stable property names. */\nfunction setTargetElement(eventInfo, targetElement) {\n    eventInfo.targetElement = targetElement;\n}\n/** Added for readability when accessing stable property names. */\nfunction getContainer(eventInfo) {\n    return eventInfo.eic;\n}\n/** Added for readability when accessing stable property names. */\nfunction setContainer(eventInfo, container) {\n    eventInfo.eic = container;\n}\n/** Added for readability when accessing stable property names. */\nfunction getTimestamp(eventInfo) {\n    return eventInfo.timeStamp;\n}\n/** Added for readability when accessing stable property names. */\nfunction setTimestamp(eventInfo, timestamp) {\n    eventInfo.timeStamp = timestamp;\n}\n/** Added for readability when accessing stable property names. */\nfunction getAction(eventInfo) {\n    return eventInfo.eia;\n}\n/** Added for readability when accessing stable property names. */\nfunction setAction(eventInfo, actionName, actionElement) {\n    eventInfo.eia = [actionName, actionElement];\n}\n/** Added for readability when accessing stable property names. */\nfunction unsetAction(eventInfo) {\n    eventInfo.eia = undefined;\n}\n/** Added for readability when accessing stable property names. */\nfunction getActionName(actionInfo) {\n    return actionInfo[0];\n}\n/** Added for readability when accessing stable property names. */\nfunction getActionElement(actionInfo) {\n    return actionInfo[1];\n}\n/** Added for readability when accessing stable property names. */\nfunction getIsReplay(eventInfo) {\n    return eventInfo.eirp;\n}\n/** Added for readability when accessing stable property names. */\nfunction setIsReplay(eventInfo, replay) {\n    eventInfo.eirp = replay;\n}\n/** Added for readability when accessing stable property names. */\nfunction getA11yClickKey(eventInfo) {\n    return eventInfo.eiack;\n}\n/** Added for readability when accessing stable property names. */\nfunction setA11yClickKey(eventInfo, a11yClickKey) {\n    eventInfo.eiack = a11yClickKey;\n}\n/** Added for readability when accessing stable property names. */\nfunction getResolved(eventInfo) {\n    return eventInfo.eir;\n}\n/** Added for readability when accessing stable property names. */\nfunction setResolved(eventInfo, resolved) {\n    eventInfo.eir = resolved;\n}\n/** Clones an `EventInfo` */\nfunction cloneEventInfo(eventInfo) {\n    return {\n        eventType: eventInfo.eventType,\n        event: eventInfo.event,\n        targetElement: eventInfo.targetElement,\n        eic: eventInfo.eic,\n        eia: eventInfo.eia,\n        timeStamp: eventInfo.timeStamp,\n        eirp: eventInfo.eirp,\n        eiack: eventInfo.eiack,\n        eir: eventInfo.eir,\n    };\n}\n/**\n * Utility function for creating an `EventInfo`.\n *\n * This can be used from code-size sensitive compilation units, as taking\n * parameters vs. an `Object` literal reduces code size.\n */\nfunction createEventInfoFromParameters(eventType, event, targetElement, container, timestamp, action, isReplay, a11yClickKey) {\n    return {\n        eventType,\n        event,\n        targetElement,\n        eic: container,\n        timeStamp: timestamp,\n        eia: action,\n        eirp: isReplay,\n        eiack: a11yClickKey,\n    };\n}\n/**\n * Utility function for creating an `EventInfo`.\n *\n * This should be used in compilation units that are less sensitive to code\n * size.\n */\nfunction createEventInfo({ eventType, event, targetElement, container, timestamp, action, isReplay, a11yClickKey, }) {\n    return {\n        eventType,\n        event,\n        targetElement,\n        eic: container,\n        timeStamp: timestamp,\n        eia: action ? [action.name, action.element] : undefined,\n        eirp: isReplay,\n        eiack: a11yClickKey,\n    };\n}\n/**\n * Utility class around an `EventInfo`.\n *\n * This should be used in compilation units that are less sensitive to code\n * size.\n */\nclass EventInfoWrapper {\n    constructor(eventInfo) {\n        this.eventInfo = eventInfo;\n    }\n    getEventType() {\n        return getEventType(this.eventInfo);\n    }\n    setEventType(eventType) {\n        setEventType(this.eventInfo, eventType);\n    }\n    getEvent() {\n        return getEvent(this.eventInfo);\n    }\n    setEvent(event) {\n        setEvent(this.eventInfo, event);\n    }\n    getTargetElement() {\n        return getTargetElement(this.eventInfo);\n    }\n    setTargetElement(targetElement) {\n        setTargetElement(this.eventInfo, targetElement);\n    }\n    getContainer() {\n        return getContainer(this.eventInfo);\n    }\n    setContainer(container) {\n        setContainer(this.eventInfo, container);\n    }\n    getTimestamp() {\n        return getTimestamp(this.eventInfo);\n    }\n    setTimestamp(timestamp) {\n        setTimestamp(this.eventInfo, timestamp);\n    }\n    getAction() {\n        const action = getAction(this.eventInfo);\n        if (!action)\n            return undefined;\n        return {\n            name: action[0],\n            element: action[1],\n        };\n    }\n    setAction(action) {\n        if (!action) {\n            unsetAction(this.eventInfo);\n            return;\n        }\n        setAction(this.eventInfo, action.name, action.element);\n    }\n    getIsReplay() {\n        return getIsReplay(this.eventInfo);\n    }\n    setIsReplay(replay) {\n        setIsReplay(this.eventInfo, replay);\n    }\n    getResolved() {\n        return getResolved(this.eventInfo);\n    }\n    setResolved(resolved) {\n        setResolved(this.eventInfo, resolved);\n    }\n    clone() {\n        return new EventInfoWrapper(cloneEventInfo(this.eventInfo));\n    }\n}\n\n/**\n * Since maps from event to action are immutable we can use a single map\n * to represent the empty map.\n */\nconst EMPTY_ACTION_MAP = {};\n/**\n * This regular expression matches a semicolon.\n */\nconst REGEXP_SEMICOLON = /\\s*;\\s*/;\n/** If no event type is defined, defaults to `click`. */\nconst DEFAULT_EVENT_TYPE = EventType.CLICK;\n/** Resolves actions for Events. */\nclass ActionResolver {\n    constructor({ syntheticMouseEventSupport = false, clickModSupport = true, } = {}) {\n        this.a11yClickSupport = false;\n        this.clickModSupport = true;\n        this.updateEventInfoForA11yClick = undefined;\n        this.preventDefaultForA11yClick = undefined;\n        this.populateClickOnlyAction = undefined;\n        this.syntheticMouseEventSupport = syntheticMouseEventSupport;\n        this.clickModSupport = clickModSupport;\n    }\n    resolveEventType(eventInfo) {\n        // We distinguish modified and plain clicks in order to support the\n        // default browser behavior of modified clicks on links; usually to\n        // open the URL of the link in new tab or new window on ctrl/cmd\n        // click. A DOM 'click' event is mapped to the jsaction 'click'\n        // event iff there is no modifier present on the event. If there is\n        // a modifier, it's mapped to 'clickmod' instead.\n        //\n        // It's allowed to omit the event in the jsaction attribute. In that\n        // case, 'click' is assumed. Thus the following two are equivalent:\n        //\n        //   <a href=\"someurl\" jsaction=\"gna.fu\">\n        //   <a href=\"someurl\" jsaction=\"click:gna.fu\">\n        //\n        // For unmodified clicks, EventContract invokes the jsaction\n        // 'gna.fu'. For modified clicks, EventContract won't find a\n        // suitable action and leave the event to be handled by the\n        // browser.\n        //\n        // In order to also invoke a jsaction handler for a modifier click,\n        // 'clickmod' needs to be used:\n        //\n        //   <a href=\"someurl\" jsaction=\"clickmod:gna.fu\">\n        //\n        // EventContract invokes the jsaction 'gna.fu' for modified\n        // clicks. Unmodified clicks are left to the browser.\n        //\n        // In order to set up the event contract to handle both clickonly and\n        // clickmod, only addEvent(EventType.CLICK) is necessary.\n        //\n        // In order to set up the event contract to handle click,\n        // addEvent() is necessary for CLICK, KEYDOWN, and KEYPRESS event types.  If\n        // a11y click support is enabled, addEvent() will set up the appropriate key\n        // event handler automatically.\n        if (this.clickModSupport &&\n            getEventType(eventInfo) === EventType.CLICK &&\n            isModifiedClickEvent(getEvent(eventInfo))) {\n            setEventType(eventInfo, EventType.CLICKMOD);\n        }\n        else if (this.a11yClickSupport) {\n            this.updateEventInfoForA11yClick(eventInfo);\n        }\n    }\n    resolveAction(eventInfo) {\n        if (getResolved(eventInfo)) {\n            return;\n        }\n        this.populateAction(eventInfo, getTargetElement(eventInfo));\n        setResolved(eventInfo, true);\n    }\n    resolveParentAction(eventInfo) {\n        const action = getAction(eventInfo);\n        const actionElement = action && getActionElement(action);\n        unsetAction(eventInfo);\n        const parentNode = actionElement && this.getParentNode(actionElement);\n        if (!parentNode) {\n            return;\n        }\n        this.populateAction(eventInfo, parentNode);\n    }\n    /**\n     * Searches for a jsaction that the DOM event maps to and creates an\n     * object containing event information used for dispatching by\n     * jsaction.Dispatcher. This method populates the `action` and `actionElement`\n     * fields of the EventInfo object passed in by finding the first\n     * jsaction attribute above the target Node of the event, and below\n     * the container Node, that specifies a jsaction for the event\n     * type. If no such jsaction is found, then action is undefined.\n     *\n     * @param eventInfo `EventInfo` to set `action` and `actionElement` if an\n     *    action is found on any `Element` in the path of the `Event`.\n     */\n    populateAction(eventInfo, currentTarget) {\n        let actionElement = currentTarget;\n        while (actionElement && actionElement !== getContainer(eventInfo)) {\n            if (actionElement.nodeType === Node.ELEMENT_NODE) {\n                this.populateActionOnElement(actionElement, eventInfo);\n            }\n            if (getAction(eventInfo)) {\n                // An event is handled by at most one jsaction. Thus we stop at the\n                // first matching jsaction specified in a jsaction attribute up the\n                // ancestor chain of the event target node.\n                break;\n            }\n            actionElement = this.getParentNode(actionElement);\n        }\n        const action = getAction(eventInfo);\n        if (!action) {\n            // No action found.\n            return;\n        }\n        if (this.a11yClickSupport) {\n            this.preventDefaultForA11yClick(eventInfo);\n        }\n        // We attempt to handle the mouseenter/mouseleave events here by\n        // detecting whether the mouseover/mouseout events correspond to\n        // entering/leaving an element.\n        if (this.syntheticMouseEventSupport) {\n            if (getEventType(eventInfo) === EventType.MOUSEENTER ||\n                getEventType(eventInfo) === EventType.MOUSELEAVE ||\n                getEventType(eventInfo) === EventType.POINTERENTER ||\n                getEventType(eventInfo) === EventType.POINTERLEAVE) {\n                // We attempt to handle the mouseenter/mouseleave events here by\n                // detecting whether the mouseover/mouseout events correspond to\n                // entering/leaving an element.\n                if (isMouseSpecialEvent(getEvent(eventInfo), getEventType(eventInfo), getActionElement(action))) {\n                    // If both mouseover/mouseout and mouseenter/mouseleave events are\n                    // enabled, two separate handlers for mouseover/mouseout are\n                    // registered. Both handlers will see the same event instance\n                    // so we create a copy to avoid interfering with the dispatching of\n                    // the mouseover/mouseout event.\n                    const copiedEvent = createMouseSpecialEvent(getEvent(eventInfo), getActionElement(action));\n                    setEvent(eventInfo, copiedEvent);\n                    // Since the mouseenter/mouseleave events do not bubble, the target\n                    // of the event is technically the `actionElement` (the node with the\n                    // `jsaction` attribute)\n                    setTargetElement(eventInfo, getActionElement(action));\n                }\n                else {\n                    unsetAction(eventInfo);\n                }\n            }\n        }\n    }\n    /**\n     * Walk to the parent node, unless the node has a different owner in\n     * which case we walk to the owner. Attempt to walk to host of a\n     * shadow root if needed.\n     */\n    getParentNode(element) {\n        const owner = element[Property.OWNER];\n        if (owner) {\n            return owner;\n        }\n        const parentNode = element.parentNode;\n        if (parentNode?.nodeName === '#document-fragment') {\n            return parentNode?.host ?? null;\n        }\n        return parentNode;\n    }\n    /**\n     * Accesses the jsaction map on a node and retrieves the name of the\n     * action the given event is mapped to, if any. It parses the\n     * attribute value and stores it in a property on the node for\n     * subsequent retrieval without re-parsing and re-accessing the\n     * attribute.\n     *\n     * @param actionElement The DOM node to retrieve the jsaction map from.\n     * @param eventInfo `EventInfo` to set `action` and `actionElement` if an\n     *    action is found on the `actionElement`.\n     */\n    populateActionOnElement(actionElement, eventInfo) {\n        const actionMap = this.parseActions(actionElement);\n        const actionName = actionMap[getEventType(eventInfo)];\n        if (actionName !== undefined) {\n            setAction(eventInfo, actionName, actionElement);\n        }\n        if (this.a11yClickSupport) {\n            this.populateClickOnlyAction(actionElement, eventInfo, actionMap);\n        }\n    }\n    /**\n     * Parses and caches an element's jsaction element into a map.\n     *\n     * This is primarily for internal use.\n     *\n     * @param actionElement The DOM node to retrieve the jsaction map from.\n     * @return Map from event to qualified name of the jsaction bound to it.\n     */\n    parseActions(actionElement) {\n        let actionMap = get(actionElement);\n        if (!actionMap) {\n            const jsactionAttribute = actionElement.getAttribute(Attribute.JSACTION);\n            if (!jsactionAttribute) {\n                actionMap = EMPTY_ACTION_MAP;\n                set(actionElement, actionMap);\n            }\n            else {\n                actionMap = getParsed(jsactionAttribute);\n                if (!actionMap) {\n                    actionMap = {};\n                    const values = jsactionAttribute.split(REGEXP_SEMICOLON);\n                    for (let idx = 0; idx < values.length; idx++) {\n                        const value = values[idx];\n                        if (!value) {\n                            continue;\n                        }\n                        const colon = value.indexOf(Char.EVENT_ACTION_SEPARATOR);\n                        const hasColon = colon !== -1;\n                        const type = hasColon ? value.substr(0, colon).trim() : DEFAULT_EVENT_TYPE;\n                        const action = hasColon ? value.substr(colon + 1).trim() : value;\n                        actionMap[type] = action;\n                    }\n                    setParsed(jsactionAttribute, actionMap);\n                }\n                set(actionElement, actionMap);\n            }\n        }\n        return actionMap;\n    }\n    addA11yClickSupport(updateEventInfoForA11yClick, preventDefaultForA11yClick, populateClickOnlyAction) {\n        this.a11yClickSupport = true;\n        this.updateEventInfoForA11yClick = updateEventInfoForA11yClick;\n        this.preventDefaultForA11yClick = preventDefaultForA11yClick;\n        this.populateClickOnlyAction = populateClickOnlyAction;\n    }\n}\n\n/**\n * @fileoverview An enum to control who can call certain jsaction APIs.\n */\nvar Restriction;\n(function (Restriction) {\n    Restriction[Restriction[\"I_AM_THE_JSACTION_FRAMEWORK\"] = 0] = \"I_AM_THE_JSACTION_FRAMEWORK\";\n})(Restriction || (Restriction = {}));\n\n/**\n * Receives a DOM event, determines the jsaction associated with the source\n * element of the DOM event, and invokes the handler associated with the\n * jsaction.\n */\nclass Dispatcher {\n    /**\n     * Options are:\n     *   - `eventReplayer`: When the event contract dispatches replay events\n     *      to the Dispatcher, the Dispatcher collects them and in the next tick\n     *      dispatches them to the `eventReplayer`. Defaults to dispatching to `dispatchDelegate`.\n     * @param dispatchDelegate A function that should handle dispatching an `EventInfoWrapper` to handlers.\n     */\n    constructor(dispatchDelegate, { actionResolver, eventReplayer, } = {}) {\n        this.dispatchDelegate = dispatchDelegate;\n        /** Whether the event replay is scheduled. */\n        this.eventReplayScheduled = false;\n        /** The queue of events. */\n        this.replayEventInfoWrappers = [];\n        this.actionResolver = actionResolver;\n        this.eventReplayer = eventReplayer;\n    }\n    /**\n     * Receives an event or the event queue from the EventContract. The event\n     * queue is copied and it attempts to replay.\n     * If event info is passed in it looks for an action handler that can handle\n     * the given event.  If there is no handler registered queues the event and\n     * checks if a loader is registered for the given namespace. If so, calls it.\n     *\n     * Alternatively, if in global dispatch mode, calls all registered global\n     * handlers for the appropriate event type.\n     *\n     * The three functionalities of this call are deliberately not split into\n     * three methods (and then declared as an abstract interface), because the\n     * interface is used by EventContract, which lives in a different jsbinary.\n     * Therefore the interface between the three is defined entirely in terms that\n     * are invariant under jscompiler processing (Function and Array, as opposed\n     * to a custom type with method names).\n     *\n     * @param eventInfo The info for the event that triggered this call or the\n     *     queue of events from EventContract.\n     */\n    dispatch(eventInfo) {\n        const eventInfoWrapper = new EventInfoWrapper(eventInfo);\n        this.actionResolver?.resolveEventType(eventInfo);\n        this.actionResolver?.resolveAction(eventInfo);\n        const action = eventInfoWrapper.getAction();\n        if (action && shouldPreventDefaultBeforeDispatching(action.element, eventInfoWrapper)) {\n            preventDefault(eventInfoWrapper.getEvent());\n        }\n        if (this.eventReplayer && eventInfoWrapper.getIsReplay()) {\n            this.scheduleEventInfoWrapperReplay(eventInfoWrapper);\n            return;\n        }\n        this.dispatchDelegate(eventInfoWrapper);\n    }\n    /**\n     * Schedules an `EventInfoWrapper` for replay. The replaying will happen in its own\n     * stack once the current flow cedes control. This is done to mimic\n     * browser event handling.\n     */\n    scheduleEventInfoWrapperReplay(eventInfoWrapper) {\n        this.replayEventInfoWrappers.push(eventInfoWrapper);\n        if (this.eventReplayScheduled) {\n            return;\n        }\n        this.eventReplayScheduled = true;\n        Promise.resolve().then(() => {\n            this.eventReplayScheduled = false;\n            this.eventReplayer(this.replayEventInfoWrappers);\n        });\n    }\n}\n/**\n * Creates an `EventReplayer` that calls the `replay` function for every `eventInfoWrapper` in\n * the queue.\n */\nfunction createEventReplayer(replay) {\n    return (eventInfoWrappers) => {\n        for (const eventInfoWrapper of eventInfoWrappers) {\n            replay(eventInfoWrapper);\n        }\n    };\n}\n/**\n * Returns true if the default action of this event should be prevented before\n * this event is dispatched.\n */\nfunction shouldPreventDefaultBeforeDispatching(actionElement, eventInfoWrapper) {\n    // Prevent browser from following <a> node links if a jsaction is present\n    // and we are dispatching the action now. Note that the targetElement may be\n    // a child of an anchor that has a jsaction attached. For that reason, we\n    // need to check the actionElement rather than the targetElement.\n    return (actionElement.tagName === 'A' &&\n        (eventInfoWrapper.getEventType() === EventType.CLICK ||\n            eventInfoWrapper.getEventType() === EventType.CLICKMOD));\n}\n/**\n * Registers deferred functionality for an EventContract and a Jsaction\n * Dispatcher.\n */\nfunction registerDispatcher$2(eventContract, dispatcher) {\n    eventContract.ecrd((eventInfo) => {\n        dispatcher.dispatch(eventInfo);\n    }, Restriction.I_AM_THE_JSACTION_FRAMEWORK);\n}\n\n/** An internal symbol used to indicate whether propagation should be stopped or not. */\nconst PROPAGATION_STOPPED_SYMBOL = Symbol.for('propagationStopped');\n/** Extra event phases beyond what the browser provides. */\nconst EventPhase = {\n    REPLAY: 101,\n};\nconst PREVENT_DEFAULT_ERROR_MESSAGE_DETAILS = ' Because event replay occurs after browser dispatch, `preventDefault` would have no ' +\n    'effect. You can check whether an event is being replayed by accessing the event phase: ' +\n    '`event.eventPhase === EventPhase.REPLAY`.';\nconst PREVENT_DEFAULT_ERROR_MESSAGE = `\\`preventDefault\\` called during event replay.`;\nconst COMPOSED_PATH_ERROR_MESSAGE_DETAILS = ' Because event replay occurs after browser ' +\n    'dispatch, `composedPath()` will be empty. Iterate parent nodes from `event.target` or ' +\n    '`event.currentTarget` if you need to check elements in the event path.';\nconst COMPOSED_PATH_ERROR_MESSAGE = `\\`composedPath\\` called during event replay.`;\n/**\n * A dispatcher that uses browser-based `Event` semantics, for example bubbling, `stopPropagation`,\n * `currentTarget`, etc.\n */\nclass EventDispatcher {\n    constructor(dispatchDelegate, clickModSupport = true) {\n        this.dispatchDelegate = dispatchDelegate;\n        this.clickModSupport = clickModSupport;\n        this.actionResolver = new ActionResolver({ clickModSupport });\n        this.dispatcher = new Dispatcher((eventInfoWrapper) => {\n            this.dispatchToDelegate(eventInfoWrapper);\n        }, {\n            actionResolver: this.actionResolver,\n        });\n    }\n    /**\n     * The entrypoint for the `EventContract` dispatch.\n     */\n    dispatch(eventInfo) {\n        this.dispatcher.dispatch(eventInfo);\n    }\n    /** Internal method that does basic disaptching. */\n    dispatchToDelegate(eventInfoWrapper) {\n        if (eventInfoWrapper.getIsReplay()) {\n            prepareEventForReplay(eventInfoWrapper);\n        }\n        prepareEventForBubbling(eventInfoWrapper);\n        while (eventInfoWrapper.getAction()) {\n            prepareEventForDispatch(eventInfoWrapper);\n            // If this is a capture event, ONLY dispatch if the action element is the target.\n            if (isCaptureEventType(eventInfoWrapper.getEventType()) &&\n                eventInfoWrapper.getAction().element !== eventInfoWrapper.getTargetElement()) {\n                return;\n            }\n            this.dispatchDelegate(eventInfoWrapper.getEvent(), eventInfoWrapper.getAction().name);\n            if (propagationStopped(eventInfoWrapper)) {\n                return;\n            }\n            this.actionResolver.resolveParentAction(eventInfoWrapper.eventInfo);\n        }\n    }\n}\nfunction prepareEventForBubbling(eventInfoWrapper) {\n    const event = eventInfoWrapper.getEvent();\n    const originalStopPropagation = eventInfoWrapper.getEvent().stopPropagation.bind(event);\n    const stopPropagation = () => {\n        event[PROPAGATION_STOPPED_SYMBOL] = true;\n        originalStopPropagation();\n    };\n    patchEventInstance(event, 'stopPropagation', stopPropagation);\n    patchEventInstance(event, 'stopImmediatePropagation', stopPropagation);\n}\nfunction propagationStopped(eventInfoWrapper) {\n    const event = eventInfoWrapper.getEvent();\n    return !!event[PROPAGATION_STOPPED_SYMBOL];\n}\nfunction prepareEventForReplay(eventInfoWrapper) {\n    const event = eventInfoWrapper.getEvent();\n    const target = eventInfoWrapper.getTargetElement();\n    const originalPreventDefault = event.preventDefault.bind(event);\n    patchEventInstance(event, 'target', target);\n    patchEventInstance(event, 'eventPhase', EventPhase.REPLAY);\n    patchEventInstance(event, 'preventDefault', () => {\n        originalPreventDefault();\n        throw new Error(PREVENT_DEFAULT_ERROR_MESSAGE + (ngDevMode ? PREVENT_DEFAULT_ERROR_MESSAGE_DETAILS : ''));\n    });\n    patchEventInstance(event, 'composedPath', () => {\n        throw new Error(COMPOSED_PATH_ERROR_MESSAGE + (ngDevMode ? COMPOSED_PATH_ERROR_MESSAGE_DETAILS : ''));\n    });\n}\nfunction prepareEventForDispatch(eventInfoWrapper) {\n    const event = eventInfoWrapper.getEvent();\n    const currentTarget = eventInfoWrapper.getAction()?.element;\n    if (currentTarget) {\n        patchEventInstance(event, 'currentTarget', currentTarget, {\n            // `currentTarget` is going to get reassigned every dispatch.\n            configurable: true,\n        });\n    }\n}\n/**\n * Patch `Event` instance during non-standard `Event` dispatch. This patches just the `Event`\n * instance that the browser created, it does not patch global properties or methods.\n *\n * This is necessary because dispatching an `Event` outside of browser dispatch results in\n * incorrect properties and methods that need to be polyfilled or do not work.\n *\n * JSAction dispatch adds two extra \"phases\" to event dispatch:\n * 1. Event delegation - the event is being dispatched by a delegating event handler on a container\n *    (typically `window.document.documentElement`), to a delegated event handler on some child\n *    element. Certain `Event` properties will be unintuitive, such as `currentTarget`, which would\n *    be the container rather than the child element. Bubbling would also not work. In order to\n *    emulate the browser, these properties and methods on the `Event` are patched.\n * 2. Event replay - the event is being dispatched by the framework once the handlers have been\n *    loaded (during hydration, or late-loaded). Certain `Event` properties can be unset by the\n *    browser because the `Event` is no longer actively being dispatched, such as `target`. Other\n *    methods have no effect because the `Event` has already been dispatched, such as\n *    `preventDefault`. Bubbling would also not work. These properties and methods are patched,\n *    either to fill in information that the browser may have removed, or to throw errors in methods\n *    that no longer behave as expected.\n */\nfunction patchEventInstance(event, property, value, { configurable = false } = {}) {\n    Object.defineProperty(event, property, { value, configurable });\n}\n/**\n * Registers deferred functionality for an EventContract and a Jsaction\n * Dispatcher.\n */\nfunction registerDispatcher$1(eventContract, dispatcher) {\n    eventContract.ecrd((eventInfo) => {\n        dispatcher.dispatch(eventInfo);\n    }, Restriction.I_AM_THE_JSACTION_FRAMEWORK);\n}\n\n/**\n * EarlyEventContract intercepts events in the bubbling phase at the\n * boundary of the document body. This mapping will be passed to the\n * late-loaded EventContract.\n */\nclass EarlyEventContract {\n    constructor(dataContainer = window, container = window.document.documentElement) {\n        this.dataContainer = dataContainer;\n        dataContainer._ejsa = createEarlyJsactionData(container);\n    }\n    /**\n     * Installs a list of event types for container .\n     */\n    addEvents(types, capture) {\n        addEvents(this.dataContainer._ejsa, types, capture);\n    }\n}\n/** Creates an `EarlyJsactionData` object. */\nfunction createEarlyJsactionData(container) {\n    const q = [];\n    const d = (eventInfo) => {\n        q.push(eventInfo);\n    };\n    const h = (event) => {\n        d(createEventInfoFromParameters(event.type, event, event.target, container, Date.now()));\n    };\n    return {\n        c: container,\n        q,\n        et: [],\n        etc: [],\n        d,\n        h,\n    };\n}\n/** Add all the events to the container stored in the `EarlyJsactionData`. */\nfunction addEvents(earlyJsactionData, types, capture) {\n    for (let i = 0; i < types.length; i++) {\n        const eventType = types[i];\n        const eventTypes = capture ? earlyJsactionData.etc : earlyJsactionData.et;\n        eventTypes.push(eventType);\n        earlyJsactionData.c.addEventListener(eventType, earlyJsactionData.h, capture);\n    }\n}\n/** Get the queued `EventInfo` objects that were dispatched before a dispatcher was registered. */\nfunction getQueuedEventInfos(earlyJsactionData) {\n    return earlyJsactionData?.q ?? [];\n}\n/** Register a different dispatcher function on the `EarlyJsactionData`. */\nfunction registerDispatcher(earlyJsactionData, dispatcher) {\n    if (!earlyJsactionData) {\n        return;\n    }\n    earlyJsactionData.d = dispatcher;\n}\n/** Removes all event listener handlers. */\nfunction removeAllEventListeners(earlyJsactionData) {\n    if (!earlyJsactionData) {\n        return;\n    }\n    removeEventListeners(earlyJsactionData.c, earlyJsactionData.et, earlyJsactionData.h);\n    removeEventListeners(earlyJsactionData.c, earlyJsactionData.etc, earlyJsactionData.h, true);\n}\nfunction removeEventListeners(container, eventTypes, earlyEventHandler, capture) {\n    for (let i = 0; i < eventTypes.length; i++) {\n        container.removeEventListener(eventTypes[i], earlyEventHandler, /* useCapture */ capture);\n    }\n}\n\n/**\n * @define Support for the non-bubbling mouseenter and mouseleave events.  This\n * flag can be overridden in a build rule.\n */\nconst MOUSE_SPECIAL_SUPPORT = false;\n\n/**\n * @fileoverview Implements the local event handling contract. This\n * allows DOM objects in a container that enters into this contract to\n * define event handlers which are executed in a local context.\n *\n * One EventContract instance can manage the contract for multiple\n * containers, which are added using the addContainer() method.\n *\n * Events can be registered using the addEvent() method.\n *\n * A Dispatcher is added using the registerDispatcher() method. Until there is\n * a dispatcher, events are queued. The idea is that the EventContract\n * class is inlined in the HTML of the top level page and instantiated\n * right after the start of <body>. The Dispatcher class is contained\n * in the external deferred js, and instantiated and registered with\n * EventContract when the external javascript in the page loads. The\n * external javascript will also register the jsaction handlers, which\n * then pick up the queued events at the time of registration.\n *\n * Since this class is meant to be inlined in the main page HTML, the\n * size of the binary compiled from this file MUST be kept as small as\n * possible and thus its dependencies to a minimum.\n */\n/**\n * EventContract intercepts events in the bubbling phase at the\n * boundary of a container element, and maps them to generic actions\n * which are specified using the custom jsaction attribute in\n * HTML. Behavior of the application is then specified in terms of\n * handler for such actions, cf. jsaction.Dispatcher in dispatcher.js.\n *\n * This has several benefits: (1) No DOM event handlers need to be\n * registered on the specific elements in the UI. (2) The set of\n * events that the application has to handle can be specified in terms\n * of the semantics of the application, rather than in terms of DOM\n * events. (3) Invocation of handlers can be delayed and handlers can\n * be delay loaded in a generic way.\n */\nclass EventContract {\n    static { this.MOUSE_SPECIAL_SUPPORT = MOUSE_SPECIAL_SUPPORT; }\n    constructor(containerManager) {\n        /**\n         * The DOM events which this contract covers. Used to prevent double\n         * registration of event types. The value of the map is the\n         * internally created DOM event handler function that handles the\n         * DOM events. See addEvent().\n         *\n         */\n        this.eventHandlers = {};\n        this.browserEventTypeToExtraEventTypes = {};\n        /**\n         * The dispatcher function. Events are passed to this function for\n         * handling once it was set using the registerDispatcher() method. This is\n         * done because the function is passed from another jsbinary, so passing the\n         * instance and invoking the method here would require to leave the method\n         * unobfuscated.\n         */\n        this.dispatcher = null;\n        /**\n         * The list of suspended `EventInfo` that will be dispatched\n         * as soon as the `Dispatcher` is registered.\n         */\n        this.queuedEventInfos = [];\n        this.containerManager = containerManager;\n    }\n    handleEvent(eventType, event, container) {\n        const eventInfo = createEventInfoFromParameters(\n        /* eventType= */ eventType, \n        /* event= */ event, \n        /* targetElement= */ event.target, \n        /* container= */ container, \n        /* timestamp= */ Date.now());\n        this.handleEventInfo(eventInfo);\n    }\n    /**\n     * Handle an `EventInfo`.\n     */\n    handleEventInfo(eventInfo) {\n        if (!this.dispatcher) {\n            // All events are queued when the dispatcher isn't yet loaded.\n            setIsReplay(eventInfo, true);\n            this.queuedEventInfos?.push(eventInfo);\n            return;\n        }\n        this.dispatcher(eventInfo);\n    }\n    /**\n     * Enables jsaction handlers to be called for the event type given by\n     * name.\n     *\n     * If the event is already registered, this does nothing.\n     *\n     * @param prefixedEventType If supplied, this event is used in\n     *     the actual browser event registration instead of the name that is\n     *     exposed to jsaction. Use this if you e.g. want users to be able\n     *     to subscribe to jsaction=\"transitionEnd:foo\" while the underlying\n     *     event is webkitTransitionEnd in one browser and mozTransitionEnd\n     *     in another.\n     */\n    addEvent(eventType, prefixedEventType) {\n        if (eventType in this.eventHandlers || !this.containerManager) {\n            return;\n        }\n        if (!EventContract.MOUSE_SPECIAL_SUPPORT && MOUSE_SPECIAL_EVENT_TYPES.indexOf(eventType) >= 0) {\n            return;\n        }\n        const eventHandler = (eventType, event, container) => {\n            this.handleEvent(eventType, event, container);\n        };\n        // Store the callback to allow us to replay events.\n        this.eventHandlers[eventType] = eventHandler;\n        const browserEventType = getBrowserEventType(prefixedEventType || eventType);\n        if (browserEventType !== eventType) {\n            const eventTypes = this.browserEventTypeToExtraEventTypes[browserEventType] || [];\n            eventTypes.push(eventType);\n            this.browserEventTypeToExtraEventTypes[browserEventType] = eventTypes;\n        }\n        this.containerManager.addEventListener(browserEventType, (element) => {\n            return (event) => {\n                eventHandler(eventType, event, element);\n            };\n        });\n    }\n    /**\n     * Gets the queued early events and replay them using the appropriate handler\n     * in the provided event contract. Once all the events are replayed, it cleans\n     * up the early contract.\n     */\n    replayEarlyEvents(earlyJsactionData = window._ejsa) {\n        // Check if the early contract is present and prevent calling this function\n        // more than once.\n        if (!earlyJsactionData) {\n            return;\n        }\n        // Replay the early contract events.\n        this.replayEarlyEventInfos(earlyJsactionData.q);\n        // Clean up the early contract.\n        removeAllEventListeners(earlyJsactionData);\n        delete window._ejsa;\n    }\n    /**\n     * Replays all the early `EventInfo` objects, dispatching them through the normal\n     * `EventContract` flow.\n     */\n    replayEarlyEventInfos(earlyEventInfos) {\n        for (let i = 0; i < earlyEventInfos.length; i++) {\n            const earlyEventInfo = earlyEventInfos[i];\n            const eventTypes = this.getEventTypesForBrowserEventType(earlyEventInfo.eventType);\n            for (let j = 0; j < eventTypes.length; j++) {\n                const eventInfo = cloneEventInfo(earlyEventInfo);\n                // EventInfo eventType maps to JSAction's internal event type,\n                // rather than the browser event type.\n                setEventType(eventInfo, eventTypes[j]);\n                this.handleEventInfo(eventInfo);\n            }\n        }\n    }\n    /**\n     * Returns all JSAction event types that have been registered for a given\n     * browser event type.\n     */\n    getEventTypesForBrowserEventType(browserEventType) {\n        const eventTypes = [];\n        if (this.eventHandlers[browserEventType]) {\n            eventTypes.push(browserEventType);\n        }\n        if (this.browserEventTypeToExtraEventTypes[browserEventType]) {\n            eventTypes.push(...this.browserEventTypeToExtraEventTypes[browserEventType]);\n        }\n        return eventTypes;\n    }\n    /**\n     * Returns the event handler function for a given event type.\n     */\n    handler(eventType) {\n        return this.eventHandlers[eventType];\n    }\n    /**\n     * Cleans up the event contract. This resets all of the `EventContract`'s\n     * internal state. Users are responsible for not using this `EventContract`\n     * after it has been cleaned up.\n     */\n    cleanUp() {\n        this.containerManager.cleanUp();\n        this.containerManager = null;\n        this.eventHandlers = {};\n        this.browserEventTypeToExtraEventTypes = {};\n        this.dispatcher = null;\n        this.queuedEventInfos = [];\n    }\n    /**\n     * Register a dispatcher function. Event info of each event mapped to\n     * a jsaction is passed for handling to this callback. The queued\n     * events are passed as well to the dispatcher for later replaying\n     * once the dispatcher is registered. Clears the event queue to null.\n     *\n     * @param dispatcher The dispatcher function.\n     * @param restriction\n     */\n    registerDispatcher(dispatcher, restriction) {\n        this.ecrd(dispatcher, restriction);\n    }\n    /**\n     * Unrenamed alias for registerDispatcher. Necessary for any codebases that\n     * split the `EventContract` and `Dispatcher` code into different compilation\n     * units.\n     */\n    ecrd(dispatcher, restriction) {\n        this.dispatcher = dispatcher;\n        if (this.queuedEventInfos?.length) {\n            for (let i = 0; i < this.queuedEventInfos.length; i++) {\n                this.handleEventInfo(this.queuedEventInfos[i]);\n            }\n            this.queuedEventInfos = null;\n        }\n    }\n}\n\n/**\n * Creates an `EarlyJsactionData`, adds events to it, and populates it on a nested object on\n * the window.\n */\nfunction bootstrapAppScopedEarlyEventContract(container, appId, bubbleEventTypes, captureEventTypes, dataContainer = window) {\n    const earlyJsactionData = createEarlyJsactionData(container);\n    if (!dataContainer._ejsas) {\n        dataContainer._ejsas = {};\n    }\n    dataContainer._ejsas[appId] = earlyJsactionData;\n    addEvents(earlyJsactionData, bubbleEventTypes);\n    addEvents(earlyJsactionData, captureEventTypes, /* capture= */ true);\n}\n/** Get the queued `EventInfo` objects that were dispatched before a dispatcher was registered. */\nfunction getAppScopedQueuedEventInfos(appId, dataContainer = window) {\n    return getQueuedEventInfos(dataContainer._ejsas?.[appId]);\n}\n/**\n * Registers a dispatcher function on the `EarlyJsactionData` present on the nested object on the\n * window.\n */\nfunction registerAppScopedDispatcher(restriction, appId, dispatcher, dataContainer = window) {\n    registerDispatcher(dataContainer._ejsas?.[appId], dispatcher);\n}\n/** Removes all event listener handlers. */\nfunction removeAllAppScopedEventListeners(appId, dataContainer = window) {\n    removeAllEventListeners(dataContainer._ejsas?.[appId]);\n}\n/** Clear the early event contract. */\nfunction clearAppScopedEarlyEventContract(appId, dataContainer = window) {\n    if (!dataContainer._ejsas) {\n        return;\n    }\n    dataContainer._ejsas[appId] = undefined;\n}\n\nexport { Attribute, EventContract, EventContractContainer, EventDispatcher, EventInfoWrapper, EventPhase, bootstrapAppScopedEarlyEventContract, clearAppScopedEarlyEventContract, getDefaulted as getActionCache, getAppScopedQueuedEventInfos, isCaptureEventType, isEarlyEventType, registerAppScopedDispatcher, registerDispatcher$1 as registerDispatcher, removeAllAppScopedEventListeners };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,MAAMA,SAAS,GAAG;EACd;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,QAAQ,EAAE;AACd,CAAC;;AAED;AACA,MAAMC,QAAQ,GAAG;EACb;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACID,QAAQ,EAAE,YAAY;EACtB;AACJ;AACA;AACA;AACA;AACA;EACIE,KAAK,EAAE;AACX,CAAC;;AAED;AACA;AACA;AACA,MAAMC,UAAU,GAAG,CAAC,CAAC;AACrB;AACA;AACA;AACA,SAASC,GAAGA,CAACC,OAAO,EAAE;EAClB,OAAOA,OAAO,CAACJ,QAAQ,CAACD,QAAQ,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA,SAASM,YAAYA,CAACD,OAAO,EAAE;EAC3B,MAAME,KAAK,GAAGH,GAAG,CAACC,OAAO,CAAC,IAAI,CAAC,CAAC;EAChCG,GAAG,CAACH,OAAO,EAAEE,KAAK,CAAC;EACnB,OAAOA,KAAK;AAChB;AACA;AACA;AACA;AACA,SAASC,GAAGA,CAACH,OAAO,EAAEI,SAAS,EAAE;EAC7BJ,OAAO,CAACJ,QAAQ,CAACD,QAAQ,CAAC,GAAGS,SAAS;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,IAAI,EAAE;EACrB,OAAOR,UAAU,CAACQ,IAAI,CAAC;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACD,IAAI,EAAEE,MAAM,EAAE;EAC7BV,UAAU,CAACQ,IAAI,CAAC,GAAGE,MAAM;AAC7B;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACT,OAAO,EAAE;EACpB,IAAIJ,QAAQ,CAACD,QAAQ,IAAIK,OAAO,EAAE;IAC9B,OAAOA,OAAO,CAACJ,QAAQ,CAACD,QAAQ,CAAC;EACrC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMe,SAAS,GAAG;EACd;AACJ;AACA;AACA;EACIC,QAAQ,EAAE,UAAU;EACpB;AACJ;AACA;AACA;EACIC,MAAM,EAAE,QAAQ;EAChB;AACJ;AACA;AACA;AACA;AACA;EACIC,KAAK,EAAE,OAAO;EACd;AACJ;AACA;AACA;AACA;AACA;EACIC,QAAQ,EAAE,UAAU;EACpB;AACJ;AACA;AACA;AACA;EACIC,SAAS,EAAE,WAAW;EACtB;AACJ;AACA;EACIC,QAAQ,EAAE,UAAU;EACpB;AACJ;AACA;AACA;AACA;EACIC,KAAK,EAAE,OAAO;EACd;AACJ;AACA;AACA;AACA;EACIC,OAAO,EAAE,SAAS;EAClB;AACJ;AACA;EACIC,IAAI,EAAE,MAAM;EACZ;AACJ;AACA;EACIC,QAAQ,EAAE,UAAU;EACpB;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,MAAM,EAAE,QAAQ;EAChB;AACJ;AACA;AACA;AACA;EACIC,OAAO,EAAE,SAAS;EAClB;AACJ;AACA;AACA;EACIC,QAAQ,EAAE,UAAU;EACpB;AACJ;AACA;AACA;AACA;EACIC,KAAK,EAAE,OAAO;EACd;AACJ;AACA;AACA;AACA;EACIC,OAAO,EAAE,SAAS;EAClB;AACJ;AACA;AACA;AACA;EACIC,SAAS,EAAE,WAAW;EACtB;AACJ;AACA;AACA;AACA;EACIC,SAAS,EAAE,WAAW;EACtB;AACJ;AACA;AACA;AACA;EACIC,QAAQ,EAAE,UAAU;EACpB;AACJ;AACA;AACA;EACIC,UAAU,EAAE,YAAY;EACxB;AACJ;AACA;AACA;EACIC,UAAU,EAAE,YAAY;EACxB;AACJ;AACA;EACIC,SAAS,EAAE,WAAW;EACtB;AACJ;AACA;AACA;AACA;EACIC,SAAS,EAAE,WAAW;EACtB;AACJ;AACA;AACA;AACA;EACIC,WAAW,EAAE,aAAa;EAC1B;AACJ;AACA;AACA;AACA;EACIC,WAAW,EAAE,aAAa;EAC1B;AACJ;AACA;AACA;AACA;EACIC,UAAU,EAAE,YAAY;EACxB;AACJ;AACA;AACA;EACIC,YAAY,EAAE,cAAc;EAC5B;AACJ;AACA;AACA;EACIC,YAAY,EAAE,cAAc;EAC5B;AACJ;AACA;EACIC,WAAW,EAAE,aAAa;EAC1B;AACJ;AACA;EACIC,aAAa,EAAE,eAAe;EAC9B;AACJ;AACA;AACA;AACA;EACIC,iBAAiB,EAAE,mBAAmB;EACtC;AACJ;AACA;AACA;AACA;EACIC,kBAAkB,EAAE,oBAAoB;EACxC;AACJ;AACA;AACA;AACA;EACIC,KAAK,EAAE,OAAO;EACd;AACJ;AACA;AACA;AACA;EACIC,IAAI,EAAE,MAAM;EACZ;AACJ;AACA;EACIC,MAAM,EAAE,QAAQ;EAChB;AACJ;AACA;AACA;EACIC,UAAU,EAAE,YAAY;EACxB;AACJ;AACA;AACA;EACIC,QAAQ,EAAE,UAAU;EACpB;AACJ;AACA;AACA;EACIC,SAAS,EAAE,WAAW;EACtB;AACJ;AACA;EACIC,KAAK,EAAE,OAAO;EACd;AACJ;AACA;EACIC,MAAM,EAAE,QAAQ;EAChB;AACJ;AACA;AACA;AACA;EACIC,MAAM,EAAE,QAAQ;EAChB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,MAAM,EAAE;AACZ,CAAC;AACD;AACA,MAAMC,yBAAyB,GAAG,CAC9B1C,SAAS,CAACmB,UAAU,EACpBnB,SAAS,CAACoB,UAAU,EACpB,cAAc,EACd,cAAc,CACjB;AACD;AACA,MAAMuB,kBAAkB,GAAG,CACvB3C,SAAS,CAACG,KAAK,EACfH,SAAS,CAACM,QAAQ,EAClBN,SAAS,CAACQ,OAAO,EACjBR,SAAS,CAACU,QAAQ,EAClBV,SAAS,CAACY,OAAO,EACjBZ,SAAS,CAACc,KAAK,EACfd,SAAS,CAACa,QAAQ,EAClBb,SAAS,CAACiB,SAAS,EACnBjB,SAAS,CAACkB,QAAQ,EAClBlB,SAAS,CAACW,MAAM,EAChBX,SAAS,CAACmC,UAAU,EACpBnC,SAAS,CAACoC,QAAQ,EAClBpC,SAAS,CAACqC,SAAS,EACnB,aAAa,EACb,UAAU,EACV,QAAQ,EACR,kBAAkB,EAClB,mBAAmB,EACnB,gBAAgB,EAChB,aAAa,EACb,OAAO,EACP,QAAQ,EACR,MAAM,EACN,KAAK,EACL,OAAO,EACP,WAAW,EACX,SAAS,EACT,OAAO,EACP,aAAa,EACb,UAAU,EACV,WAAW,EACX,WAAW,EACX,MAAM,EACN,WAAW,EACX,SAAS,EACT,aAAa,EACb,aAAa,EACb,WAAW,EACX,eAAe,EACf,aAAa,EACb,YAAY,EACZ,mBAAmB,EACnB,oBAAoB;AACpB;AACA,OAAO,EACP,gBAAgB;AAChB;AACA,UAAU,EACV,UAAU,EACV,kBAAkB;AAClB;AACA,aAAa,CAChB;AACD;AACA,MAAMO,mBAAmB,GAAG,CACxB5C,SAAS,CAACO,KAAK,EACfP,SAAS,CAACS,IAAI,EACdT,SAAS,CAACgC,KAAK,EACfhC,SAAS,CAACiC,IAAI,EACdjC,SAAS,CAACwC,MAAM,CACnB;AACD;AACA;AACA;AACA;AACA;AACA,MAAMK,kBAAkB,GAAIC,SAAS,IAAKF,mBAAmB,CAACG,OAAO,CAACD,SAAS,CAAC,IAAI,CAAC;AACrF;AACA,MAAME,iBAAiB,GAAGL,kBAAkB,CAACM,MAAM,CAACL,mBAAmB,CAAC;AACxE;AACA;AACA;AACA,MAAMM,gBAAgB,GAAIJ,SAAS,IAAKE,iBAAiB,CAACD,OAAO,CAACD,SAAS,CAAC,IAAI,CAAC;;AAEjF;AACA;AACA;AACA;AACA,MAAMK,SAAS,GAAG,CAAC;AACnB;AACA,MAAMC,KAAK,GAAG,EAAE;AAChB;AACA,MAAMC,KAAK,GAAG,EAAE;AAChB;AACA,MAAMC,OAAO,GAAG;EAAEH,SAAS;EAAEC,KAAK;EAAEC;AAAM,CAAC;;AAE3C;AACA;AACA;AACA,SAASE,mBAAmBA,CAACT,SAAS,EAAE;EACpC;EACA;EACA;EACA;EACA;EACA,IAAIA,SAAS,KAAK9C,SAAS,CAACmB,UAAU,EAAE;IACpC,OAAOnB,SAAS,CAACiB,SAAS;EAC9B,CAAC,MACI,IAAI6B,SAAS,KAAK9C,SAAS,CAACoB,UAAU,EAAE;IACzC,OAAOpB,SAAS,CAACkB,QAAQ;EAC7B,CAAC,MACI,IAAI4B,SAAS,KAAK9C,SAAS,CAAC0B,YAAY,EAAE;IAC3C,OAAO1B,SAAS,CAACwB,WAAW;EAChC,CAAC,MACI,IAAIsB,SAAS,KAAK9C,SAAS,CAAC2B,YAAY,EAAE;IAC3C,OAAO3B,SAAS,CAACyB,UAAU;EAC/B;EACA,OAAOqB,SAAS;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,gBAAgBA,CAAClE,OAAO,EAAEwD,SAAS,EAAEW,OAAO,EAAE;EACnD;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIC,OAAO,GAAG,KAAK;EACnB,IAAIb,kBAAkB,CAACC,SAAS,CAAC,EAAE;IAC/BY,OAAO,GAAG,IAAI;EAClB;EACApE,OAAO,CAACkE,gBAAgB,CAACV,SAAS,EAAEW,OAAO,EAAEC,OAAO,CAAC;EACrD,OAAO;IAAEZ,SAAS;IAAEW,OAAO;IAAEC;EAAQ,CAAC;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,mBAAmBA,CAACrE,OAAO,EAAEsE,IAAI,EAAE;EACxC,IAAItE,OAAO,CAACqE,mBAAmB,EAAE;IAC7BrE,OAAO,CAACqE,mBAAmB,CAACC,IAAI,CAACd,SAAS,EAAEc,IAAI,CAACH,OAAO,EAAEG,IAAI,CAACF,OAAO,CAAC;IACvE;IACA;EACJ,CAAC,MACI,IAAIpE,OAAO,CAACuE,WAAW,EAAE;IAC1B;IACA;IACAvE,OAAO,CAACuE,WAAW,CAAC,KAAKD,IAAI,CAACd,SAAS,EAAE,EAAEc,IAAI,CAACH,OAAO,CAAC;EAC5D;AACJ;AACA;AACA;AACA;AACA;AACA,SAASK,eAAeA,CAACC,CAAC,EAAE;EACxBA,CAAC,CAACD,eAAe,GAAGC,CAAC,CAACD,eAAe,CAAC,CAAC,GAAIC,CAAC,CAACC,YAAY,GAAG,IAAK;AACrE;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACF,CAAC,EAAE;EACvBA,CAAC,CAACE,cAAc,GAAGF,CAAC,CAACE,cAAc,CAAC,CAAC,GAAIF,CAAC,CAACG,WAAW,GAAG,KAAM;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACJ,CAAC,EAAE;EAClB,IAAIK,EAAE,GAAGL,CAAC,CAACM,MAAM;EACjB;EACA;EACA,IAAI,CAACD,EAAE,CAACE,YAAY,IAAIF,EAAE,CAACG,UAAU,EAAE;IACnCH,EAAE,GAAGA,EAAE,CAACG,UAAU;EACtB;EACA,OAAOH,EAAE;AACb;AACA;AACA;AACA;AACA,IAAII,KAAK,GAAG,OAAOC,SAAS,KAAK,WAAW,IAAI,WAAW,CAACC,IAAI,CAACD,SAAS,CAACE,SAAS,CAAC;AACrF;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACb,CAAC,EAAE;EACtB;IACA;IACA;IACAA,CAAC,CAACc,KAAK,KAAK,CAAC;IACT;IACA;IACCd,CAAC,CAACc,KAAK,IAAI,IAAI;IACZ;IACA;IACAd,CAAC,CAACe,MAAM,KAAK,CAAE,CAAC;EAAA;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAChB,CAAC,EAAE;EAC7B;IACA;IACA;IACCS,KAAK,IAAIT,CAAC,CAACiB,OAAO;IACf;IACA;IACC,CAACR,KAAK,IAAIT,CAAC,CAACkB,OAAQ,IACrBL,aAAa,CAACb,CAAC,CAAC;IAChB;IACA;IACAA,CAAC,CAACmB;EAAQ;AAClB;AACA;AACA,MAAMC,QAAQ,GAAG,OAAOV,SAAS,KAAK,WAAW,IAC7C,CAAC,OAAO,CAACC,IAAI,CAACD,SAAS,CAACE,SAAS,CAAC,IAClC,QAAQ,CAACD,IAAI,CAACD,SAAS,CAACE,SAAS,CAAC;AACtC;AACA,MAAMS,IAAI,GAAG,OAAOX,SAAS,KAAK,WAAW,KACxC,MAAM,CAACC,IAAI,CAACD,SAAS,CAACE,SAAS,CAAC,IAAI,SAAS,CAACD,IAAI,CAACD,SAAS,CAACE,SAAS,CAAC,CAAC;AAC7E;AACA,MAAMU,OAAO,GAAG,OAAOZ,SAAS,KAAK,WAAW,IAC5C,CAAC,cAAc,CAACC,IAAI,CAACD,SAAS,CAACE,SAAS,CAAC,IACzC,OAAO,CAACD,IAAI,CAACD,SAAS,CAACa,OAAO,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,sBAAsBA,CAACnB,EAAE,EAAE;EAChC,IAAI,EAAE,cAAc,IAAIA,EAAE,CAAC,EAAE;IACzB,OAAO,KAAK;EAChB;EACA,IAAIoB,aAAa,CAACpB,EAAE,CAAC,EAAE;IACnB,OAAO,KAAK;EAChB;EACA,IAAIqB,qBAAqB,CAACrB,EAAE,CAAC,EAAE;IAC3B,OAAO,KAAK;EAChB;EACA;EACA;EACA,IAAIA,EAAE,CAACsB,iBAAiB,EAAE;IACtB,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAAC5B,CAAC,EAAE;EACvB;IACA;IACA;IACAA,CAAC,CAACkB,OAAO;IACL;IACA;IACAlB,CAAC,CAACmB,QAAQ;IACV;IACA;IACAnB,CAAC,CAAC6B,MAAM;IACR;IACA;IACA7B,CAAC,CAACiB;EAAO;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASa,2CAA2CA,CAAC9B,CAAC,EAAE;EACpD,MAAMK,EAAE,GAAGD,SAAS,CAACJ,CAAC,CAAC;EACvB,MAAM+B,OAAO,GAAG1B,EAAE,CAAC0B,OAAO,CAACC,WAAW,CAAC,CAAC;EACxC,MAAMC,IAAI,GAAG,CAAC5B,EAAE,CAACE,YAAY,CAAC,MAAM,CAAC,IAAI,EAAE,EAAEyB,WAAW,CAAC,CAAC;EAC1D,IAAID,OAAO,KAAK,QAAQ,IAAIE,IAAI,KAAK,QAAQ,EAAE;IAC3C,OAAO,IAAI;EACf;EACA,IAAI,CAACC,mBAAmB,CAAC7B,EAAE,CAAC,EAAE;IAC1B,OAAO,KAAK;EAChB;EACA,IAAI0B,OAAO,KAAK,GAAG,EAAE;IACjB,OAAO,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIA,OAAO,KAAK,QAAQ,EAAE;IACtB,OAAO,KAAK;EAChB;EACA,IAAII,YAAY,CAAC9B,EAAE,CAAC,EAAE;IAClB,OAAO,KAAK;EAChB;EACA,IAAIoB,aAAa,CAACpB,EAAE,CAAC,EAAE;IACnB,OAAO,KAAK;EAChB;EACA,OAAO,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+B,gBAAgBA,CAACpC,CAAC,EAAE;EACzB,IAAIqC,GAAG;EACP;EACA;EACArC,CAAC,CAACc,KAAK;EACH;EACA;EACAd,CAAC,CAACsC,OAAO;EACb,IAAI,CAACD,GAAG,IAAIrC,CAAC,CAACqC,GAAG,EAAE;IACfA,GAAG,GAAGE,qBAAqB,CAACvC,CAAC,CAACqC,GAAG,CAAC;EACtC;EACA,IAAIjB,QAAQ,IAAIiB,GAAG,KAAK9C,OAAO,CAACH,SAAS,EAAE;IACvCiD,GAAG,GAAG9C,OAAO,CAACF,KAAK;EACvB;EACA,IAAIgD,GAAG,KAAK9C,OAAO,CAACF,KAAK,IAAIgD,GAAG,KAAK9C,OAAO,CAACD,KAAK,EAAE;IAChD,OAAO,KAAK;EAChB;EACA,MAAMe,EAAE,GAAGD,SAAS,CAACJ,CAAC,CAAC;EACvB,IAAIA,CAAC,CAACwC,IAAI,KAAKvG,SAAS,CAACY,OAAO,IAAI,CAAC2E,sBAAsB,CAACnB,EAAE,CAAC,IAAIuB,cAAc,CAAC5B,CAAC,CAAC,EAAE;IAClF,OAAO,KAAK;EAChB;EACA;EACA;EACA,IAAImC,YAAY,CAAC9B,EAAE,CAAC,IAAIgC,GAAG,KAAK9C,OAAO,CAACD,KAAK,EAAE;IAC3C,OAAO,KAAK;EAChB;EACA;EACA;EACA;EACA,IAAI,CAACmD,WAAW,CAACpC,EAAE,CAAC,EAAE;IAClB,OAAO,KAAK;EAChB;EACA,MAAMmC,IAAI,GAAG,CAACnC,EAAE,CAACE,YAAY,CAAC,MAAM,CAAC,IACjCF,EAAE,CAACmC,IAAI,IACPnC,EAAE,CAAC0B,OAAO,EAAEC,WAAW,CAAC,CAAC;EAC7B,MAAMU,oBAAoB,GAAGC,iCAAiC,CAACH,IAAI,CAAC,GAAGH,GAAG,KAAK,CAAC;EAChF,MAAMO,mBAAmB,GAAG,EAAEJ,IAAI,IAAIG,iCAAiC,CAAC,IAAIN,GAAG,KAAK9C,OAAO,CAACF,KAAK;EACjG,MAAMwD,OAAO,GAAGxC,EAAE,CAAC0B,OAAO,CAACC,WAAW,CAAC,CAAC,KAAK,OAAO,IAAI,CAAC,CAAC3B,EAAE,CAACmC,IAAI;EACjE,OAAO,CAACE,oBAAoB,IAAIE,mBAAmB,KAAKC,OAAO;AACnE;AACA;AACA;AACA;AACA;AACA;AACA,SAASJ,WAAWA,CAACpC,EAAE,EAAE;EACrB,OAAQ,CAACA,EAAE,CAAC0B,OAAO,IAAIe,2BAA2B,IAAIC,oBAAoB,CAAC1C,EAAE,CAAC,KAC1E,CAACA,EAAE,CAAC2C,QAAQ;AACpB;AACA;AACA;AACA;AACA;AACA,SAASD,oBAAoBA,CAACxH,OAAO,EAAE;EACnC;EACA;EACA;EACA,MAAM0H,QAAQ,GAAG1H,OAAO,CAAC2H,gBAAgB,CAAC,UAAU,CAAC,CAAC,CAAC;EACvD,OAAOD,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAACE,SAAS;AACjD;AACA;AACA,MAAML,2BAA2B,GAAG;EAChC,GAAG,EAAE,CAAC;EACN,OAAO,EAAE,CAAC;EACV,UAAU,EAAE,CAAC;EACb,QAAQ,EAAE,CAAC;EACX,QAAQ,EAAE;AACd,CAAC;AACD;AACA,SAASM,eAAeA,CAACpD,CAAC,EAAE;EACxB,MAAMqC,GAAG;EACT;EACA;EACArC,CAAC,CAACc,KAAK;EACH;EACA;EACAd,CAAC,CAACsC,OAAO;EACb,MAAMjC,EAAE,GAAGD,SAAS,CAACJ,CAAC,CAAC;EACvB,MAAMqD,WAAW,GAAG,CAAChD,EAAE,CAACmC,IAAI,IAAInC,EAAE,CAAC0B,OAAO,EAAEC,WAAW,CAAC,CAAC;EACzD,OAAOK,GAAG,KAAK9C,OAAO,CAACD,KAAK,IAAI+D,WAAW,KAAK,UAAU;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,mBAAmBA,CAACtD,CAAC,EAAEwC,IAAI,EAAEjH,OAAO,EAAE;EAC3C;EACA;EACA,MAAMgI,OAAO,GAAGvD,CAAC,CAACwD,aAAa;EAC/B,OAAQ,CAAExD,CAAC,CAACwC,IAAI,KAAKvG,SAAS,CAACiB,SAAS,IAAIsF,IAAI,KAAKvG,SAAS,CAACmB,UAAU,IACpE4C,CAAC,CAACwC,IAAI,KAAKvG,SAAS,CAACkB,QAAQ,IAAIqF,IAAI,KAAKvG,SAAS,CAACoB,UAAW,IAC/D2C,CAAC,CAACwC,IAAI,KAAKvG,SAAS,CAACwB,WAAW,IAAI+E,IAAI,KAAKvG,SAAS,CAAC0B,YAAa,IACpEqC,CAAC,CAACwC,IAAI,KAAKvG,SAAS,CAACyB,UAAU,IAAI8E,IAAI,KAAKvG,SAAS,CAAC2B,YAAa,MACnE,CAAC2F,OAAO,IAAKA,OAAO,KAAKhI,OAAO,IAAI,CAACA,OAAO,CAACkI,QAAQ,CAACF,OAAO,CAAE,CAAC;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,uBAAuBA,CAAC1D,CAAC,EAAEM,MAAM,EAAE;EACxC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,MAAMqD,IAAI,GAAG,CAAC,CAAC;EACf,KAAK,MAAMC,QAAQ,IAAI5D,CAAC,EAAE;IACtB,IAAI4D,QAAQ,KAAK,YAAY,IAAIA,QAAQ,KAAK,QAAQ,EAAE;MACpD;IACJ;IACA,MAAMvB,GAAG,GAAGuB,QAAQ;IACpB;IACA;IACA,MAAMC,KAAK,GAAG7D,CAAC,CAACqC,GAAG,CAAC;IACpB,IAAI,OAAOwB,KAAK,KAAK,UAAU,EAAE;MAC7B;IACJ;IACA;IACA;IACA;IACAF,IAAI,CAACtB,GAAG,CAAC,GAAGwB,KAAK;EACrB;EACA,IAAI7D,CAAC,CAACwC,IAAI,KAAKvG,SAAS,CAACiB,SAAS,EAAE;IAChCyG,IAAI,CAAC,MAAM,CAAC,GAAG1H,SAAS,CAACmB,UAAU;EACvC,CAAC,MACI,IAAI4C,CAAC,CAACwC,IAAI,KAAKvG,SAAS,CAACkB,QAAQ,EAAE;IACpCwG,IAAI,CAAC,MAAM,CAAC,GAAG1H,SAAS,CAACoB,UAAU;EACvC,CAAC,MACI,IAAI2C,CAAC,CAACwC,IAAI,KAAKvG,SAAS,CAACwB,WAAW,EAAE;IACvCkG,IAAI,CAAC,MAAM,CAAC,GAAG1H,SAAS,CAAC0B,YAAY;EACzC,CAAC,MACI;IACDgG,IAAI,CAAC,MAAM,CAAC,GAAG1H,SAAS,CAAC2B,YAAY;EACzC;EACA+F,IAAI,CAAC,QAAQ,CAAC,GAAGA,IAAI,CAAC,YAAY,CAAC,GAAGrD,MAAM;EAC5CqD,IAAI,CAAC,SAAS,CAAC,GAAG,KAAK;EACvB,OAAOA,IAAI;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,YAAYA,CAACC,KAAK,EAAE;EACzB,MAAMC,KAAK,GAAID,KAAK,CAACE,cAAc,IAAIF,KAAK,CAACE,cAAc,CAAC,CAAC,CAAC,IAAMF,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACG,OAAO,CAAC,CAAC,CAAE;EACtG,IAAI,CAACF,KAAK,EAAE;IACR,OAAO,IAAI;EACf;EACA,OAAO;IACHG,OAAO,EAAEH,KAAK,CAACG,OAAO;IACtBC,OAAO,EAAEJ,KAAK,CAACI,OAAO;IACtBC,OAAO,EAAEL,KAAK,CAACK,OAAO;IACtBC,OAAO,EAAEN,KAAK,CAACM;EACnB,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,yBAAyBA,CAACR,KAAK,EAAE;EACtC,MAAMS,KAAK,GAAG,CAAC,CAAC;EAChBA,KAAK,CAAC,mBAAmB,CAAC,GAAGT,KAAK,CAACvB,IAAI;EACvCgC,KAAK,CAAC,MAAM,CAAC,GAAGvI,SAAS,CAACG,KAAK;EAC/B,KAAK,MAAMwH,QAAQ,IAAIG,KAAK,EAAE;IAC1B,IAAIH,QAAQ,KAAK,MAAM,IAAIA,QAAQ,KAAK,YAAY,EAAE;MAClD;IACJ;IACA,MAAMvB,GAAG,GAAGuB,QAAQ;IACpB;IACA;IACA,MAAMC,KAAK,GAAGE,KAAK,CAAC1B,GAAG,CAAC;IACxB,IAAI,OAAOwB,KAAK,KAAK,UAAU,EAAE;MAC7B;IACJ;IACA;IACA;IACA;IACAW,KAAK,CAACnC,GAAG,CAAC,GAAGwB,KAAK;EACtB;EACA;EACA;EACAW,KAAK,CAAC,WAAW,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;EAC/B;EACAF,KAAK,CAAC,kBAAkB,CAAC,GAAG,KAAK;EACjCA,KAAK,CAAC,gBAAgB,CAAC,GAAGG,uBAAuB;EACjDH,KAAK,CAAC,qBAAqB,CAAC,GAAG,KAAK;EACpCA,KAAK,CAAC,iBAAiB,CAAC,GAAGI,wBAAwB;EACnD;EACA,MAAMZ,KAAK,GAAGF,YAAY,CAACC,KAAK,CAAC;EACjC,IAAIC,KAAK,EAAE;IACPQ,KAAK,CAAC,SAAS,CAAC,GAAGR,KAAK,CAACG,OAAO;IAChCK,KAAK,CAAC,SAAS,CAAC,GAAGR,KAAK,CAACI,OAAO;IAChCI,KAAK,CAAC,SAAS,CAAC,GAAGR,KAAK,CAACK,OAAO;IAChCG,KAAK,CAAC,SAAS,CAAC,GAAGR,KAAK,CAACM,OAAO;EACpC;EACA,OAAOE,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA,SAASG,uBAAuBA,CAAA,EAAG;EAC/B,IAAI,CAACE,gBAAgB,GAAG,IAAI;AAChC;AACA;AACA;AACA;AACA;AACA,SAASD,wBAAwBA,CAAA,EAAG;EAChC,IAAI,CAACE,mBAAmB,GAAG,IAAI;AACnC;AACA;AACA;AACA;AACA;AACA,MAAMvC,qBAAqB,GAAG;EAC1B,OAAO,EAAEhD,OAAO,CAACF,KAAK;EACtB,GAAG,EAAEE,OAAO,CAACD;AACjB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMqD,iCAAiC,GAAG;EACtC,GAAG,EAAEpD,OAAO,CAACF,KAAK;EAClB,QAAQ,EAAE,CAAC;EACX,UAAU,EAAEE,OAAO,CAACD,KAAK;EACzB,UAAU,EAAEC,OAAO,CAACF,KAAK;EACzB,MAAM,EAAE,CAAC;EACT,UAAU,EAAEE,OAAO,CAACF,KAAK;EACzB,MAAM,EAAEE,OAAO,CAACF,KAAK;EACrB,SAAS,EAAEE,OAAO,CAACF,KAAK;EACxB,MAAM,EAAE,CAAC;EACT,SAAS,EAAE,CAAC;EACZ,UAAU,EAAE,CAAC;EACb,kBAAkB,EAAE,CAAC;EACrB,eAAe,EAAE,CAAC;EAClB,QAAQ,EAAE,CAAC;EACX,OAAO,EAAEE,OAAO,CAACD,KAAK;EACtB,YAAY,EAAEC,OAAO,CAACD,KAAK;EAC3B,OAAO,EAAE,CAAC;EACV,QAAQ,EAAE,CAAC;EACX,QAAQ,EAAEC,OAAO,CAACD,KAAK;EACvB,KAAK,EAAE,CAAC;EACR,MAAM,EAAEC,OAAO,CAACF,KAAK;EACrB,UAAU,EAAEE,OAAO,CAACF;AACxB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8C,YAAYA,CAAC5G,OAAO,EAAE;EAC3B,MAAMiH,IAAI,GAAG,CAACjH,OAAO,CAACgF,YAAY,CAAC,MAAM,CAAC,IAAIhF,OAAO,CAACwG,OAAO,EAAEC,WAAW,CAAC,CAAC;EAC5E,OAAOQ,IAAI,IAAIuC,aAAa;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,SAAStD,aAAaA,CAACpB,EAAE,EAAE;EACvB,MAAMmC,IAAI,GAAG,CAACnC,EAAE,CAACE,YAAY,CAAC,MAAM,CAAC,IAAIF,EAAE,CAAC0B,OAAO,EAAEC,WAAW,CAAC,CAAC;EAClE,OAAOQ,IAAI,IAAIwC,aAAa;AAChC;AACA;AACA;AACA;AACA;AACA;AACA,SAAS9C,mBAAmBA,CAAC7B,EAAE,EAAE;EAC7B,OAAOA,EAAE,CAAC0B,OAAO,CAACC,WAAW,CAAC,CAAC,IAAIiD,oBAAoB;AAC3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASvD,qBAAqBA,CAACrB,EAAE,EAAE;EAC/B,OAAQA,EAAE,CAAC0B,OAAO,CAACC,WAAW,CAAC,CAAC,KAAK,QAAQ,IACxC,CAAC,CAAC3B,EAAE,CAACmC,IAAI,IAAInC,EAAE,CAACmC,IAAI,CAACR,WAAW,CAAC,CAAC,KAAK,MAAO;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+C,aAAa,GAAG;EAClB,UAAU,EAAE,IAAI;EAChB,MAAM,EAAE,IAAI;EACZ,QAAQ,EAAE,IAAI;EACd,OAAO,EAAE;AACb,CAAC;AACD;AACA,MAAMC,aAAa,GAAG;EAClB,OAAO,EAAE,IAAI;EACb,MAAM,EAAE,IAAI;EACZ,UAAU,EAAE,IAAI;EAChB,gBAAgB,EAAE,IAAI;EACtB,OAAO,EAAE,IAAI;EACb,OAAO,EAAE,IAAI;EACb,QAAQ,EAAE,IAAI;EACd,UAAU,EAAE,IAAI;EAChB,OAAO,EAAE,IAAI;EACb,QAAQ,EAAE,IAAI;EACd,KAAK,EAAE,IAAI;EACX,MAAM,EAAE,IAAI;EACZ,UAAU,EAAE,IAAI;EAChB,MAAM,EAAE,IAAI;EACZ,KAAK,EAAE,IAAI;EACX,MAAM,EAAE;AACZ,CAAC;AACD;AACA,MAAMC,oBAAoB,GAAG;EACzB,GAAG,EAAE,IAAI;EACT,MAAM,EAAE,IAAI;EACZ,QAAQ,EAAE,IAAI;EACd,QAAQ,EAAE,IAAI;EACd,KAAK,EAAE,IAAI;EACX,OAAO,EAAE,IAAI;EACb,MAAM,EAAE,IAAI;EACZ,MAAM,EAAE,IAAI;EACZ,UAAU,EAAE,IAAI;EAChB,QAAQ,EAAE,IAAI;EACd,UAAU,EAAE,IAAI;EAChB,QAAQ,EAAE,IAAI;EACd,UAAU,EAAE;AAChB,CAAC;AACD;AACA,MAAMC,OAAO,GAAG;EACZC,QAAQA,CAACtB,KAAK,EAAE;IACZpD,KAAK,GAAGoD,KAAK;EACjB;AACJ,CAAC;;AAED;AACA;AACA;AACA,MAAMuB,KAAK,GAAG,OAAO1E,SAAS,KAAK,WAAW,IAAI,kBAAkB,CAACC,IAAI,CAACD,SAAS,CAACE,SAAS,CAAC;AAC9F;AACA;AACA;AACA;AACA;AACA,MAAMyE,sBAAsB,CAAC;EACzB;AACJ;AACA;EACIC,WAAWA,CAAC/J,OAAO,EAAE;IACjB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACgK,YAAY,GAAG,EAAE;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACI9F,gBAAgBA,CAACV,SAAS,EAAEyG,UAAU,EAAE;IACpC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAIJ,KAAK,EAAE;MACP,IAAI,CAAC7J,OAAO,CAACkK,KAAK,CAACC,MAAM,GAAG,SAAS;IACzC;IACA,IAAI,CAACH,YAAY,CAACI,IAAI,CAAClG,gBAAgB,CAAC,IAAI,CAAClE,OAAO,EAAEwD,SAAS,EAAEyG,UAAU,CAAC,IAAI,CAACjK,OAAO,CAAC,CAAC,CAAC;EAC/F;EACA;AACJ;AACA;EACIqK,OAAOA,CAAA,EAAG;IACN,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACN,YAAY,CAACO,MAAM,EAAED,CAAC,EAAE,EAAE;MAC/CjG,mBAAmB,CAAC,IAAI,CAACrE,OAAO,EAAE,IAAI,CAACgK,YAAY,CAACM,CAAC,CAAC,CAAC;IAC3D;IACA,IAAI,CAACN,YAAY,GAAG,EAAE;EAC1B;AACJ;AAEA,MAAMQ,IAAI,GAAG;EACT;AACJ;AACA;AACA;EACIC,0BAA0B,EAAE,GAAG;EAC/B;AACJ;AACA;AACA;EACIC,sBAAsB,EAAE;AAC5B,CAAC;;AAED;AACA,SAASC,YAAYA,CAACC,SAAS,EAAE;EAC7B,OAAOA,SAAS,CAACpH,SAAS;AAC9B;AACA;AACA,SAASqH,YAAYA,CAACD,SAAS,EAAEpH,SAAS,EAAE;EACxCoH,SAAS,CAACpH,SAAS,GAAGA,SAAS;AACnC;AACA;AACA,SAASsH,QAAQA,CAACF,SAAS,EAAE;EACzB,OAAOA,SAAS,CAACpC,KAAK;AAC1B;AACA;AACA,SAASuC,QAAQA,CAACH,SAAS,EAAEpC,KAAK,EAAE;EAChCoC,SAAS,CAACpC,KAAK,GAAGA,KAAK;AAC3B;AACA;AACA,SAASwC,gBAAgBA,CAACJ,SAAS,EAAE;EACjC,OAAOA,SAAS,CAACK,aAAa;AAClC;AACA;AACA,SAASC,gBAAgBA,CAACN,SAAS,EAAEK,aAAa,EAAE;EAChDL,SAAS,CAACK,aAAa,GAAGA,aAAa;AAC3C;AACA;AACA,SAASE,YAAYA,CAACP,SAAS,EAAE;EAC7B,OAAOA,SAAS,CAACQ,GAAG;AACxB;AACA;AACA,SAASC,YAAYA,CAACT,SAAS,EAAEU,SAAS,EAAE;EACxCV,SAAS,CAACQ,GAAG,GAAGE,SAAS;AAC7B;AACA;AACA,SAASC,YAAYA,CAACX,SAAS,EAAE;EAC7B,OAAOA,SAAS,CAACY,SAAS;AAC9B;AACA;AACA,SAASC,YAAYA,CAACb,SAAS,EAAEc,SAAS,EAAE;EACxCd,SAAS,CAACY,SAAS,GAAGE,SAAS;AACnC;AACA;AACA,SAASC,SAASA,CAACf,SAAS,EAAE;EAC1B,OAAOA,SAAS,CAACgB,GAAG;AACxB;AACA;AACA,SAASC,SAASA,CAACjB,SAAS,EAAEkB,UAAU,EAAEC,aAAa,EAAE;EACrDnB,SAAS,CAACgB,GAAG,GAAG,CAACE,UAAU,EAAEC,aAAa,CAAC;AAC/C;AACA;AACA,SAASC,WAAWA,CAACpB,SAAS,EAAE;EAC5BA,SAAS,CAACgB,GAAG,GAAGK,SAAS;AAC7B;AACA;AACA,SAASC,aAAaA,CAACC,UAAU,EAAE;EAC/B,OAAOA,UAAU,CAAC,CAAC,CAAC;AACxB;AACA;AACA,SAASC,gBAAgBA,CAACD,UAAU,EAAE;EAClC,OAAOA,UAAU,CAAC,CAAC,CAAC;AACxB;AACA;AACA,SAASE,WAAWA,CAACzB,SAAS,EAAE;EAC5B,OAAOA,SAAS,CAAC0B,IAAI;AACzB;AACA;AACA,SAASC,WAAWA,CAAC3B,SAAS,EAAE4B,MAAM,EAAE;EACpC5B,SAAS,CAAC0B,IAAI,GAAGE,MAAM;AAC3B;AACA;AACA,SAASC,eAAeA,CAAC7B,SAAS,EAAE;EAChC,OAAOA,SAAS,CAAC8B,KAAK;AAC1B;AACA;AACA,SAASC,eAAeA,CAAC/B,SAAS,EAAEgC,YAAY,EAAE;EAC9ChC,SAAS,CAAC8B,KAAK,GAAGE,YAAY;AAClC;AACA;AACA,SAASC,WAAWA,CAACjC,SAAS,EAAE;EAC5B,OAAOA,SAAS,CAACkC,GAAG;AACxB;AACA;AACA,SAASC,WAAWA,CAACnC,SAAS,EAAEoC,QAAQ,EAAE;EACtCpC,SAAS,CAACkC,GAAG,GAAGE,QAAQ;AAC5B;AACA;AACA,SAASC,cAAcA,CAACrC,SAAS,EAAE;EAC/B,OAAO;IACHpH,SAAS,EAAEoH,SAAS,CAACpH,SAAS;IAC9BgF,KAAK,EAAEoC,SAAS,CAACpC,KAAK;IACtByC,aAAa,EAAEL,SAAS,CAACK,aAAa;IACtCG,GAAG,EAAER,SAAS,CAACQ,GAAG;IAClBQ,GAAG,EAAEhB,SAAS,CAACgB,GAAG;IAClBJ,SAAS,EAAEZ,SAAS,CAACY,SAAS;IAC9Bc,IAAI,EAAE1B,SAAS,CAAC0B,IAAI;IACpBI,KAAK,EAAE9B,SAAS,CAAC8B,KAAK;IACtBI,GAAG,EAAElC,SAAS,CAACkC;EACnB,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,6BAA6BA,CAAC1J,SAAS,EAAEgF,KAAK,EAAEyC,aAAa,EAAEK,SAAS,EAAEI,SAAS,EAAEyB,MAAM,EAAEC,QAAQ,EAAER,YAAY,EAAE;EAC1H,OAAO;IACHpJ,SAAS;IACTgF,KAAK;IACLyC,aAAa;IACbG,GAAG,EAAEE,SAAS;IACdE,SAAS,EAAEE,SAAS;IACpBE,GAAG,EAAEuB,MAAM;IACXb,IAAI,EAAEc,QAAQ;IACdV,KAAK,EAAEE;EACX,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,eAAeA,CAAC;EAAE7J,SAAS;EAAEgF,KAAK;EAAEyC,aAAa;EAAEK,SAAS;EAAEI,SAAS;EAAEyB,MAAM;EAAEC,QAAQ;EAAER;AAAc,CAAC,EAAE;EACjH,OAAO;IACHpJ,SAAS;IACTgF,KAAK;IACLyC,aAAa;IACbG,GAAG,EAAEE,SAAS;IACdE,SAAS,EAAEE,SAAS;IACpBE,GAAG,EAAEuB,MAAM,GAAG,CAACA,MAAM,CAACG,IAAI,EAAEH,MAAM,CAACnN,OAAO,CAAC,GAAGiM,SAAS;IACvDK,IAAI,EAAEc,QAAQ;IACdV,KAAK,EAAEE;EACX,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMW,gBAAgB,CAAC;EACnBxD,WAAWA,CAACa,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACAD,YAAYA,CAAA,EAAG;IACX,OAAOA,YAAY,CAAC,IAAI,CAACC,SAAS,CAAC;EACvC;EACAC,YAAYA,CAACrH,SAAS,EAAE;IACpBqH,YAAY,CAAC,IAAI,CAACD,SAAS,EAAEpH,SAAS,CAAC;EAC3C;EACAsH,QAAQA,CAAA,EAAG;IACP,OAAOA,QAAQ,CAAC,IAAI,CAACF,SAAS,CAAC;EACnC;EACAG,QAAQA,CAACvC,KAAK,EAAE;IACZuC,QAAQ,CAAC,IAAI,CAACH,SAAS,EAAEpC,KAAK,CAAC;EACnC;EACAwC,gBAAgBA,CAAA,EAAG;IACf,OAAOA,gBAAgB,CAAC,IAAI,CAACJ,SAAS,CAAC;EAC3C;EACAM,gBAAgBA,CAACD,aAAa,EAAE;IAC5BC,gBAAgB,CAAC,IAAI,CAACN,SAAS,EAAEK,aAAa,CAAC;EACnD;EACAE,YAAYA,CAAA,EAAG;IACX,OAAOA,YAAY,CAAC,IAAI,CAACP,SAAS,CAAC;EACvC;EACAS,YAAYA,CAACC,SAAS,EAAE;IACpBD,YAAY,CAAC,IAAI,CAACT,SAAS,EAAEU,SAAS,CAAC;EAC3C;EACAC,YAAYA,CAAA,EAAG;IACX,OAAOA,YAAY,CAAC,IAAI,CAACX,SAAS,CAAC;EACvC;EACAa,YAAYA,CAACC,SAAS,EAAE;IACpBD,YAAY,CAAC,IAAI,CAACb,SAAS,EAAEc,SAAS,CAAC;EAC3C;EACAC,SAASA,CAAA,EAAG;IACR,MAAMwB,MAAM,GAAGxB,SAAS,CAAC,IAAI,CAACf,SAAS,CAAC;IACxC,IAAI,CAACuC,MAAM,EACP,OAAOlB,SAAS;IACpB,OAAO;MACHqB,IAAI,EAAEH,MAAM,CAAC,CAAC,CAAC;MACfnN,OAAO,EAAEmN,MAAM,CAAC,CAAC;IACrB,CAAC;EACL;EACAtB,SAASA,CAACsB,MAAM,EAAE;IACd,IAAI,CAACA,MAAM,EAAE;MACTnB,WAAW,CAAC,IAAI,CAACpB,SAAS,CAAC;MAC3B;IACJ;IACAiB,SAAS,CAAC,IAAI,CAACjB,SAAS,EAAEuC,MAAM,CAACG,IAAI,EAAEH,MAAM,CAACnN,OAAO,CAAC;EAC1D;EACAqM,WAAWA,CAAA,EAAG;IACV,OAAOA,WAAW,CAAC,IAAI,CAACzB,SAAS,CAAC;EACtC;EACA2B,WAAWA,CAACC,MAAM,EAAE;IAChBD,WAAW,CAAC,IAAI,CAAC3B,SAAS,EAAE4B,MAAM,CAAC;EACvC;EACAK,WAAWA,CAAA,EAAG;IACV,OAAOA,WAAW,CAAC,IAAI,CAACjC,SAAS,CAAC;EACtC;EACAmC,WAAWA,CAACC,QAAQ,EAAE;IAClBD,WAAW,CAAC,IAAI,CAACnC,SAAS,EAAEoC,QAAQ,CAAC;EACzC;EACAQ,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAID,gBAAgB,CAACN,cAAc,CAAC,IAAI,CAACrC,SAAS,CAAC,CAAC;EAC/D;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAM6C,gBAAgB,GAAG,CAAC,CAAC;AAC3B;AACA;AACA;AACA,MAAMC,gBAAgB,GAAG,SAAS;AAClC;AACA,MAAMC,kBAAkB,GAAGjN,SAAS,CAACG,KAAK;AAC1C;AACA,MAAM+M,cAAc,CAAC;EACjB7D,WAAWA,CAAC;IAAE8D,0BAA0B,GAAG,KAAK;IAAEC,eAAe,GAAG;EAAM,CAAC,GAAG,CAAC,CAAC,EAAE;IAC9E,IAAI,CAACC,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACD,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACE,2BAA2B,GAAG/B,SAAS;IAC5C,IAAI,CAACgC,0BAA0B,GAAGhC,SAAS;IAC3C,IAAI,CAACiC,uBAAuB,GAAGjC,SAAS;IACxC,IAAI,CAAC4B,0BAA0B,GAAGA,0BAA0B;IAC5D,IAAI,CAACC,eAAe,GAAGA,eAAe;EAC1C;EACAK,gBAAgBA,CAACvD,SAAS,EAAE;IACxB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACkD,eAAe,IACpBnD,YAAY,CAACC,SAAS,CAAC,KAAKlK,SAAS,CAACG,KAAK,IAC3C4E,oBAAoB,CAACqF,QAAQ,CAACF,SAAS,CAAC,CAAC,EAAE;MAC3CC,YAAY,CAACD,SAAS,EAAElK,SAAS,CAACI,QAAQ,CAAC;IAC/C,CAAC,MACI,IAAI,IAAI,CAACiN,gBAAgB,EAAE;MAC5B,IAAI,CAACC,2BAA2B,CAACpD,SAAS,CAAC;IAC/C;EACJ;EACAwD,aAAaA,CAACxD,SAAS,EAAE;IACrB,IAAIiC,WAAW,CAACjC,SAAS,CAAC,EAAE;MACxB;IACJ;IACA,IAAI,CAACyD,cAAc,CAACzD,SAAS,EAAEI,gBAAgB,CAACJ,SAAS,CAAC,CAAC;IAC3DmC,WAAW,CAACnC,SAAS,EAAE,IAAI,CAAC;EAChC;EACA0D,mBAAmBA,CAAC1D,SAAS,EAAE;IAC3B,MAAMuC,MAAM,GAAGxB,SAAS,CAACf,SAAS,CAAC;IACnC,MAAMmB,aAAa,GAAGoB,MAAM,IAAIf,gBAAgB,CAACe,MAAM,CAAC;IACxDnB,WAAW,CAACpB,SAAS,CAAC;IACtB,MAAM3F,UAAU,GAAG8G,aAAa,IAAI,IAAI,CAACwC,aAAa,CAACxC,aAAa,CAAC;IACrE,IAAI,CAAC9G,UAAU,EAAE;MACb;IACJ;IACA,IAAI,CAACoJ,cAAc,CAACzD,SAAS,EAAE3F,UAAU,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIoJ,cAAcA,CAACzD,SAAS,EAAE4D,aAAa,EAAE;IACrC,IAAIzC,aAAa,GAAGyC,aAAa;IACjC,OAAOzC,aAAa,IAAIA,aAAa,KAAKZ,YAAY,CAACP,SAAS,CAAC,EAAE;MAC/D,IAAImB,aAAa,CAAC0C,QAAQ,KAAKC,IAAI,CAACC,YAAY,EAAE;QAC9C,IAAI,CAACC,uBAAuB,CAAC7C,aAAa,EAAEnB,SAAS,CAAC;MAC1D;MACA,IAAIe,SAAS,CAACf,SAAS,CAAC,EAAE;QACtB;QACA;QACA;QACA;MACJ;MACAmB,aAAa,GAAG,IAAI,CAACwC,aAAa,CAACxC,aAAa,CAAC;IACrD;IACA,MAAMoB,MAAM,GAAGxB,SAAS,CAACf,SAAS,CAAC;IACnC,IAAI,CAACuC,MAAM,EAAE;MACT;MACA;IACJ;IACA,IAAI,IAAI,CAACY,gBAAgB,EAAE;MACvB,IAAI,CAACE,0BAA0B,CAACrD,SAAS,CAAC;IAC9C;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACiD,0BAA0B,EAAE;MACjC,IAAIlD,YAAY,CAACC,SAAS,CAAC,KAAKlK,SAAS,CAACmB,UAAU,IAChD8I,YAAY,CAACC,SAAS,CAAC,KAAKlK,SAAS,CAACoB,UAAU,IAChD6I,YAAY,CAACC,SAAS,CAAC,KAAKlK,SAAS,CAAC0B,YAAY,IAClDuI,YAAY,CAACC,SAAS,CAAC,KAAKlK,SAAS,CAAC2B,YAAY,EAAE;QACpD;QACA;QACA;QACA,IAAI0F,mBAAmB,CAAC+C,QAAQ,CAACF,SAAS,CAAC,EAAED,YAAY,CAACC,SAAS,CAAC,EAAEwB,gBAAgB,CAACe,MAAM,CAAC,CAAC,EAAE;UAC7F;UACA;UACA;UACA;UACA;UACA,MAAM0B,WAAW,GAAG1G,uBAAuB,CAAC2C,QAAQ,CAACF,SAAS,CAAC,EAAEwB,gBAAgB,CAACe,MAAM,CAAC,CAAC;UAC1FpC,QAAQ,CAACH,SAAS,EAAEiE,WAAW,CAAC;UAChC;UACA;UACA;UACA3D,gBAAgB,CAACN,SAAS,EAAEwB,gBAAgB,CAACe,MAAM,CAAC,CAAC;QACzD,CAAC,MACI;UACDnB,WAAW,CAACpB,SAAS,CAAC;QAC1B;MACJ;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI2D,aAAaA,CAACvO,OAAO,EAAE;IACnB,MAAM8O,KAAK,GAAG9O,OAAO,CAACJ,QAAQ,CAACC,KAAK,CAAC;IACrC,IAAIiP,KAAK,EAAE;MACP,OAAOA,KAAK;IAChB;IACA,MAAM7J,UAAU,GAAGjF,OAAO,CAACiF,UAAU;IACrC,IAAIA,UAAU,EAAE8J,QAAQ,KAAK,oBAAoB,EAAE;MAC/C,OAAO9J,UAAU,EAAE+J,IAAI,IAAI,IAAI;IACnC;IACA,OAAO/J,UAAU;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI2J,uBAAuBA,CAAC7C,aAAa,EAAEnB,SAAS,EAAE;IAC9C,MAAMxK,SAAS,GAAG,IAAI,CAAC6O,YAAY,CAAClD,aAAa,CAAC;IAClD,MAAMD,UAAU,GAAG1L,SAAS,CAACuK,YAAY,CAACC,SAAS,CAAC,CAAC;IACrD,IAAIkB,UAAU,KAAKG,SAAS,EAAE;MAC1BJ,SAAS,CAACjB,SAAS,EAAEkB,UAAU,EAAEC,aAAa,CAAC;IACnD;IACA,IAAI,IAAI,CAACgC,gBAAgB,EAAE;MACvB,IAAI,CAACG,uBAAuB,CAACnC,aAAa,EAAEnB,SAAS,EAAExK,SAAS,CAAC;IACrE;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI6O,YAAYA,CAAClD,aAAa,EAAE;IACxB,IAAI3L,SAAS,GAAGL,GAAG,CAACgM,aAAa,CAAC;IAClC,IAAI,CAAC3L,SAAS,EAAE;MACZ,MAAM8O,iBAAiB,GAAGnD,aAAa,CAAC/G,YAAY,CAACtF,SAAS,CAACC,QAAQ,CAAC;MACxE,IAAI,CAACuP,iBAAiB,EAAE;QACpB9O,SAAS,GAAGqN,gBAAgB;QAC5BtN,GAAG,CAAC4L,aAAa,EAAE3L,SAAS,CAAC;MACjC,CAAC,MACI;QACDA,SAAS,GAAGC,SAAS,CAAC6O,iBAAiB,CAAC;QACxC,IAAI,CAAC9O,SAAS,EAAE;UACZA,SAAS,GAAG,CAAC,CAAC;UACd,MAAM+O,MAAM,GAAGD,iBAAiB,CAACE,KAAK,CAAC1B,gBAAgB,CAAC;UACxD,KAAK,IAAI2B,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGF,MAAM,CAAC5E,MAAM,EAAE8E,GAAG,EAAE,EAAE;YAC1C,MAAM/G,KAAK,GAAG6G,MAAM,CAACE,GAAG,CAAC;YACzB,IAAI,CAAC/G,KAAK,EAAE;cACR;YACJ;YACA,MAAMgH,KAAK,GAAGhH,KAAK,CAAC7E,OAAO,CAAC+G,IAAI,CAACE,sBAAsB,CAAC;YACxD,MAAM6E,QAAQ,GAAGD,KAAK,KAAK,CAAC,CAAC;YAC7B,MAAMrI,IAAI,GAAGsI,QAAQ,GAAGjH,KAAK,CAACkH,MAAM,CAAC,CAAC,EAAEF,KAAK,CAAC,CAACG,IAAI,CAAC,CAAC,GAAG9B,kBAAkB;YAC1E,MAAMR,MAAM,GAAGoC,QAAQ,GAAGjH,KAAK,CAACkH,MAAM,CAACF,KAAK,GAAG,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC,GAAGnH,KAAK;YAChElI,SAAS,CAAC6G,IAAI,CAAC,GAAGkG,MAAM;UAC5B;UACA5M,SAAS,CAAC2O,iBAAiB,EAAE9O,SAAS,CAAC;QAC3C;QACAD,GAAG,CAAC4L,aAAa,EAAE3L,SAAS,CAAC;MACjC;IACJ;IACA,OAAOA,SAAS;EACpB;EACAsP,mBAAmBA,CAAC1B,2BAA2B,EAAEC,0BAA0B,EAAEC,uBAAuB,EAAE;IAClG,IAAI,CAACH,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,2BAA2B,GAAGA,2BAA2B;IAC9D,IAAI,CAACC,0BAA0B,GAAGA,0BAA0B;IAC5D,IAAI,CAACC,uBAAuB,GAAGA,uBAAuB;EAC1D;AACJ;;AAEA;AACA;AACA;AACA,IAAIyB,WAAW;AACf,CAAC,UAAUA,WAAW,EAAE;EACpBA,WAAW,CAACA,WAAW,CAAC,6BAA6B,CAAC,GAAG,CAAC,CAAC,GAAG,6BAA6B;AAC/F,CAAC,EAAEA,WAAW,KAAKA,WAAW,GAAG,CAAC,CAAC,CAAC,CAAC;;AAErC;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACb;AACJ;AACA;AACA;AACA;AACA;AACA;EACI7F,WAAWA,CAAC8F,gBAAgB,EAAE;IAAEC,cAAc;IAAEC;EAAe,CAAC,GAAG,CAAC,CAAC,EAAE;IACnE,IAAI,CAACF,gBAAgB,GAAGA,gBAAgB;IACxC;IACA,IAAI,CAACG,oBAAoB,GAAG,KAAK;IACjC;IACA,IAAI,CAACC,uBAAuB,GAAG,EAAE;IACjC,IAAI,CAACH,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,aAAa,GAAGA,aAAa;EACtC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIG,QAAQA,CAACtF,SAAS,EAAE;IAChB,MAAMuF,gBAAgB,GAAG,IAAI5C,gBAAgB,CAAC3C,SAAS,CAAC;IACxD,IAAI,CAACkF,cAAc,EAAE3B,gBAAgB,CAACvD,SAAS,CAAC;IAChD,IAAI,CAACkF,cAAc,EAAE1B,aAAa,CAACxD,SAAS,CAAC;IAC7C,MAAMuC,MAAM,GAAGgD,gBAAgB,CAACxE,SAAS,CAAC,CAAC;IAC3C,IAAIwB,MAAM,IAAIiD,qCAAqC,CAACjD,MAAM,CAACnN,OAAO,EAAEmQ,gBAAgB,CAAC,EAAE;MACnFxL,cAAc,CAACwL,gBAAgB,CAACrF,QAAQ,CAAC,CAAC,CAAC;IAC/C;IACA,IAAI,IAAI,CAACiF,aAAa,IAAII,gBAAgB,CAAC9D,WAAW,CAAC,CAAC,EAAE;MACtD,IAAI,CAACgE,8BAA8B,CAACF,gBAAgB,CAAC;MACrD;IACJ;IACA,IAAI,CAACN,gBAAgB,CAACM,gBAAgB,CAAC;EAC3C;EACA;AACJ;AACA;AACA;AACA;EACIE,8BAA8BA,CAACF,gBAAgB,EAAE;IAC7C,IAAI,CAACF,uBAAuB,CAAC7F,IAAI,CAAC+F,gBAAgB,CAAC;IACnD,IAAI,IAAI,CAACH,oBAAoB,EAAE;MAC3B;IACJ;IACA,IAAI,CAACA,oBAAoB,GAAG,IAAI;IAChCM,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MACzB,IAAI,CAACR,oBAAoB,GAAG,KAAK;MACjC,IAAI,CAACD,aAAa,CAAC,IAAI,CAACE,uBAAuB,CAAC;IACpD,CAAC,CAAC;EACN;AACJ;AACA;AACA;AACA;AACA;AACA,SAASQ,mBAAmBA,CAACjE,MAAM,EAAE;EACjC,OAAQkE,iBAAiB,IAAK;IAC1B,KAAK,MAAMP,gBAAgB,IAAIO,iBAAiB,EAAE;MAC9ClE,MAAM,CAAC2D,gBAAgB,CAAC;IAC5B;EACJ,CAAC;AACL;AACA;AACA;AACA;AACA;AACA,SAASC,qCAAqCA,CAACrE,aAAa,EAAEoE,gBAAgB,EAAE;EAC5E;EACA;EACA;EACA;EACA,OAAQpE,aAAa,CAACvF,OAAO,KAAK,GAAG,KAChC2J,gBAAgB,CAACxF,YAAY,CAAC,CAAC,KAAKjK,SAAS,CAACG,KAAK,IAChDsP,gBAAgB,CAACxF,YAAY,CAAC,CAAC,KAAKjK,SAAS,CAACI,QAAQ,CAAC;AACnE;AACA;AACA;AACA;AACA;AACA,SAAS6P,oBAAoBA,CAACC,aAAa,EAAEC,UAAU,EAAE;EACrDD,aAAa,CAACE,IAAI,CAAElG,SAAS,IAAK;IAC9BiG,UAAU,CAACX,QAAQ,CAACtF,SAAS,CAAC;EAClC,CAAC,EAAE+E,WAAW,CAACoB,2BAA2B,CAAC;AAC/C;;AAEA;AACA,MAAMC,0BAA0B,GAAGC,MAAM,CAACC,GAAG,CAAC,oBAAoB,CAAC;AACnE;AACA,MAAMC,UAAU,GAAG;EACfC,MAAM,EAAE;AACZ,CAAC;AACD,MAAMC,qCAAqC,GAAG,sFAAsF,GAChI,yFAAyF,GACzF,2CAA2C;AAC/C,MAAMC,6BAA6B,GAAG,gDAAgD;AACtF,MAAMC,mCAAmC,GAAG,6CAA6C,GACrF,wFAAwF,GACxF,wEAAwE;AAC5E,MAAMC,2BAA2B,GAAG,8CAA8C;AAClF;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAClB1H,WAAWA,CAAC8F,gBAAgB,EAAE/B,eAAe,GAAG,IAAI,EAAE;IAClD,IAAI,CAAC+B,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAAC/B,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACgC,cAAc,GAAG,IAAIlC,cAAc,CAAC;MAAEE;IAAgB,CAAC,CAAC;IAC7D,IAAI,CAAC+C,UAAU,GAAG,IAAIjB,UAAU,CAAEO,gBAAgB,IAAK;MACnD,IAAI,CAACuB,kBAAkB,CAACvB,gBAAgB,CAAC;IAC7C,CAAC,EAAE;MACCL,cAAc,EAAE,IAAI,CAACA;IACzB,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACII,QAAQA,CAACtF,SAAS,EAAE;IAChB,IAAI,CAACiG,UAAU,CAACX,QAAQ,CAACtF,SAAS,CAAC;EACvC;EACA;EACA8G,kBAAkBA,CAACvB,gBAAgB,EAAE;IACjC,IAAIA,gBAAgB,CAAC9D,WAAW,CAAC,CAAC,EAAE;MAChCsF,qBAAqB,CAACxB,gBAAgB,CAAC;IAC3C;IACAyB,uBAAuB,CAACzB,gBAAgB,CAAC;IACzC,OAAOA,gBAAgB,CAACxE,SAAS,CAAC,CAAC,EAAE;MACjCkG,uBAAuB,CAAC1B,gBAAgB,CAAC;MACzC;MACA,IAAI5M,kBAAkB,CAAC4M,gBAAgB,CAACxF,YAAY,CAAC,CAAC,CAAC,IACnDwF,gBAAgB,CAACxE,SAAS,CAAC,CAAC,CAAC3L,OAAO,KAAKmQ,gBAAgB,CAACnF,gBAAgB,CAAC,CAAC,EAAE;QAC9E;MACJ;MACA,IAAI,CAAC6E,gBAAgB,CAACM,gBAAgB,CAACrF,QAAQ,CAAC,CAAC,EAAEqF,gBAAgB,CAACxE,SAAS,CAAC,CAAC,CAAC2B,IAAI,CAAC;MACrF,IAAIwE,kBAAkB,CAAC3B,gBAAgB,CAAC,EAAE;QACtC;MACJ;MACA,IAAI,CAACL,cAAc,CAACxB,mBAAmB,CAAC6B,gBAAgB,CAACvF,SAAS,CAAC;IACvE;EACJ;AACJ;AACA,SAASgH,uBAAuBA,CAACzB,gBAAgB,EAAE;EAC/C,MAAM3H,KAAK,GAAG2H,gBAAgB,CAACrF,QAAQ,CAAC,CAAC;EACzC,MAAMiH,uBAAuB,GAAG5B,gBAAgB,CAACrF,QAAQ,CAAC,CAAC,CAACtG,eAAe,CAACwN,IAAI,CAACxJ,KAAK,CAAC;EACvF,MAAMhE,eAAe,GAAGA,CAAA,KAAM;IAC1BgE,KAAK,CAACwI,0BAA0B,CAAC,GAAG,IAAI;IACxCe,uBAAuB,CAAC,CAAC;EAC7B,CAAC;EACDE,kBAAkB,CAACzJ,KAAK,EAAE,iBAAiB,EAAEhE,eAAe,CAAC;EAC7DyN,kBAAkB,CAACzJ,KAAK,EAAE,0BAA0B,EAAEhE,eAAe,CAAC;AAC1E;AACA,SAASsN,kBAAkBA,CAAC3B,gBAAgB,EAAE;EAC1C,MAAM3H,KAAK,GAAG2H,gBAAgB,CAACrF,QAAQ,CAAC,CAAC;EACzC,OAAO,CAAC,CAACtC,KAAK,CAACwI,0BAA0B,CAAC;AAC9C;AACA,SAASW,qBAAqBA,CAACxB,gBAAgB,EAAE;EAC7C,MAAM3H,KAAK,GAAG2H,gBAAgB,CAACrF,QAAQ,CAAC,CAAC;EACzC,MAAM/F,MAAM,GAAGoL,gBAAgB,CAACnF,gBAAgB,CAAC,CAAC;EAClD,MAAMkH,sBAAsB,GAAG1J,KAAK,CAAC7D,cAAc,CAACqN,IAAI,CAACxJ,KAAK,CAAC;EAC/DyJ,kBAAkB,CAACzJ,KAAK,EAAE,QAAQ,EAAEzD,MAAM,CAAC;EAC3CkN,kBAAkB,CAACzJ,KAAK,EAAE,YAAY,EAAE2I,UAAU,CAACC,MAAM,CAAC;EAC1Da,kBAAkB,CAACzJ,KAAK,EAAE,gBAAgB,EAAE,MAAM;IAC9C0J,sBAAsB,CAAC,CAAC;IACxB,MAAM,IAAIC,KAAK,CAACb,6BAA6B,IAAIc,SAAS,GAAGf,qCAAqC,GAAG,EAAE,CAAC,CAAC;EAC7G,CAAC,CAAC;EACFY,kBAAkB,CAACzJ,KAAK,EAAE,cAAc,EAAE,MAAM;IAC5C,MAAM,IAAI2J,KAAK,CAACX,2BAA2B,IAAIY,SAAS,GAAGb,mCAAmC,GAAG,EAAE,CAAC,CAAC;EACzG,CAAC,CAAC;AACN;AACA,SAASM,uBAAuBA,CAAC1B,gBAAgB,EAAE;EAC/C,MAAM3H,KAAK,GAAG2H,gBAAgB,CAACrF,QAAQ,CAAC,CAAC;EACzC,MAAM0D,aAAa,GAAG2B,gBAAgB,CAACxE,SAAS,CAAC,CAAC,EAAE3L,OAAO;EAC3D,IAAIwO,aAAa,EAAE;IACfyD,kBAAkB,CAACzJ,KAAK,EAAE,eAAe,EAAEgG,aAAa,EAAE;MACtD;MACA6D,YAAY,EAAE;IAClB,CAAC,CAAC;EACN;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASJ,kBAAkBA,CAACzJ,KAAK,EAAEH,QAAQ,EAAEC,KAAK,EAAE;EAAE+J,YAAY,GAAG;AAAM,CAAC,GAAG,CAAC,CAAC,EAAE;EAC/EC,MAAM,CAACC,cAAc,CAAC/J,KAAK,EAAEH,QAAQ,EAAE;IAAEC,KAAK;IAAE+J;EAAa,CAAC,CAAC;AACnE;AACA;AACA;AACA;AACA;AACA,SAASG,oBAAoBA,CAAC5B,aAAa,EAAEC,UAAU,EAAE;EACrDD,aAAa,CAACE,IAAI,CAAElG,SAAS,IAAK;IAC9BiG,UAAU,CAACX,QAAQ,CAACtF,SAAS,CAAC;EAClC,CAAC,EAAE+E,WAAW,CAACoB,2BAA2B,CAAC;AAC/C;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAM0B,kBAAkB,CAAC;EACrB1I,WAAWA,CAAC2I,aAAa,GAAGC,MAAM,EAAErH,SAAS,GAAGqH,MAAM,CAACC,QAAQ,CAACC,eAAe,EAAE;IAC7E,IAAI,CAACH,aAAa,GAAGA,aAAa;IAClCA,aAAa,CAACI,KAAK,GAAGC,uBAAuB,CAACzH,SAAS,CAAC;EAC5D;EACA;AACJ;AACA;EACI0H,SAASA,CAACC,KAAK,EAAE7O,OAAO,EAAE;IACtB4O,SAAS,CAAC,IAAI,CAACN,aAAa,CAACI,KAAK,EAAEG,KAAK,EAAE7O,OAAO,CAAC;EACvD;AACJ;AACA;AACA,SAAS2O,uBAAuBA,CAACzH,SAAS,EAAE;EACxC,MAAM4H,CAAC,GAAG,EAAE;EACZ,MAAMC,CAAC,GAAIvI,SAAS,IAAK;IACrBsI,CAAC,CAAC9I,IAAI,CAACQ,SAAS,CAAC;EACrB,CAAC;EACD,MAAMwI,CAAC,GAAI5K,KAAK,IAAK;IACjB2K,CAAC,CAACjG,6BAA6B,CAAC1E,KAAK,CAACvB,IAAI,EAAEuB,KAAK,EAAEA,KAAK,CAACzD,MAAM,EAAEuG,SAAS,EAAEpC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC5F,CAAC;EACD,OAAO;IACHkK,CAAC,EAAE/H,SAAS;IACZ4H,CAAC;IACDI,EAAE,EAAE,EAAE;IACNC,GAAG,EAAE,EAAE;IACPJ,CAAC;IACDC;EACJ,CAAC;AACL;AACA;AACA,SAASJ,SAASA,CAACQ,iBAAiB,EAAEP,KAAK,EAAE7O,OAAO,EAAE;EAClD,KAAK,IAAIkG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2I,KAAK,CAAC1I,MAAM,EAAED,CAAC,EAAE,EAAE;IACnC,MAAM9G,SAAS,GAAGyP,KAAK,CAAC3I,CAAC,CAAC;IAC1B,MAAMmJ,UAAU,GAAGrP,OAAO,GAAGoP,iBAAiB,CAACD,GAAG,GAAGC,iBAAiB,CAACF,EAAE;IACzEG,UAAU,CAACrJ,IAAI,CAAC5G,SAAS,CAAC;IAC1BgQ,iBAAiB,CAACH,CAAC,CAACnP,gBAAgB,CAACV,SAAS,EAAEgQ,iBAAiB,CAACJ,CAAC,EAAEhP,OAAO,CAAC;EACjF;AACJ;AACA;AACA,SAASsP,mBAAmBA,CAACF,iBAAiB,EAAE;EAC5C,OAAOA,iBAAiB,EAAEN,CAAC,IAAI,EAAE;AACrC;AACA;AACA,SAASS,kBAAkBA,CAACH,iBAAiB,EAAE3C,UAAU,EAAE;EACvD,IAAI,CAAC2C,iBAAiB,EAAE;IACpB;EACJ;EACAA,iBAAiB,CAACL,CAAC,GAAGtC,UAAU;AACpC;AACA;AACA,SAAS+C,uBAAuBA,CAACJ,iBAAiB,EAAE;EAChD,IAAI,CAACA,iBAAiB,EAAE;IACpB;EACJ;EACAK,oBAAoB,CAACL,iBAAiB,CAACH,CAAC,EAAEG,iBAAiB,CAACF,EAAE,EAAEE,iBAAiB,CAACJ,CAAC,CAAC;EACpFS,oBAAoB,CAACL,iBAAiB,CAACH,CAAC,EAAEG,iBAAiB,CAACD,GAAG,EAAEC,iBAAiB,CAACJ,CAAC,EAAE,IAAI,CAAC;AAC/F;AACA,SAASS,oBAAoBA,CAACvI,SAAS,EAAEmI,UAAU,EAAEK,iBAAiB,EAAE1P,OAAO,EAAE;EAC7E,KAAK,IAAIkG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmJ,UAAU,CAAClJ,MAAM,EAAED,CAAC,EAAE,EAAE;IACxCgB,SAAS,CAACjH,mBAAmB,CAACoP,UAAU,CAACnJ,CAAC,CAAC,EAAEwJ,iBAAiB,EAAE,gBAAiB1P,OAAO,CAAC;EAC7F;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAM2P,qBAAqB,GAAG,KAAK;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChB;IAAS,IAAI,CAACD,qBAAqB,GAAGA,qBAAqB;EAAE;EAC7DhK,WAAWA,CAACkK,gBAAgB,EAAE;IAC1B;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,CAAC,CAAC;IACvB,IAAI,CAACC,iCAAiC,GAAG,CAAC,CAAC;IAC3C;AACR;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACtD,UAAU,GAAG,IAAI;IACtB;AACR;AACA;AACA;IACQ,IAAI,CAACuD,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACH,gBAAgB,GAAGA,gBAAgB;EAC5C;EACAI,WAAWA,CAAC7Q,SAAS,EAAEgF,KAAK,EAAE8C,SAAS,EAAE;IACrC,MAAMV,SAAS,GAAGsC,6BAA6B,CAC/C,gBAAiB1J,SAAS,EAC1B,YAAagF,KAAK,EAClB,oBAAqBA,KAAK,CAACzD,MAAM,EACjC,gBAAiBuG,SAAS,EAC1B,gBAAiBpC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;IAC5B,IAAI,CAACmL,eAAe,CAAC1J,SAAS,CAAC;EACnC;EACA;AACJ;AACA;EACI0J,eAAeA,CAAC1J,SAAS,EAAE;IACvB,IAAI,CAAC,IAAI,CAACiG,UAAU,EAAE;MAClB;MACAtE,WAAW,CAAC3B,SAAS,EAAE,IAAI,CAAC;MAC5B,IAAI,CAACwJ,gBAAgB,EAAEhK,IAAI,CAACQ,SAAS,CAAC;MACtC;IACJ;IACA,IAAI,CAACiG,UAAU,CAACjG,SAAS,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI2J,QAAQA,CAAC/Q,SAAS,EAAEgR,iBAAiB,EAAE;IACnC,IAAIhR,SAAS,IAAI,IAAI,CAAC0Q,aAAa,IAAI,CAAC,IAAI,CAACD,gBAAgB,EAAE;MAC3D;IACJ;IACA,IAAI,CAACD,aAAa,CAACD,qBAAqB,IAAI3Q,yBAAyB,CAACK,OAAO,CAACD,SAAS,CAAC,IAAI,CAAC,EAAE;MAC3F;IACJ;IACA,MAAMiR,YAAY,GAAGA,CAACjR,SAAS,EAAEgF,KAAK,EAAE8C,SAAS,KAAK;MAClD,IAAI,CAAC+I,WAAW,CAAC7Q,SAAS,EAAEgF,KAAK,EAAE8C,SAAS,CAAC;IACjD,CAAC;IACD;IACA,IAAI,CAAC4I,aAAa,CAAC1Q,SAAS,CAAC,GAAGiR,YAAY;IAC5C,MAAMC,gBAAgB,GAAGzQ,mBAAmB,CAACuQ,iBAAiB,IAAIhR,SAAS,CAAC;IAC5E,IAAIkR,gBAAgB,KAAKlR,SAAS,EAAE;MAChC,MAAMiQ,UAAU,GAAG,IAAI,CAACU,iCAAiC,CAACO,gBAAgB,CAAC,IAAI,EAAE;MACjFjB,UAAU,CAACrJ,IAAI,CAAC5G,SAAS,CAAC;MAC1B,IAAI,CAAC2Q,iCAAiC,CAACO,gBAAgB,CAAC,GAAGjB,UAAU;IACzE;IACA,IAAI,CAACQ,gBAAgB,CAAC/P,gBAAgB,CAACwQ,gBAAgB,EAAG1U,OAAO,IAAK;MAClE,OAAQwI,KAAK,IAAK;QACdiM,YAAY,CAACjR,SAAS,EAAEgF,KAAK,EAAExI,OAAO,CAAC;MAC3C,CAAC;IACL,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACI2U,iBAAiBA,CAACnB,iBAAiB,GAAGb,MAAM,CAACG,KAAK,EAAE;IAChD;IACA;IACA,IAAI,CAACU,iBAAiB,EAAE;MACpB;IACJ;IACA;IACA,IAAI,CAACoB,qBAAqB,CAACpB,iBAAiB,CAACN,CAAC,CAAC;IAC/C;IACAU,uBAAuB,CAACJ,iBAAiB,CAAC;IAC1C,OAAOb,MAAM,CAACG,KAAK;EACvB;EACA;AACJ;AACA;AACA;EACI8B,qBAAqBA,CAACC,eAAe,EAAE;IACnC,KAAK,IAAIvK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuK,eAAe,CAACtK,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7C,MAAMwK,cAAc,GAAGD,eAAe,CAACvK,CAAC,CAAC;MACzC,MAAMmJ,UAAU,GAAG,IAAI,CAACsB,gCAAgC,CAACD,cAAc,CAACtR,SAAS,CAAC;MAClF,KAAK,IAAIwR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvB,UAAU,CAAClJ,MAAM,EAAEyK,CAAC,EAAE,EAAE;QACxC,MAAMpK,SAAS,GAAGqC,cAAc,CAAC6H,cAAc,CAAC;QAChD;QACA;QACAjK,YAAY,CAACD,SAAS,EAAE6I,UAAU,CAACuB,CAAC,CAAC,CAAC;QACtC,IAAI,CAACV,eAAe,CAAC1J,SAAS,CAAC;MACnC;IACJ;EACJ;EACA;AACJ;AACA;AACA;EACImK,gCAAgCA,CAACL,gBAAgB,EAAE;IAC/C,MAAMjB,UAAU,GAAG,EAAE;IACrB,IAAI,IAAI,CAACS,aAAa,CAACQ,gBAAgB,CAAC,EAAE;MACtCjB,UAAU,CAACrJ,IAAI,CAACsK,gBAAgB,CAAC;IACrC;IACA,IAAI,IAAI,CAACP,iCAAiC,CAACO,gBAAgB,CAAC,EAAE;MAC1DjB,UAAU,CAACrJ,IAAI,CAAC,GAAG,IAAI,CAAC+J,iCAAiC,CAACO,gBAAgB,CAAC,CAAC;IAChF;IACA,OAAOjB,UAAU;EACrB;EACA;AACJ;AACA;EACItP,OAAOA,CAACX,SAAS,EAAE;IACf,OAAO,IAAI,CAAC0Q,aAAa,CAAC1Q,SAAS,CAAC;EACxC;EACA;AACJ;AACA;AACA;AACA;EACI6G,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC4J,gBAAgB,CAAC5J,OAAO,CAAC,CAAC;IAC/B,IAAI,CAAC4J,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,aAAa,GAAG,CAAC,CAAC;IACvB,IAAI,CAACC,iCAAiC,GAAG,CAAC,CAAC;IAC3C,IAAI,CAACtD,UAAU,GAAG,IAAI;IACtB,IAAI,CAACuD,gBAAgB,GAAG,EAAE;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIT,kBAAkBA,CAAC9C,UAAU,EAAEoE,WAAW,EAAE;IACxC,IAAI,CAACnE,IAAI,CAACD,UAAU,EAAEoE,WAAW,CAAC;EACtC;EACA;AACJ;AACA;AACA;AACA;EACInE,IAAIA,CAACD,UAAU,EAAEoE,WAAW,EAAE;IAC1B,IAAI,CAACpE,UAAU,GAAGA,UAAU;IAC5B,IAAI,IAAI,CAACuD,gBAAgB,EAAE7J,MAAM,EAAE;MAC/B,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC8J,gBAAgB,CAAC7J,MAAM,EAAED,CAAC,EAAE,EAAE;QACnD,IAAI,CAACgK,eAAe,CAAC,IAAI,CAACF,gBAAgB,CAAC9J,CAAC,CAAC,CAAC;MAClD;MACA,IAAI,CAAC8J,gBAAgB,GAAG,IAAI;IAChC;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA,SAASc,oCAAoCA,CAAC5J,SAAS,EAAE6J,KAAK,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAE3C,aAAa,GAAGC,MAAM,EAAE;EACzH,MAAMa,iBAAiB,GAAGT,uBAAuB,CAACzH,SAAS,CAAC;EAC5D,IAAI,CAACoH,aAAa,CAAC4C,MAAM,EAAE;IACvB5C,aAAa,CAAC4C,MAAM,GAAG,CAAC,CAAC;EAC7B;EACA5C,aAAa,CAAC4C,MAAM,CAACH,KAAK,CAAC,GAAG3B,iBAAiB;EAC/CR,SAAS,CAACQ,iBAAiB,EAAE4B,gBAAgB,CAAC;EAC9CpC,SAAS,CAACQ,iBAAiB,EAAE6B,iBAAiB,EAAE,cAAe,IAAI,CAAC;AACxE;AACA;AACA,SAASE,4BAA4BA,CAACJ,KAAK,EAAEzC,aAAa,GAAGC,MAAM,EAAE;EACjE,OAAOe,mBAAmB,CAAChB,aAAa,CAAC4C,MAAM,GAAGH,KAAK,CAAC,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA,SAASK,2BAA2BA,CAACP,WAAW,EAAEE,KAAK,EAAEtE,UAAU,EAAE6B,aAAa,GAAGC,MAAM,EAAE;EACzFgB,kBAAkB,CAACjB,aAAa,CAAC4C,MAAM,GAAGH,KAAK,CAAC,EAAEtE,UAAU,CAAC;AACjE;AACA;AACA,SAAS4E,gCAAgCA,CAACN,KAAK,EAAEzC,aAAa,GAAGC,MAAM,EAAE;EACrEiB,uBAAuB,CAAClB,aAAa,CAAC4C,MAAM,GAAGH,KAAK,CAAC,CAAC;AAC1D;AACA;AACA,SAASO,gCAAgCA,CAACP,KAAK,EAAEzC,aAAa,GAAGC,MAAM,EAAE;EACrE,IAAI,CAACD,aAAa,CAAC4C,MAAM,EAAE;IACvB;EACJ;EACA5C,aAAa,CAAC4C,MAAM,CAACH,KAAK,CAAC,GAAGlJ,SAAS;AAC3C;AAEA,SAASvM,SAAS,EAAEsU,aAAa,EAAElK,sBAAsB,EAAE2H,eAAe,EAAElE,gBAAgB,EAAE4D,UAAU,EAAE+D,oCAAoC,EAAEQ,gCAAgC,EAAEzV,YAAY,IAAI0V,cAAc,EAAEJ,4BAA4B,EAAEhS,kBAAkB,EAAEK,gBAAgB,EAAE4R,2BAA2B,EAAEhD,oBAAoB,IAAImB,kBAAkB,EAAE8B,gCAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}