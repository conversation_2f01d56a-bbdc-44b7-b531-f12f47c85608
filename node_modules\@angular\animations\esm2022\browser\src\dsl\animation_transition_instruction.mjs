/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
export function createTransitionInstruction(element, triggerName, fromState, toState, isRemovalTransition, fromStyles, toStyles, timelines, queriedElements, preStyleProps, postStyleProps, totalTime, errors) {
    return {
        type: 0 /* AnimationTransitionInstructionType.TransitionAnimation */,
        element,
        triggerName,
        isRemovalTransition,
        fromState,
        fromStyles,
        toState,
        toStyles,
        timelines,
        queriedElements,
        preStyleProps,
        postStyleProps,
        totalTime,
        errors,
    };
}
//# sourceMappingURL=data:application/json;base64,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