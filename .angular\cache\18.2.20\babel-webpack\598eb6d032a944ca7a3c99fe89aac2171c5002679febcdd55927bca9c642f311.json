{"ast": null, "code": "export const routes = [{\n  path: '',\n  loadComponent: () => import('./pages/home/<USER>').then(m => m.HomeComponent)\n}, {\n  path: 'our-story',\n  loadComponent: () => import('./pages/our-story/our-story.component').then(m => m.OurStoryComponent)\n}, {\n  path: 'love-notes',\n  loadComponent: () => import('./pages/love-notes/love-notes.component').then(m => m.LoveNotesComponent)\n}, {\n  path: 'memories',\n  loadComponent: () => import('./pages/memories/memories.component').then(m => m.MemoriesComponent)\n}, {\n  path: '**',\n  redirectTo: ''\n}];", "map": {"version": 3, "names": ["routes", "path", "loadComponent", "then", "m", "HomeComponent", "OurStoryComponent", "LoveNotesComponent", "MemoriesComponent", "redirectTo"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\1st-Monthsary-Tangi\\monthsary-website\\src\\app\\app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\n\nexport const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./pages/home/<USER>').then(m => m.HomeComponent)\n  },\n  {\n    path: 'our-story',\n    loadComponent: () => import('./pages/our-story/our-story.component').then(m => m.OurStoryComponent)\n  },\n  {\n    path: 'love-notes',\n    loadComponent: () => import('./pages/love-notes/love-notes.component').then(m => m.LoveNotesComponent)\n  },\n  {\n    path: 'memories',\n    loadComponent: () => import('./pages/memories/memories.component').then(m => m.MemoriesComponent)\n  },\n  {\n    path: '**',\n    redirectTo: ''\n  }\n];\n"], "mappings": "AAEA,OAAO,MAAMA,MAAM,GAAW,CAC5B;EACEC,IAAI,EAAE,EAAE;EACRC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa;CACrF,EACD;EACEJ,IAAI,EAAE,WAAW;EACjBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,iBAAiB;CACnG,EACD;EACEL,IAAI,EAAE,YAAY;EAClBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,yCAAyC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,kBAAkB;CACtG,EACD;EACEN,IAAI,EAAE,UAAU;EAChBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,iBAAiB;CACjG,EACD;EACEP,IAAI,EAAE,IAAI;EACVQ,UAAU,EAAE;CACb,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}