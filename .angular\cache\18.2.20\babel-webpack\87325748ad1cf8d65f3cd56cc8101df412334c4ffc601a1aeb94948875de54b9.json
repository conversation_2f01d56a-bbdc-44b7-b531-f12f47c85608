{"ast": null, "code": "import { Observable } from '../Observable';\nimport { async as asyncScheduler } from '../scheduler/async';\nimport { isScheduler } from '../util/isScheduler';\nimport { isValidDate } from '../util/isDate';\nexport function timer(dueTime = 0, intervalOrScheduler, scheduler = asyncScheduler) {\n  let intervalDuration = -1;\n  if (intervalOrScheduler != null) {\n    if (isScheduler(intervalOrScheduler)) {\n      scheduler = intervalOrScheduler;\n    } else {\n      intervalDuration = intervalOrScheduler;\n    }\n  }\n  return new Observable(subscriber => {\n    let due = isValidDate(dueTime) ? +dueTime - scheduler.now() : dueTime;\n    if (due < 0) {\n      due = 0;\n    }\n    let n = 0;\n    return scheduler.schedule(function () {\n      if (!subscriber.closed) {\n        subscriber.next(n++);\n        if (0 <= intervalDuration) {\n          this.schedule(undefined, intervalDuration);\n        } else {\n          subscriber.complete();\n        }\n      }\n    }, due);\n  });\n}", "map": {"version": 3, "names": ["Observable", "async", "asyncScheduler", "isScheduler", "isValidDate", "timer", "dueTime", "intervalOrScheduler", "scheduler", "intervalDuration", "subscriber", "due", "now", "n", "schedule", "closed", "next", "undefined", "complete"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/1st-Month<PERSON><PERSON>-<PERSON><PERSON>/monthsary-website/node_modules/rxjs/dist/esm/internal/observable/timer.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { async as asyncScheduler } from '../scheduler/async';\nimport { isScheduler } from '../util/isScheduler';\nimport { isValidDate } from '../util/isDate';\nexport function timer(dueTime = 0, intervalOrScheduler, scheduler = asyncScheduler) {\n    let intervalDuration = -1;\n    if (intervalOrScheduler != null) {\n        if (isScheduler(intervalOrScheduler)) {\n            scheduler = intervalOrScheduler;\n        }\n        else {\n            intervalDuration = intervalOrScheduler;\n        }\n    }\n    return new Observable((subscriber) => {\n        let due = isValidDate(dueTime) ? +dueTime - scheduler.now() : dueTime;\n        if (due < 0) {\n            due = 0;\n        }\n        let n = 0;\n        return scheduler.schedule(function () {\n            if (!subscriber.closed) {\n                subscriber.next(n++);\n                if (0 <= intervalDuration) {\n                    this.schedule(undefined, intervalDuration);\n                }\n                else {\n                    subscriber.complete();\n                }\n            }\n        }, due);\n    });\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,KAAK,IAAIC,cAAc,QAAQ,oBAAoB;AAC5D,SAASC,WAAW,QAAQ,qBAAqB;AACjD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAO,SAASC,KAAKA,CAACC,OAAO,GAAG,CAAC,EAAEC,mBAAmB,EAAEC,SAAS,GAAGN,cAAc,EAAE;EAChF,IAAIO,gBAAgB,GAAG,CAAC,CAAC;EACzB,IAAIF,mBAAmB,IAAI,IAAI,EAAE;IAC7B,IAAIJ,WAAW,CAACI,mBAAmB,CAAC,EAAE;MAClCC,SAAS,GAAGD,mBAAmB;IACnC,CAAC,MACI;MACDE,gBAAgB,GAAGF,mBAAmB;IAC1C;EACJ;EACA,OAAO,IAAIP,UAAU,CAAEU,UAAU,IAAK;IAClC,IAAIC,GAAG,GAAGP,WAAW,CAACE,OAAO,CAAC,GAAG,CAACA,OAAO,GAAGE,SAAS,CAACI,GAAG,CAAC,CAAC,GAAGN,OAAO;IACrE,IAAIK,GAAG,GAAG,CAAC,EAAE;MACTA,GAAG,GAAG,CAAC;IACX;IACA,IAAIE,CAAC,GAAG,CAAC;IACT,OAAOL,SAAS,CAACM,QAAQ,CAAC,YAAY;MAClC,IAAI,CAACJ,UAAU,CAACK,MAAM,EAAE;QACpBL,UAAU,CAACM,IAAI,CAACH,CAAC,EAAE,CAAC;QACpB,IAAI,CAAC,IAAIJ,gBAAgB,EAAE;UACvB,IAAI,CAACK,QAAQ,CAACG,SAAS,EAAER,gBAAgB,CAAC;QAC9C,CAAC,MACI;UACDC,UAAU,CAACQ,QAAQ,CAAC,CAAC;QACzB;MACJ;IACJ,CAAC,EAAEP,GAAG,CAAC;EACX,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}