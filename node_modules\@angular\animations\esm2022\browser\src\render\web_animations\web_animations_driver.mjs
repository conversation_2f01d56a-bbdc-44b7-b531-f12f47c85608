import { allowPreviousPlayerStylesMerge, balancePreviousStylesIntoKeyframes, camelCaseToDashCase, computeStyle, normalizeKeyframes, } from '../../util';
import { containsElement, getParentElement, invokeQuery, validateStyleProperty, validateWebAnimatableStyleProperty, } from '../shared';
import { packageNonAnimatableStyles } from '../special_cased_styles';
import { WebAnimationsPlayer } from './web_animations_player';
export class WebAnimationsDriver {
    validateStyleProperty(prop) {
        // Perform actual validation in dev mode only, in prod mode this check is a noop.
        if (typeof ngDevMode === 'undefined' || ngDevMode) {
            return validateStyleProperty(prop);
        }
        return true;
    }
    validateAnimatableStyleProperty(prop) {
        // Perform actual validation in dev mode only, in prod mode this check is a noop.
        if (typeof ngDevMode === 'undefined' || ngDevMode) {
            const cssProp = camelCaseToDashCase(prop);
            return validateWebAnimatableStyleProperty(cssProp);
        }
        return true;
    }
    containsElement(elm1, elm2) {
        return containsElement(elm1, elm2);
    }
    getParentElement(element) {
        return getParentElement(element);
    }
    query(element, selector, multi) {
        return invokeQuery(element, selector, multi);
    }
    computeStyle(element, prop, defaultValue) {
        return computeStyle(element, prop);
    }
    animate(element, keyframes, duration, delay, easing, previousPlayers = []) {
        const fill = delay == 0 ? 'both' : 'forwards';
        const playerOptions = { duration, delay, fill };
        // we check for this to avoid having a null|undefined value be present
        // for the easing (which results in an error for certain browsers #9752)
        if (easing) {
            playerOptions['easing'] = easing;
        }
        const previousStyles = new Map();
        const previousWebAnimationPlayers = (previousPlayers.filter((player) => player instanceof WebAnimationsPlayer));
        if (allowPreviousPlayerStylesMerge(duration, delay)) {
            previousWebAnimationPlayers.forEach((player) => {
                player.currentSnapshot.forEach((val, prop) => previousStyles.set(prop, val));
            });
        }
        let _keyframes = normalizeKeyframes(keyframes).map((styles) => new Map(styles));
        _keyframes = balancePreviousStylesIntoKeyframes(element, _keyframes, previousStyles);
        const specialStyles = packageNonAnimatableStyles(element, _keyframes);
        return new WebAnimationsPlayer(element, _keyframes, playerOptions, specialStyles);
    }
}
//# sourceMappingURL=data:application/json;base64,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