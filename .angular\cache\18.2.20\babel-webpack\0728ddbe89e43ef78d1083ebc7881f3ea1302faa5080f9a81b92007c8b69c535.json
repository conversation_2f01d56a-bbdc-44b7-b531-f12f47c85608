{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterLink } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nconst _forTrack0 = ($index, $item) => $item.id;\nfunction HomeComponent_For_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40);\n    i0.ɵɵtext(1, \" \\uD83D\\uDC96 \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const heart_r1 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"left\", heart_r1.left, \"%\")(\"top\", heart_r1.top, \"%\")(\"animation-delay\", heart_r1.delay + \"s\")(\"font-size\", heart_r1.size, \"px\");\n  }\n}\nexport class HomeComponent {\n  constructor() {\n    this.floatingHearts = [{\n      id: 1,\n      left: 10,\n      top: 20,\n      size: 20,\n      delay: 0\n    }, {\n      id: 2,\n      left: 80,\n      top: 10,\n      size: 25,\n      delay: 1\n    }, {\n      id: 3,\n      left: 60,\n      top: 70,\n      size: 18,\n      delay: 2\n    }, {\n      id: 4,\n      left: 30,\n      top: 50,\n      size: 22,\n      delay: 0.5\n    }];\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HomeComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 79,\n      vars: 0,\n      consts: [[1, \"min-h-screen\", \"flex\", \"items-center\", \"justify-center\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\"], [\"src\", \"https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2025&q=80\", \"alt\", \"Romantic couple silhouette at sunset\", 1, \"w-full\", \"h-full\", \"object-cover\", \"opacity-15\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-br\", \"from-pink-50/90\", \"to-purple-50/90\"], [1, \"absolute\", \"top-1/3\", \"left-1/4\", \"w-96\", \"h-96\", \"bg-pink-100\", \"rounded-full\", \"mix-blend-multiply\", \"filter\", \"blur-3xl\", \"opacity-30\", \"animate-gentle-glow\"], [1, \"absolute\", \"bottom-1/3\", \"right-1/4\", \"w-80\", \"h-80\", \"bg-purple-100\", \"rounded-full\", \"mix-blend-multiply\", \"filter\", \"blur-3xl\", \"opacity-30\", \"animate-gentle-glow\", \"animation-delay-2000\"], [1, \"absolute\", \"inset-0\", \"pointer-events-none\", \"overflow-hidden\"], [1, \"absolute\", \"text-pink-200\", \"opacity-30\", \"animate-gentle-float\", 3, \"left\", \"top\", \"animation-delay\", \"font-size\"], [1, \"relative\", \"z-10\", \"text-center\", \"px-6\", \"sm:px-8\", \"lg:px-12\", \"max-w-5xl\", \"mx-auto\"], [1, \"glass-effect\", \"rounded-3xl\", \"p-12\", \"sm:p-16\", \"lg:p-20\", \"shadow-xl\", \"border\", \"border-white/20\"], [1, \"mb-8\"], [1, \"text-4xl\", \"sm:text-5xl\", \"animate-soft-pulse\"], [1, \"font-dancing\", \"text-5xl\", \"sm:text-7xl\", \"lg:text-8xl\", \"text-romantic\", \"mb-6\", \"animate-soft-pulse\", \"leading-tight\"], [1, \"font-playfair\", \"text-2xl\", \"sm:text-3xl\", \"lg:text-4xl\", \"text-gray-600\", \"mb-8\", \"italic\", \"font-medium\"], [1, \"font-poppins\", \"text-lg\", \"sm:text-xl\", \"text-gray-600\", \"max-w-3xl\", \"mx-auto\", \"leading-relaxed\", \"mb-12\", \"font-light\"], [1, \"flex\", \"flex-col\", \"sm:flex-row\", \"gap-4\", \"justify-center\"], [\"routerLink\", \"/our-story\", 1, \"bg-gradient-to-r\", \"from-pink-300\", \"to-purple-300\", \"text-gray-700\", \"px-10\", \"py-4\", \"rounded-full\", \"font-poppins\", \"font-medium\", \"text-lg\", \"hover:shadow-xl\", \"transform\", \"hover:scale-105\", \"transition-all\", \"duration-500\", \"hover:from-pink-400\", \"hover:to-purple-400\", \"hover:text-white\"], [\"routerLink\", \"/love-notes\", 1, \"bg-white/50\", \"backdrop-blur-sm\", \"text-gray-700\", \"px-10\", \"py-4\", \"rounded-full\", \"font-poppins\", \"font-medium\", \"text-lg\", \"hover:shadow-xl\", \"transform\", \"hover:scale-105\", \"transition-all\", \"duration-500\", \"border\", \"border-pink-200\", \"hover:bg-pink-100\"], [1, \"absolute\", \"top-20\", \"left-20\", \"text-pink-200\", \"text-2xl\", \"animate-gentle-glow\"], [1, \"absolute\", \"bottom-20\", \"right-20\", \"text-purple-200\", \"text-2xl\", \"animate-gentle-glow\", \"animation-delay-2000\"], [1, \"py-20\", \"px-6\", \"sm:px-8\", \"lg:px-12\"], [1, \"max-w-6xl\", \"mx-auto\"], [1, \"text-center\", \"mb-16\"], [1, \"font-playfair\", \"text-4xl\", \"sm:text-5xl\", \"text-romantic\", \"mb-6\"], [1, \"font-poppins\", \"text-xl\", \"text-gray-600\", \"max-w-2xl\", \"mx-auto\", \"font-light\"], [1, \"grid\", \"grid-cols-1\", \"md:grid-cols-3\", \"gap-8\"], [1, \"glass-effect\", \"rounded-2xl\", \"overflow-hidden\", \"card-hover\", \"border\", \"border-white/20\", \"group\"], [1, \"relative\", \"h-32\", \"overflow-hidden\"], [\"src\", \"https://images.unsplash.com/photo-1516589178581-6cd7833ae3b2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80\", \"alt\", \"Our Story\", 1, \"w-full\", \"h-full\", \"object-cover\", \"group-hover:scale-110\", \"transition-transform\", \"duration-700\"], [1, \"absolute\", \"inset-0\", \"bg-gradient-to-t\", \"from-black/30\", \"to-transparent\"], [1, \"absolute\", \"top-2\", \"left-2\"], [1, \"text-3xl\", \"animate-soft-pulse\", \"drop-shadow-lg\"], [1, \"p-6\", \"text-center\"], [1, \"font-playfair\", \"text-2xl\", \"font-semibold\", \"text-gray-700\", \"mb-4\"], [1, \"font-poppins\", \"text-gray-600\", \"mb-6\", \"font-light\"], [\"routerLink\", \"/our-story\", 1, \"text-romantic\", \"font-medium\", \"hover:underline\"], [\"src\", \"https://images.unsplash.com/photo-1518568814500-bf0f8d125f46?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80\", \"alt\", \"Love Notes\", 1, \"w-full\", \"h-full\", \"object-cover\", \"group-hover:scale-110\", \"transition-transform\", \"duration-700\"], [\"routerLink\", \"/love-notes\", 1, \"text-romantic\", \"font-medium\", \"hover:underline\"], [\"src\", \"https://images.unsplash.com/photo-1529333166437-7750a6dd5a70?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80\", \"alt\", \"Memories\", 1, \"w-full\", \"h-full\", \"object-cover\", \"group-hover:scale-110\", \"transition-transform\", \"duration-700\"], [\"routerLink\", \"/memories\", 1, \"text-romantic\", \"font-medium\", \"hover:underline\"], [1, \"absolute\", \"text-pink-200\", \"opacity-30\", \"animate-gentle-float\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1);\n          i0.ɵɵelement(2, \"img\", 2)(3, \"div\", 3)(4, \"div\", 4)(5, \"div\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 6);\n          i0.ɵɵrepeaterCreate(7, HomeComponent_For_8_Template, 2, 8, \"div\", 7, _forTrack0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 8)(10, \"div\", 9)(11, \"div\", 10)(12, \"span\", 11);\n          i0.ɵɵtext(13, \"\\uD83D\\uDC96\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(14, \"h1\", 12);\n          i0.ɵɵtext(15, \" Happy 1st Monthsary \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"h2\", 13);\n          i0.ɵɵtext(17, \" My Tangi \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"p\", 14);\n          i0.ɵɵtext(19, \" Welcome to our digital love story, where every moment is treasured and every memory is painted with pure affection \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 15)(21, \"a\", 16);\n          i0.ɵɵtext(22, \" Our Story \\u2728 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"a\", 17);\n          i0.ɵɵtext(24, \" Love Notes \\uD83D\\uDC95 \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(25, \"div\", 18);\n          i0.ɵɵtext(26, \"\\u2728\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"div\", 19);\n          i0.ɵɵtext(28, \"\\uD83D\\uDCAB\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"section\", 20)(30, \"div\", 21)(31, \"div\", 22)(32, \"h2\", 23);\n          i0.ɵɵtext(33, \" Our Journey Together \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(34, \"p\", 24);\n          i0.ɵɵtext(35, \" Every day with you is a new chapter in our beautiful love story \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(36, \"div\", 25)(37, \"div\", 26)(38, \"div\", 27);\n          i0.ɵɵelement(39, \"img\", 28)(40, \"div\", 29);\n          i0.ɵɵelementStart(41, \"div\", 30)(42, \"div\", 31);\n          i0.ɵɵtext(43, \"\\uD83D\\uDCD6\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(44, \"div\", 32)(45, \"h3\", 33);\n          i0.ɵɵtext(46, \"Our Story\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"p\", 34);\n          i0.ɵɵtext(48, \"From our first glance to this beautiful moment\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"a\", 35);\n          i0.ɵɵtext(50, \"Read More \\u2192\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(51, \"div\", 26)(52, \"div\", 27);\n          i0.ɵɵelement(53, \"img\", 36)(54, \"div\", 29);\n          i0.ɵɵelementStart(55, \"div\", 30)(56, \"div\", 31);\n          i0.ɵɵtext(57, \"\\uD83D\\uDC8C\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(58, \"div\", 32)(59, \"h3\", 33);\n          i0.ɵɵtext(60, \"Love Notes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"p\", 34);\n          i0.ɵɵtext(62, \"All the things I love about you\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(63, \"a\", 37);\n          i0.ɵɵtext(64, \"Discover \\u2192\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(65, \"div\", 26)(66, \"div\", 27);\n          i0.ɵɵelement(67, \"img\", 38)(68, \"div\", 29);\n          i0.ɵɵelementStart(69, \"div\", 30)(70, \"div\", 31);\n          i0.ɵɵtext(71, \"\\uD83D\\uDCF8\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(72, \"div\", 32)(73, \"h3\", 33);\n          i0.ɵɵtext(74, \"Memories\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"p\", 34);\n          i0.ɵɵtext(76, \"Our precious moments together\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"a\", 39);\n          i0.ɵɵtext(78, \"Explore \\u2192\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(7);\n          i0.ɵɵrepeater(ctx.floatingHearts);\n        }\n      },\n      dependencies: [CommonModule, RouterLink],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterLink", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵstyleProp", "heart_r1", "left", "top", "delay", "size", "HomeComponent", "constructor", "floatingHearts", "id", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵrepeaterCreate", "HomeComponent_For_8_Template", "_forTrack0", "ɵɵadvance", "ɵɵrepeater", "encapsulation"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\monthsary-website\\src\\app\\pages\\home\\home.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterLink } from '@angular/router';\n\n@Component({\n  selector: 'app-home',\n  standalone: true,\n  imports: [CommonModule, RouterLink],\n  template: `\n    <!-- Hero Section -->\n    <section class=\"min-h-screen flex items-center justify-center relative overflow-hidden\">\n      <!-- Beautiful Background Image -->\n      <div class=\"absolute inset-0\">\n        <img\n          src=\"https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2025&q=80\"\n          alt=\"Romantic couple silhouette at sunset\"\n          class=\"w-full h-full object-cover opacity-15\"\n        />\n        <div class=\"absolute inset-0 bg-gradient-to-br from-pink-50/90 to-purple-50/90\"></div>\n        <div class=\"absolute top-1/3 left-1/4 w-96 h-96 bg-pink-100 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-gentle-glow\"></div>\n        <div class=\"absolute bottom-1/3 right-1/4 w-80 h-80 bg-purple-100 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-gentle-glow animation-delay-2000\"></div>\n      </div>\n\n      <!-- Floating Hearts -->\n      <div class=\"absolute inset-0 pointer-events-none overflow-hidden\">\n        @for (heart of floatingHearts; track heart.id) {\n          <div\n            class=\"absolute text-pink-200 opacity-30 animate-gentle-float\"\n            [style.left.%]=\"heart.left\"\n            [style.top.%]=\"heart.top\"\n            [style.animation-delay]=\"heart.delay + 's'\"\n            [style.font-size.px]=\"heart.size\">\n            💖\n          </div>\n        }\n      </div>\n\n      <!-- Main Content -->\n      <div class=\"relative z-10 text-center px-6 sm:px-8 lg:px-12 max-w-5xl mx-auto\">\n        <!-- Elegant Glass Card -->\n        <div class=\"glass-effect rounded-3xl p-12 sm:p-16 lg:p-20 shadow-xl border border-white/20\">\n          <div class=\"mb-8\">\n            <span class=\"text-4xl sm:text-5xl animate-soft-pulse\">💖</span>\n          </div>\n          \n          <h1 class=\"font-dancing text-5xl sm:text-7xl lg:text-8xl text-romantic mb-6 animate-soft-pulse leading-tight\">\n            Happy 1st Monthsary\n          </h1>\n          \n          <h2 class=\"font-playfair text-2xl sm:text-3xl lg:text-4xl text-gray-600 mb-8 italic font-medium\">\n            My Tangi \n          </h2>\n          \n          <p class=\"font-poppins text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-12 font-light\">\n            Welcome to our digital love story, where every moment is treasured\n            and every memory is painted with pure affection\n          </p>\n          \n          <!-- Navigation Buttons -->\n          <div class=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <a routerLink=\"/our-story\" \n               class=\"bg-gradient-to-r from-pink-300 to-purple-300 text-gray-700 px-10 py-4 rounded-full font-poppins font-medium text-lg hover:shadow-xl transform hover:scale-105 transition-all duration-500 hover:from-pink-400 hover:to-purple-400 hover:text-white\">\n              Our Story ✨\n            </a>\n            <a routerLink=\"/love-notes\" \n               class=\"bg-white/50 backdrop-blur-sm text-gray-700 px-10 py-4 rounded-full font-poppins font-medium text-lg hover:shadow-xl transform hover:scale-105 transition-all duration-500 border border-pink-200 hover:bg-pink-100\">\n              Love Notes 💕\n            </a>\n          </div>\n        </div>\n      </div>\n\n      <!-- Minimal Sparkle Effects -->\n      <div class=\"absolute top-20 left-20 text-pink-200 text-2xl animate-gentle-glow\">✨</div>\n      <div class=\"absolute bottom-20 right-20 text-purple-200 text-2xl animate-gentle-glow animation-delay-2000\">💫</div>\n    </section>\n\n    <!-- Quick Preview Section -->\n    <section class=\"py-20 px-6 sm:px-8 lg:px-12\">\n      <div class=\"max-w-6xl mx-auto\">\n        <div class=\"text-center mb-16\">\n          <h2 class=\"font-playfair text-4xl sm:text-5xl text-romantic mb-6\">\n            Our Journey Together\n          </h2>\n          <p class=\"font-poppins text-xl text-gray-600 max-w-2xl mx-auto font-light\">\n            Every day with you is a new chapter in our beautiful love story\n          </p>\n        </div>\n\n        <div class=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n          <!-- Story Preview -->\n          <div class=\"glass-effect rounded-2xl overflow-hidden card-hover border border-white/20 group\">\n            <div class=\"relative h-32 overflow-hidden\">\n              <img\n                src=\"https://images.unsplash.com/photo-1516589178581-6cd7833ae3b2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80\"\n                alt=\"Our Story\"\n                class=\"w-full h-full object-cover group-hover:scale-110 transition-transform duration-700\"\n              />\n              <div class=\"absolute inset-0 bg-gradient-to-t from-black/30 to-transparent\"></div>\n              <div class=\"absolute top-2 left-2\">\n                <div class=\"text-3xl animate-soft-pulse drop-shadow-lg\">📖</div>\n              </div>\n            </div>\n            <div class=\"p-6 text-center\">\n              <h3 class=\"font-playfair text-2xl font-semibold text-gray-700 mb-4\">Our Story</h3>\n              <p class=\"font-poppins text-gray-600 mb-6 font-light\">From our first glance to this beautiful moment</p>\n              <a routerLink=\"/our-story\" class=\"text-romantic font-medium hover:underline\">Read More →</a>\n            </div>\n          </div>\n\n          <!-- Love Notes Preview -->\n          <div class=\"glass-effect rounded-2xl overflow-hidden card-hover border border-white/20 group\">\n            <div class=\"relative h-32 overflow-hidden\">\n              <img\n                src=\"https://images.unsplash.com/photo-1518568814500-bf0f8d125f46?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80\"\n                alt=\"Love Notes\"\n                class=\"w-full h-full object-cover group-hover:scale-110 transition-transform duration-700\"\n              />\n              <div class=\"absolute inset-0 bg-gradient-to-t from-black/30 to-transparent\"></div>\n              <div class=\"absolute top-2 left-2\">\n                <div class=\"text-3xl animate-soft-pulse drop-shadow-lg\">💌</div>\n              </div>\n            </div>\n            <div class=\"p-6 text-center\">\n              <h3 class=\"font-playfair text-2xl font-semibold text-gray-700 mb-4\">Love Notes</h3>\n              <p class=\"font-poppins text-gray-600 mb-6 font-light\">All the things I love about you</p>\n              <a routerLink=\"/love-notes\" class=\"text-romantic font-medium hover:underline\">Discover →</a>\n            </div>\n          </div>\n\n          <!-- Memories Preview -->\n          <div class=\"glass-effect rounded-2xl overflow-hidden card-hover border border-white/20 group\">\n            <div class=\"relative h-32 overflow-hidden\">\n              <img\n                src=\"https://images.unsplash.com/photo-1529333166437-7750a6dd5a70?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80\"\n                alt=\"Memories\"\n                class=\"w-full h-full object-cover group-hover:scale-110 transition-transform duration-700\"\n              />\n              <div class=\"absolute inset-0 bg-gradient-to-t from-black/30 to-transparent\"></div>\n              <div class=\"absolute top-2 left-2\">\n                <div class=\"text-3xl animate-soft-pulse drop-shadow-lg\">📸</div>\n              </div>\n            </div>\n            <div class=\"p-6 text-center\">\n              <h3 class=\"font-playfair text-2xl font-semibold text-gray-700 mb-4\">Memories</h3>\n              <p class=\"font-poppins text-gray-600 mb-6 font-light\">Our precious moments together</p>\n              <a routerLink=\"/memories\" class=\"text-romantic font-medium hover:underline\">Explore →</a>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  `,\n  styles: []\n})\nexport class HomeComponent {\n  floatingHearts = [\n    { id: 1, left: 10, top: 20, size: 20, delay: 0 },\n    { id: 2, left: 80, top: 10, size: 25, delay: 1 },\n    { id: 3, left: 60, top: 70, size: 18, delay: 2 },\n    { id: 4, left: 30, top: 50, size: 22, delay: 0.5 }\n  ];\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,UAAU,QAAQ,iBAAiB;;;;;IAwBlCC,EAAA,CAAAC,cAAA,cAKoC;IAClCD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAFJH,EAHA,CAAAI,WAAA,SAAAC,QAAA,CAAAC,IAAA,MAA2B,QAAAD,QAAA,CAAAE,GAAA,MACF,oBAAAF,QAAA,CAAAG,KAAA,OACkB,cAAAH,QAAA,CAAAI,IAAA,OACV;;;AA4H7C,OAAM,MAAOC,aAAa;EAvJ1BC,YAAA;IAwJE,KAAAC,cAAc,GAAG,CACf;MAAEC,EAAE,EAAE,CAAC;MAAEP,IAAI,EAAE,EAAE;MAAEC,GAAG,EAAE,EAAE;MAAEE,IAAI,EAAE,EAAE;MAAED,KAAK,EAAE;IAAC,CAAE,EAChD;MAAEK,EAAE,EAAE,CAAC;MAAEP,IAAI,EAAE,EAAE;MAAEC,GAAG,EAAE,EAAE;MAAEE,IAAI,EAAE,EAAE;MAAED,KAAK,EAAE;IAAC,CAAE,EAChD;MAAEK,EAAE,EAAE,CAAC;MAAEP,IAAI,EAAE,EAAE;MAAEC,GAAG,EAAE,EAAE;MAAEE,IAAI,EAAE,EAAE;MAAED,KAAK,EAAE;IAAC,CAAE,EAChD;MAAEK,EAAE,EAAE,CAAC;MAAEP,IAAI,EAAE,EAAE;MAAEC,GAAG,EAAE,EAAE;MAAEE,IAAI,EAAE,EAAE;MAAED,KAAK,EAAE;IAAG,CAAE,CACnD;;;;uCANUE,aAAa;IAAA;EAAA;;;YAAbA,aAAa;MAAAI,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAhB,EAAA,CAAAiB,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA/IpBvB,EAFF,CAAAC,cAAA,iBAAwF,aAExD;UAQ5BD,EAPA,CAAAyB,SAAA,aAIE,aACoF,aAC4D,aAC2B;UAC/KzB,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,aAAkE;UAChED,EAAA,CAAA0B,gBAAA,IAAAC,4BAAA,kBAAAC,UAAA,CASC;UACH5B,EAAA,CAAAG,YAAA,EAAM;UAOAH,EAJN,CAAAC,cAAA,aAA+E,cAEe,eACxE,gBACsC;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAC1DF,EAD0D,CAAAG,YAAA,EAAO,EAC3D;UAENH,EAAA,CAAAC,cAAA,cAA8G;UAC5GD,EAAA,CAAAE,MAAA,6BACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAELH,EAAA,CAAAC,cAAA,cAAiG;UAC/FD,EAAA,CAAAE,MAAA,kBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAELH,EAAA,CAAAC,cAAA,aAA4G;UAC1GD,EAAA,CAAAE,MAAA,4HAEF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAIFH,EADF,CAAAC,cAAA,eAA4D,aAEoM;UAC5PD,EAAA,CAAAE,MAAA,0BACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACJH,EAAA,CAAAC,cAAA,aAC8N;UAC5ND,EAAA,CAAAE,MAAA,iCACF;UAGNF,EAHM,CAAAG,YAAA,EAAI,EACA,EACF,EACF;UAGNH,EAAA,CAAAC,cAAA,eAAgF;UAAAD,EAAA,CAAAE,MAAA,cAAC;UAAAF,EAAA,CAAAG,YAAA,EAAM;UACvFH,EAAA,CAAAC,cAAA,eAA2G;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAC/GF,EAD+G,CAAAG,YAAA,EAAM,EAC3G;UAMJH,EAHN,CAAAC,cAAA,mBAA6C,eACZ,eACE,cACqC;UAChED,EAAA,CAAAE,MAAA,8BACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,aAA2E;UACzED,EAAA,CAAAE,MAAA,yEACF;UACFF,EADE,CAAAG,YAAA,EAAI,EACA;UAKFH,EAHJ,CAAAC,cAAA,eAAmD,eAE6C,eACjD;UAMzCD,EALA,CAAAyB,SAAA,eAIE,eACgF;UAEhFzB,EADF,CAAAC,cAAA,eAAmC,eACuB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAE9DF,EAF8D,CAAAG,YAAA,EAAM,EAC5D,EACF;UAEJH,EADF,CAAAC,cAAA,eAA6B,cACyC;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAClFH,EAAA,CAAAC,cAAA,aAAsD;UAAAD,EAAA,CAAAE,MAAA,sDAA8C;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACxGH,EAAA,CAAAC,cAAA,aAA6E;UAAAD,EAAA,CAAAE,MAAA,wBAAW;UAE5FF,EAF4F,CAAAG,YAAA,EAAI,EACxF,EACF;UAIJH,EADF,CAAAC,cAAA,eAA8F,eACjD;UAMzCD,EALA,CAAAyB,SAAA,eAIE,eACgF;UAEhFzB,EADF,CAAAC,cAAA,eAAmC,eACuB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAE9DF,EAF8D,CAAAG,YAAA,EAAM,EAC5D,EACF;UAEJH,EADF,CAAAC,cAAA,eAA6B,cACyC;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACnFH,EAAA,CAAAC,cAAA,aAAsD;UAAAD,EAAA,CAAAE,MAAA,uCAA+B;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACzFH,EAAA,CAAAC,cAAA,aAA8E;UAAAD,EAAA,CAAAE,MAAA,uBAAU;UAE5FF,EAF4F,CAAAG,YAAA,EAAI,EACxF,EACF;UAIJH,EADF,CAAAC,cAAA,eAA8F,eACjD;UAMzCD,EALA,CAAAyB,SAAA,eAIE,eACgF;UAEhFzB,EADF,CAAAC,cAAA,eAAmC,eACuB;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAE9DF,EAF8D,CAAAG,YAAA,EAAM,EAC5D,EACF;UAEJH,EADF,CAAAC,cAAA,eAA6B,cACyC;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACjFH,EAAA,CAAAC,cAAA,aAAsD;UAAAD,EAAA,CAAAE,MAAA,qCAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACvFH,EAAA,CAAAC,cAAA,aAA4E;UAAAD,EAAA,CAAAE,MAAA,sBAAS;UAK/FF,EAL+F,CAAAG,YAAA,EAAI,EACrF,EACF,EACF,EACF,EACE;;;UA9HNH,EAAA,CAAA6B,SAAA,GASC;UATD7B,EAAA,CAAA8B,UAAA,CAAAN,GAAA,CAAAZ,cAAA,CASC;;;qBA3BGd,YAAY,EAAEC,UAAU;MAAAgC,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}