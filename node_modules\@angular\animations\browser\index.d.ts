/**
 * @license Angular v18.2.13
 * (c) 2010-2024 Google LLC. https://angular.io/
 * License: MIT
 */


import { AnimationMetadata } from '@angular/animations';
import { AnimationOptions } from '@angular/animations';
import { AnimationPlayer } from '@angular/animations';
import { AnimationTriggerMetadata } from '@angular/animations';
import * as i0 from '@angular/core';
import type { NgZone } from '@angular/core';
import { Renderer2 } from '@angular/core';
import { RendererFactory2 } from '@angular/core';
import { RendererStyleFlags2 } from '@angular/core';
import type { RendererType2 } from '@angular/core';
import { ɵAnimationRendererType } from '@angular/core';
import { ɵStyleData } from '@angular/animations';
import { ɵStyleDataMap } from '@angular/animations';

/**
 * @publicApi
 */
export declare abstract class AnimationDriver {
    /**
     * @deprecated Use the NoopAnimationDriver class.
     */
    static NOOP: AnimationDriver;
    abstract validateStyleProperty(prop: string): boolean;
    abstract validateAnimatableStyleProperty?: (prop: string) => boolean;
    abstract containsElement(elm1: any, elm2: any): boolean;
    /**
     * Obtains the parent element, if any. `null` is returned if the element does not have a parent.
     */
    abstract getParentElement(element: unknown): unknown;
    abstract query(element: any, selector: string, multi: boolean): any[];
    abstract computeStyle(element: any, prop: string, defaultValue?: string): string;
    abstract animate(element: any, keyframes: Array<Map<string, string | number>>, duration: number, delay: number, easing?: string | null, previousPlayers?: any[], scrubberAccessRequested?: boolean): any;
}

declare interface AnimationEngineInstruction {
    type: AnimationTransitionInstructionType;
}

declare type AnimationFactoryWithListenerCallback = RendererFactory2 & {
    scheduleListenerCallback: (count: number, fn: (e: any) => any, data: any) => void;
};

declare interface AnimationTimelineInstruction extends AnimationEngineInstruction {
    element: any;
    keyframes: Array<ɵStyleDataMap>;
    preStyleProps: string[];
    postStyleProps: string[];
    duration: number;
    delay: number;
    totalTime: number;
    easing: string | null;
    stretchStartingKeyframe?: boolean;
    subTimeline: boolean;
}


declare const enum AnimationTransitionInstructionType {
    TransitionAnimation = 0,
    TimelineAnimation = 1
}

declare class ElementInstructionMap {
    private _map;
    get(element: any): AnimationTimelineInstruction[];
    append(element: any, instructions: AnimationTimelineInstruction[]): void;
    has(element: any): boolean;
    clear(): void;
}

/**
 * @publicApi
 *
 * `AnimationDriver` implentation for Noop animations
 */
export declare class NoopAnimationDriver implements AnimationDriver {
    /**
     * @returns Whether `prop` is a valid CSS property
     */
    validateStyleProperty(prop: string): boolean;
    /**
     *
     * @returns Whether elm1 contains elm2.
     */
    containsElement(elm1: any, elm2: any): boolean;
    /**
     * @returns Rhe parent of the given element or `null` if the element is the `document`
     */
    getParentElement(element: unknown): unknown;
    /**
     * @returns The result of the query selector on the element. The array will contain up to 1 item
     *     if `multi` is  `false`.
     */
    query(element: any, selector: string, multi: boolean): any[];
    /**
     * @returns The `defaultValue` or empty string
     */
    computeStyle(element: any, prop: string, defaultValue?: string): string;
    /**
     * @returns An `NoopAnimationPlayer`
     */
    animate(element: any, keyframes: Array<Map<string, string | number>>, duration: number, delay: number, easing: string, previousPlayers?: any[], scrubberAccessRequested?: boolean): AnimationPlayer;
    static ɵfac: i0.ɵɵFactoryDeclaration<NoopAnimationDriver, never>;
    static ɵprov: i0.ɵɵInjectableDeclaration<NoopAnimationDriver>;
}

/**
 * Designed to be executed during a keyframe-based animation to apply any special-cased styles.
 *
 * When started (when the `start()` method is run) then the provided `startStyles`
 * will be applied. When finished (when the `finish()` method is called) the
 * `endStyles` will be applied as well any any starting styles. Finally when
 * `destroy()` is called then all styles will be removed.
 */
declare class SpecialCasedStyles {
    private _element;
    private _startStyles;
    private _endStyles;
    static initialStylesByElement: WeakMap<any, ɵStyleDataMap>;
    private _state;
    private _initialStyles;
    constructor(_element: any, _startStyles: ɵStyleDataMap | null, _endStyles: ɵStyleDataMap | null);
    start(): void;
    finish(): void;
    destroy(): void;
}

export declare function ɵallowPreviousPlayerStylesMerge(duration: number, delay: number): boolean;

export declare class ɵAnimation {
    private _driver;
    private _animationAst;
    constructor(_driver: AnimationDriver, input: AnimationMetadata | AnimationMetadata[]);
    buildTimelines(element: any, startingStyles: ɵStyleDataMap | Array<ɵStyleDataMap>, destinationStyles: ɵStyleDataMap | Array<ɵStyleDataMap>, options: AnimationOptions, subInstructions?: ElementInstructionMap): AnimationTimelineInstruction[];
}

export declare class ɵAnimationEngine {
    private _driver;
    private _normalizer;
    private _transitionEngine;
    private _timelineEngine;
    private _triggerCache;
    onRemovalComplete: (element: any, context: any) => void;
    constructor(doc: Document, _driver: AnimationDriver, _normalizer: ɵAnimationStyleNormalizer);
    registerTrigger(componentId: string, namespaceId: string, hostElement: any, name: string, metadata: AnimationTriggerMetadata): void;
    register(namespaceId: string, hostElement: any): void;
    destroy(namespaceId: string, context: any): void;
    onInsert(namespaceId: string, element: any, parent: any, insertBefore: boolean): void;
    onRemove(namespaceId: string, element: any, context: any): void;
    disableAnimations(element: any, disable: boolean): void;
    process(namespaceId: string, element: any, property: string, value: any): void;
    listen(namespaceId: string, element: any, eventName: string, eventPhase: string, callback: (event: any) => any): () => any;
    flush(microtaskId?: number): void;
    get players(): AnimationPlayer[];
    whenRenderingDone(): Promise<any>;
    afterFlushAnimationsDone(cb: VoidFunction): void;
}

export declare class ɵAnimationRenderer extends ɵBaseAnimationRenderer implements Renderer2 {
    factory: AnimationFactoryWithListenerCallback;
    constructor(factory: AnimationFactoryWithListenerCallback, namespaceId: string, delegate: Renderer2, engine: ɵAnimationEngine, onDestroy?: () => void);
    setProperty(el: any, name: string, value: any): void;
    listen(target: 'window' | 'document' | 'body' | any, eventName: string, callback: (event: any) => any): () => void;
}

export declare class ɵAnimationRendererFactory implements RendererFactory2 {
    private delegate;
    private engine;
    private _zone;
    private _currentId;
    private _microtaskId;
    private _animationCallbacksBuffer;
    private _rendererCache;
    private _cdRecurDepth;
    constructor(delegate: RendererFactory2, engine: ɵAnimationEngine, _zone: NgZone);
    createRenderer(hostElement: any, type: RendererType2): ɵBaseAnimationRenderer;
    begin(): void;
    private _scheduleCountTask;
    end(): void;
    whenRenderingDone(): Promise<any>;
}


export declare abstract class ɵAnimationStyleNormalizer {
    abstract normalizePropertyName(propertyName: string, errors: Error[]): string;
    abstract normalizeStyleValue(userProvidedProperty: string, normalizedProperty: string, value: string | number, errors: Error[]): string;
}

export declare class ɵBaseAnimationRenderer implements Renderer2 {
    protected namespaceId: string;
    delegate: Renderer2;
    engine: ɵAnimationEngine;
    private _onDestroy?;
    readonly ɵtype: ɵAnimationRendererType.Regular;
    constructor(namespaceId: string, delegate: Renderer2, engine: ɵAnimationEngine, _onDestroy?: (() => void) | undefined);
    get data(): {
        [key: string]: any;
    };
    destroyNode(node: any): void;
    destroy(): void;
    createElement(name: string, namespace?: string | null | undefined): any;
    createComment(value: string): any;
    createText(value: string): any;
    appendChild(parent: any, newChild: any): void;
    insertBefore(parent: any, newChild: any, refChild: any, isMove?: boolean): void;
    removeChild(parent: any, oldChild: any, isHostElement?: boolean): void;
    selectRootElement(selectorOrNode: any, preserveContent?: boolean): any;
    parentNode(node: any): any;
    nextSibling(node: any): any;
    setAttribute(el: any, name: string, value: string, namespace?: string | null | undefined): void;
    removeAttribute(el: any, name: string, namespace?: string | null | undefined): void;
    addClass(el: any, name: string): void;
    removeClass(el: any, name: string): void;
    setStyle(el: any, style: string, value: any, flags?: RendererStyleFlags2 | undefined): void;
    removeStyle(el: any, style: string, flags?: RendererStyleFlags2 | undefined): void;
    setProperty(el: any, name: string, value: any): void;
    setValue(node: any, value: string): void;
    listen(target: any, eventName: string, callback: (event: any) => boolean | void): () => void;
    protected disableAnimations(element: any, value: boolean): void;
}

export declare function ɵcamelCaseToDashCase(input: string): string;

export declare function ɵcontainsElement(elm1: any, elm2: any): boolean;

export declare function ɵcreateEngine(type: 'animations' | 'noop', doc: Document): ɵAnimationEngine;

export declare function ɵgetParentElement(element: any): unknown | null;

export declare function ɵinvokeQuery(element: any, selector: string, multi: boolean): any[];

export declare class ɵNoopAnimationStyleNormalizer {
    normalizePropertyName(propertyName: string, errors: Error[]): string;
    normalizeStyleValue(userProvidedProperty: string, normalizedProperty: string, value: string | number, errors: Error[]): string;
}

export declare function ɵnormalizeKeyframes(keyframes: Array<ɵStyleData> | Array<ɵStyleDataMap>): Array<ɵStyleDataMap>;

export declare function ɵvalidateStyleProperty(prop: string): boolean;

export declare function ɵvalidateWebAnimatableStyleProperty(prop: string): boolean;

export declare class ɵWebAnimationsDriver implements AnimationDriver {
    validateStyleProperty(prop: string): boolean;
    validateAnimatableStyleProperty(prop: string): boolean;
    containsElement(elm1: any, elm2: any): boolean;
    getParentElement(element: unknown): unknown;
    query(element: any, selector: string, multi: boolean): any[];
    computeStyle(element: any, prop: string, defaultValue?: string): string;
    animate(element: any, keyframes: Array<Map<string, string | number>>, duration: number, delay: number, easing: string, previousPlayers?: AnimationPlayer[]): AnimationPlayer;
}

export declare class ɵWebAnimationsPlayer implements AnimationPlayer {
    element: any;
    keyframes: Array<ɵStyleDataMap>;
    options: {
        [key: string]: string | number;
    };
    private _specialStyles?;
    private _onDoneFns;
    private _onStartFns;
    private _onDestroyFns;
    private _duration;
    private _delay;
    private _initialized;
    private _finished;
    private _started;
    private _destroyed;
    private _finalKeyframe?;
    private _originalOnDoneFns;
    private _originalOnStartFns;
    readonly domPlayer: Animation;
    time: number;
    parentPlayer: AnimationPlayer | null;
    currentSnapshot: ɵStyleDataMap;
    constructor(element: any, keyframes: Array<ɵStyleDataMap>, options: {
        [key: string]: string | number;
    }, _specialStyles?: (SpecialCasedStyles | null) | undefined);
    private _onFinish;
    init(): void;
    private _buildPlayer;
    private _preparePlayerBeforeStart;
    private _convertKeyframesToObject;
    onStart(fn: () => void): void;
    onDone(fn: () => void): void;
    onDestroy(fn: () => void): void;
    play(): void;
    pause(): void;
    finish(): void;
    reset(): void;
    private _resetDomPlayerState;
    restart(): void;
    hasStarted(): boolean;
    destroy(): void;
    setPosition(p: number): void;
    getPosition(): number;
    get totalTime(): number;
    beforeDestroy(): void;
}

export declare class ɵWebAnimationsStyleNormalizer extends ɵAnimationStyleNormalizer {
    normalizePropertyName(propertyName: string, errors: Error[]): string;
    normalizeStyleValue(userProvidedProperty: string, normalizedProperty: string, value: string | number, errors: Error[]): string;
}

export { }
