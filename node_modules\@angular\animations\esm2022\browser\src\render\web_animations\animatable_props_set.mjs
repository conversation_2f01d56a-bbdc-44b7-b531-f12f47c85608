/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
/**
 * Set of all animatable CSS properties
 *
 * @see https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_animated_properties
 */
export const ANIMATABLE_PROP_SET = new Set([
    '-moz-outline-radius',
    '-moz-outline-radius-bottomleft',
    '-moz-outline-radius-bottomright',
    '-moz-outline-radius-topleft',
    '-moz-outline-radius-topright',
    '-ms-grid-columns',
    '-ms-grid-rows',
    '-webkit-line-clamp',
    '-webkit-text-fill-color',
    '-webkit-text-stroke',
    '-webkit-text-stroke-color',
    'accent-color',
    'all',
    'backdrop-filter',
    'background',
    'background-color',
    'background-position',
    'background-size',
    'block-size',
    'border',
    'border-block-end',
    'border-block-end-color',
    'border-block-end-width',
    'border-block-start',
    'border-block-start-color',
    'border-block-start-width',
    'border-bottom',
    'border-bottom-color',
    'border-bottom-left-radius',
    'border-bottom-right-radius',
    'border-bottom-width',
    'border-color',
    'border-end-end-radius',
    'border-end-start-radius',
    'border-image-outset',
    'border-image-slice',
    'border-image-width',
    'border-inline-end',
    'border-inline-end-color',
    'border-inline-end-width',
    'border-inline-start',
    'border-inline-start-color',
    'border-inline-start-width',
    'border-left',
    'border-left-color',
    'border-left-width',
    'border-radius',
    'border-right',
    'border-right-color',
    'border-right-width',
    'border-start-end-radius',
    'border-start-start-radius',
    'border-top',
    'border-top-color',
    'border-top-left-radius',
    'border-top-right-radius',
    'border-top-width',
    'border-width',
    'bottom',
    'box-shadow',
    'caret-color',
    'clip',
    'clip-path',
    'color',
    'column-count',
    'column-gap',
    'column-rule',
    'column-rule-color',
    'column-rule-width',
    'column-width',
    'columns',
    'filter',
    'flex',
    'flex-basis',
    'flex-grow',
    'flex-shrink',
    'font',
    'font-size',
    'font-size-adjust',
    'font-stretch',
    'font-variation-settings',
    'font-weight',
    'gap',
    'grid-column-gap',
    'grid-gap',
    'grid-row-gap',
    'grid-template-columns',
    'grid-template-rows',
    'height',
    'inline-size',
    'input-security',
    'inset',
    'inset-block',
    'inset-block-end',
    'inset-block-start',
    'inset-inline',
    'inset-inline-end',
    'inset-inline-start',
    'left',
    'letter-spacing',
    'line-clamp',
    'line-height',
    'margin',
    'margin-block-end',
    'margin-block-start',
    'margin-bottom',
    'margin-inline-end',
    'margin-inline-start',
    'margin-left',
    'margin-right',
    'margin-top',
    'mask',
    'mask-border',
    'mask-position',
    'mask-size',
    'max-block-size',
    'max-height',
    'max-inline-size',
    'max-lines',
    'max-width',
    'min-block-size',
    'min-height',
    'min-inline-size',
    'min-width',
    'object-position',
    'offset',
    'offset-anchor',
    'offset-distance',
    'offset-path',
    'offset-position',
    'offset-rotate',
    'opacity',
    'order',
    'outline',
    'outline-color',
    'outline-offset',
    'outline-width',
    'padding',
    'padding-block-end',
    'padding-block-start',
    'padding-bottom',
    'padding-inline-end',
    'padding-inline-start',
    'padding-left',
    'padding-right',
    'padding-top',
    'perspective',
    'perspective-origin',
    'right',
    'rotate',
    'row-gap',
    'scale',
    'scroll-margin',
    'scroll-margin-block',
    'scroll-margin-block-end',
    'scroll-margin-block-start',
    'scroll-margin-bottom',
    'scroll-margin-inline',
    'scroll-margin-inline-end',
    'scroll-margin-inline-start',
    'scroll-margin-left',
    'scroll-margin-right',
    'scroll-margin-top',
    'scroll-padding',
    'scroll-padding-block',
    'scroll-padding-block-end',
    'scroll-padding-block-start',
    'scroll-padding-bottom',
    'scroll-padding-inline',
    'scroll-padding-inline-end',
    'scroll-padding-inline-start',
    'scroll-padding-left',
    'scroll-padding-right',
    'scroll-padding-top',
    'scroll-snap-coordinate',
    'scroll-snap-destination',
    'scrollbar-color',
    'shape-image-threshold',
    'shape-margin',
    'shape-outside',
    'tab-size',
    'text-decoration',
    'text-decoration-color',
    'text-decoration-thickness',
    'text-emphasis',
    'text-emphasis-color',
    'text-indent',
    'text-shadow',
    'text-underline-offset',
    'top',
    'transform',
    'transform-origin',
    'translate',
    'vertical-align',
    'visibility',
    'width',
    'word-spacing',
    'z-index',
    'zoom',
]);
//# sourceMappingURL=data:application/json;base64,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