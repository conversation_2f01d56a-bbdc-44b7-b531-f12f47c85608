{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nconst _forTrack0 = ($index, $item) => $item.id;\nfunction LoveNotesComponent_For_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 15)(2, \"div\", 16)(3, \"span\", 17);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\", 18);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 19);\n    i0.ɵɵtext(8, \"Hover to see why \\u2728\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 20)(10, \"div\", 21)(11, \"span\", 22);\n    i0.ɵɵtext(12, \"\\uD83D\\uDC96\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\", 23);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const loveNote_r1 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(loveNote_r1.emoji);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(loveNote_r1.title);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(loveNote_r1.message);\n  }\n}\nexport class LoveNotesComponent {\n  constructor() {\n    this.loveNotes = [{\n      id: 1,\n      emoji: '😍',\n      title: 'Your Smile',\n      message: 'Your smile lights up my entire world and makes even the darkest days feel bright and beautiful.'\n    }, {\n      id: 2,\n      emoji: '💖',\n      title: 'Your Kindness',\n      message: 'The way you care for others and show compassion makes me fall in love with you more every day.'\n    }, {\n      id: 3,\n      emoji: '🌟',\n      title: 'Your Intelligence',\n      message: 'Your brilliant mind and the way you see the world never fails to amaze and inspire me.'\n    }, {\n      id: 4,\n      emoji: '🎵',\n      title: 'Your Laugh',\n      message: 'Your laughter is my favorite melody, and I would do anything just to hear it every day.'\n    }, {\n      id: 5,\n      emoji: '🤗',\n      title: 'Your Hugs',\n      message: 'In your arms, I have found my home, my peace, and my greatest comfort in this world.'\n    }, {\n      id: 6,\n      emoji: '✨',\n      title: 'Your Dreams',\n      message: 'The passion you have for your dreams and goals motivates me to be the best version of myself.'\n    }];\n  }\n  static {\n    this.ɵfac = function LoveNotesComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || LoveNotesComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoveNotesComponent,\n      selectors: [[\"app-love-notes\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 22,\n      vars: 0,\n      consts: [[1, \"py-20\", \"px-6\", \"sm:px-8\", \"lg:px-12\", \"min-h-screen\"], [1, \"max-w-6xl\", \"mx-auto\"], [1, \"text-center\", \"mb-20\"], [1, \"font-playfair\", \"text-4xl\", \"sm:text-5xl\", \"lg:text-6xl\", \"text-romantic\", \"mb-6\"], [1, \"font-poppins\", \"text-xl\", \"text-gray-600\", \"max-w-2xl\", \"mx-auto\", \"font-light\"], [1, \"grid\", \"grid-cols-1\", \"sm:grid-cols-2\", \"lg:grid-cols-3\", \"gap-8\", \"lg:gap-10\"], [1, \"flip-card\", \"h-80\"], [1, \"mt-12\", \"sm:hidden\"], [1, \"text-center\", \"text-sm\", \"text-gray-500\", \"font-poppins\", \"font-light\"], [1, \"mt-20\", \"text-center\"], [1, \"glass-effect\", \"rounded-3xl\", \"p-12\", \"sm:p-16\", \"shadow-xl\", \"border\", \"border-white/30\", \"max-w-4xl\", \"mx-auto\"], [1, \"mb-8\"], [1, \"text-5xl\", \"animate-soft-pulse\"], [1, \"font-dancing\", \"text-3xl\", \"sm:text-4xl\", \"lg:text-5xl\", \"text-romantic\", \"mb-8\"], [1, \"font-poppins\", \"text-lg\", \"sm:text-xl\", \"text-gray-700\", \"leading-relaxed\", \"max-w-3xl\", \"mx-auto\", \"font-light\"], [1, \"flip-card-inner\"], [1, \"flip-card-front\", \"bg-gradient-to-br\", \"from-pink-100\", \"via-pink-150\", \"to-purple-150\", \"p-8\", \"shadow-lg\", \"flex\", \"flex-col\", \"items-center\", \"justify-center\", \"text-center\", \"border\", \"border-pink-200/30\"], [1, \"text-6xl\", \"mb-6\", \"animate-soft-pulse\"], [1, \"font-playfair\", \"text-2xl\", \"font-semibold\", \"text-gray-700\", \"mb-3\"], [1, \"text-sm\", \"text-gray-500\", \"font-poppins\", \"font-light\"], [1, \"flip-card-back\", \"bg-white/90\", \"backdrop-blur-sm\", \"p-8\", \"shadow-lg\", \"flex\", \"items-center\", \"justify-center\", \"border\", \"border-pink-200/30\"], [1, \"text-center\"], [1, \"text-4xl\", \"mb-6\", \"block\", \"animate-soft-pulse\"], [1, \"font-poppins\", \"text-gray-700\", \"leading-relaxed\", \"text-base\", \"font-light\"]],\n      template: function LoveNotesComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3);\n          i0.ɵɵtext(4, \" What I Love About You \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"p\", 4);\n          i0.ɵɵtext(6, \" Every little thing about you makes my heart skip a beat \\uD83D\\uDC95 \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(7, \"div\", 5);\n          i0.ɵɵrepeaterCreate(8, LoveNotesComponent_For_9_Template, 15, 3, \"div\", 6, _forTrack0);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"div\", 7)(11, \"p\", 8);\n          i0.ɵɵtext(12, \" \\uD83D\\uDCA1 Tap cards to see the messages on mobile \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"div\", 9)(14, \"div\", 10)(15, \"div\", 11)(16, \"span\", 12);\n          i0.ɵɵtext(17, \"\\uD83D\\uDC96\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(18, \"h2\", 13);\n          i0.ɵɵtext(19, \" Happy 1st Monthsary, my love! \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"p\", 14);\n          i0.ɵɵtext(21, \" Every day with you feels like a new adventure, and I can't wait to see what amazing memories we'll create together. Here's to many more months, years, and a lifetime of love, laughter, and beautiful moments. \");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(8);\n          i0.ɵɵrepeater(ctx.loveNotes);\n        }\n      },\n      dependencies: [CommonModule],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "loveNote_r1", "emoji", "title", "message", "LoveNotesComponent", "constructor", "loveNotes", "id", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "LoveNotesComponent_Template", "rf", "ctx", "ɵɵrepeaterCreate", "LoveNotesComponent_For_9_Template", "_forTrack0", "ɵɵrepeater", "encapsulation"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\1st-Monthsary-Tangi\\monthsary-website\\src\\app\\pages\\love-notes\\love-notes.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\n\n@Component({\n  selector: 'app-love-notes',\n  standalone: true,\n  imports: [CommonModule],\n  template: `\n    <section class=\"py-20 px-6 sm:px-8 lg:px-12 min-h-screen\">\n      <div class=\"max-w-6xl mx-auto\">\n        <div class=\"text-center mb-20\">\n          <h1 class=\"font-playfair text-4xl sm:text-5xl lg:text-6xl text-romantic mb-6\">\n            What I Love About You\n          </h1>\n          <p class=\"font-poppins text-xl text-gray-600 max-w-2xl mx-auto font-light\">\n            Every little thing about you makes my heart skip a beat 💕\n          </p>\n        </div>\n\n        <div class=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10\">\n          @for (loveNote of loveNotes; track loveNote.id) {\n            <div class=\"flip-card h-80\">\n              <div class=\"flip-card-inner\">\n                <!-- Front of card -->\n                <div class=\"flip-card-front bg-gradient-to-br from-pink-100 via-pink-150 to-purple-150 p-8 shadow-lg flex flex-col items-center justify-center text-center border border-pink-200/30\">\n                  <span class=\"text-6xl mb-6 animate-soft-pulse\">{{ loveNote.emoji }}</span>\n                  <h3 class=\"font-playfair text-2xl font-semibold text-gray-700 mb-3\">{{ loveNote.title }}</h3>\n                  <p class=\"text-sm text-gray-500 font-poppins font-light\">Hover to see why ✨</p>\n                </div>\n                \n                <!-- Back of card -->\n                <div class=\"flip-card-back bg-white/90 backdrop-blur-sm p-8 shadow-lg flex items-center justify-center border border-pink-200/30\">\n                  <div class=\"text-center\">\n                    <span class=\"text-4xl mb-6 block animate-soft-pulse\">💖</span>\n                    <p class=\"font-poppins text-gray-700 leading-relaxed text-base font-light\">{{ loveNote.message }}</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          }\n        </div>\n\n        <!-- Mobile-friendly alternative for touch devices -->\n        <div class=\"mt-12 sm:hidden\">\n          <p class=\"text-center text-sm text-gray-500 font-poppins font-light\">\n            💡 Tap cards to see the messages on mobile\n          </p>\n        </div>\n\n        <!-- Final Message -->\n        <div class=\"mt-20 text-center\">\n          <div class=\"glass-effect rounded-3xl p-12 sm:p-16 shadow-xl border border-white/30 max-w-4xl mx-auto\">\n            <div class=\"mb-8\">\n              <span class=\"text-5xl animate-soft-pulse\">💖</span>\n            </div>\n            \n            <h2 class=\"font-dancing text-3xl sm:text-4xl lg:text-5xl text-romantic mb-8\">\n              Happy 1st Monthsary, my love!\n            </h2>\n            \n            <p class=\"font-poppins text-lg sm:text-xl text-gray-700 leading-relaxed max-w-3xl mx-auto font-light\">\n              Every day with you feels like a new adventure, and I can't wait to see what \n              amazing memories we'll create together. Here's to many more months, years, \n              and a lifetime of love, laughter, and beautiful moments.\n            </p>\n          </div>\n        </div>\n      </div>\n    </section>\n  `,\n  styles: []\n})\nexport class LoveNotesComponent {\n  loveNotes = [\n    {\n      id: 1,\n      emoji: '😍',\n      title: 'Your Smile',\n      message: 'Your smile lights up my entire world and makes even the darkest days feel bright and beautiful.'\n    },\n    {\n      id: 2,\n      emoji: '💖',\n      title: 'Your Kindness',\n      message: 'The way you care for others and show compassion makes me fall in love with you more every day.'\n    },\n    {\n      id: 3,\n      emoji: '🌟',\n      title: 'Your Intelligence',\n      message: 'Your brilliant mind and the way you see the world never fails to amaze and inspire me.'\n    },\n    {\n      id: 4,\n      emoji: '🎵',\n      title: 'Your Laugh',\n      message: 'Your laughter is my favorite melody, and I would do anything just to hear it every day.'\n    },\n    {\n      id: 5,\n      emoji: '🤗',\n      title: 'Your Hugs',\n      message: 'In your arms, I have found my home, my peace, and my greatest comfort in this world.'\n    },\n    {\n      id: 6,\n      emoji: '✨',\n      title: 'Your Dreams',\n      message: 'The passion you have for your dreams and goals motivates me to be the best version of myself.'\n    }\n  ];\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;;;;;IAwB5BC,EAJN,CAAAC,cAAA,aAA4B,cACG,cAE2J,eACrI;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1EH,EAAA,CAAAC,cAAA,aAAoE;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7FH,EAAA,CAAAC,cAAA,YAAyD;IAAAD,EAAA,CAAAE,MAAA,8BAAkB;IAC7EF,EAD6E,CAAAG,YAAA,EAAI,EAC3E;IAKFH,EAFJ,CAAAC,cAAA,cAAkI,eACvG,gBAC8B;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9DH,EAAA,CAAAC,cAAA,aAA2E;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAIzGF,EAJyG,CAAAG,YAAA,EAAI,EACjG,EACF,EACF,EACF;;;;IAb+CH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAK,iBAAA,CAAAC,WAAA,CAAAC,KAAA,CAAoB;IACCP,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAK,iBAAA,CAAAC,WAAA,CAAAE,KAAA,CAAoB;IAQXR,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAC,WAAA,CAAAG,OAAA,CAAsB;;;AAsCrH,OAAM,MAAOC,kBAAkB;EArE/BC,YAAA;IAsEE,KAAAC,SAAS,GAAG,CACV;MACEC,EAAE,EAAE,CAAC;MACLN,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE,YAAY;MACnBC,OAAO,EAAE;KACV,EACD;MACEI,EAAE,EAAE,CAAC;MACLN,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE,eAAe;MACtBC,OAAO,EAAE;KACV,EACD;MACEI,EAAE,EAAE,CAAC;MACLN,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAE;KACV,EACD;MACEI,EAAE,EAAE,CAAC;MACLN,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE,YAAY;MACnBC,OAAO,EAAE;KACV,EACD;MACEI,EAAE,EAAE,CAAC;MACLN,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE,WAAW;MAClBC,OAAO,EAAE;KACV,EACD;MACEI,EAAE,EAAE,CAAC;MACLN,KAAK,EAAE,GAAG;MACVC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAE;KACV,CACF;;;;uCAtCUC,kBAAkB;IAAA;EAAA;;;YAAlBA,kBAAkB;MAAAI,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAhB,EAAA,CAAAiB,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA7DrBvB,EAHN,CAAAC,cAAA,iBAA0D,aACzB,aACE,YACiD;UAC5ED,EAAA,CAAAE,MAAA,8BACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,WAA2E;UACzED,EAAA,CAAAE,MAAA,6EACF;UACFF,EADE,CAAAG,YAAA,EAAI,EACA;UAENH,EAAA,CAAAC,cAAA,aAA4E;UAC1ED,EAAA,CAAAyB,gBAAA,IAAAC,iCAAA,mBAAAC,UAAA,CAmBC;UACH3B,EAAA,CAAAG,YAAA,EAAM;UAIJH,EADF,CAAAC,cAAA,cAA6B,YAC0C;UACnED,EAAA,CAAAE,MAAA,8DACF;UACFF,EADE,CAAAG,YAAA,EAAI,EACA;UAMAH,EAHN,CAAAC,cAAA,cAA+B,eACyE,eAClF,gBAC0B;UAAAD,EAAA,CAAAE,MAAA,oBAAE;UAC9CF,EAD8C,CAAAG,YAAA,EAAO,EAC/C;UAENH,EAAA,CAAAC,cAAA,cAA6E;UAC3ED,EAAA,CAAAE,MAAA,uCACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAELH,EAAA,CAAAC,cAAA,aAAsG;UACpGD,EAAA,CAAAE,MAAA,yNAGF;UAIRF,EAJQ,CAAAG,YAAA,EAAI,EACA,EACF,EACF,EACE;;;UAhDJH,EAAA,CAAAI,SAAA,GAmBC;UAnBDJ,EAAA,CAAA4B,UAAA,CAAAJ,GAAA,CAAAZ,SAAA,CAmBC;;;qBAjCCb,YAAY;MAAA8B,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}