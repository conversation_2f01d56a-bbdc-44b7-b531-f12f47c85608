{"ast": null, "code": "import { operate } from '../util/lift';\nimport { mergeInternals } from './mergeInternals';\nexport function expand(project, concurrent = Infinity, scheduler) {\n  concurrent = (concurrent || 0) < 1 ? Infinity : concurrent;\n  return operate((source, subscriber) => mergeInternals(source, subscriber, project, concurrent, undefined, true, scheduler));\n}", "map": {"version": 3, "names": ["operate", "mergeInternals", "expand", "project", "concurrent", "Infinity", "scheduler", "source", "subscriber", "undefined"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/monthsary-website/node_modules/rxjs/dist/esm/internal/operators/expand.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { mergeInternals } from './mergeInternals';\nexport function expand(project, concurrent = Infinity, scheduler) {\n    concurrent = (concurrent || 0) < 1 ? Infinity : concurrent;\n    return operate((source, subscriber) => mergeInternals(source, subscriber, project, concurrent, undefined, true, scheduler));\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,cAAc,QAAQ,kBAAkB;AACjD,OAAO,SAASC,MAAMA,CAACC,OAAO,EAAEC,UAAU,GAAGC,QAAQ,EAAEC,SAAS,EAAE;EAC9DF,UAAU,GAAG,CAACA,UAAU,IAAI,CAAC,IAAI,CAAC,GAAGC,QAAQ,GAAGD,UAAU;EAC1D,OAAOJ,OAAO,CAAC,CAACO,MAAM,EAAEC,UAAU,KAAKP,cAAc,CAACM,MAAM,EAAEC,UAAU,EAAEL,OAAO,EAAEC,UAAU,EAAEK,SAAS,EAAE,IAAI,EAAEH,SAAS,CAAC,CAAC;AAC/H", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}