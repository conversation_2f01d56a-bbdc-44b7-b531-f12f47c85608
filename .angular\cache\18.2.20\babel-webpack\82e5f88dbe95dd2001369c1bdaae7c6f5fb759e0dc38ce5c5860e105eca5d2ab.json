{"ast": null, "code": "/**\n * @license Angular v18.2.13\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵDomAdapter, ɵsetRootDomAdapter, ɵparseCookieValue, ɵgetDOM, isPlatformServer, DOCUMENT, ɵPLATFORM_BROWSER_ID, XhrFactory, CommonModule } from '@angular/common';\nexport { ɵgetDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { ɵglobal, ɵRuntimeError, Injectable, InjectionToken, Inject, APP_ID, CSP_NONCE, PLATFORM_ID, Optional, ViewEncapsulation, RendererStyleFlags2, ɵinternalCreateApplication, ErrorHandler, ɵsetDocument, PLATFORM_INITIALIZER, createPlatformFactory, platformCore, ɵTESTABILITY_GETTER, ɵTESTABILITY, Testability, NgZone, TestabilityRegistry, ɵINJECTOR_SCOPE, RendererFactory2, ApplicationModule, NgModule, SkipSelf, ApplicationRef, ɵConsole, forwardRef, ɵXSS_SECURITY_URL, SecurityContext, ɵallowSanitizationBypassAndThrow, ɵunwrapSafeValue, ɵ_sanitizeUrl, ɵ_sanitizeHtml, ɵbypassSanitizationTrustHtml, ɵbypassSanitizationTrustStyle, ɵbypassSanitizationTrustScript, ɵbypassSanitizationTrustUrl, ɵbypassSanitizationTrustResourceUrl, ɵwithI18nSupport, ɵwithEventReplay, ENVIRONMENT_INITIALIZER, inject, ɵZONELESS_ENABLED, ɵformatRuntimeError, makeEnvironmentProviders, ɵwithDomHydration, Version } from '@angular/core';\nimport { ɵwithHttpTransferCache } from '@angular/common/http';\n\n/**\n * Provides DOM operations in any browser environment.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nclass GenericBrowserDomAdapter extends ɵDomAdapter {\n  constructor() {\n    super(...arguments);\n    this.supportsDOMEvents = true;\n  }\n}\n\n/**\n * A `DomAdapter` powered by full browser DOM APIs.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\n/* tslint:disable:requireParameterType no-console */\nclass BrowserDomAdapter extends GenericBrowserDomAdapter {\n  static makeCurrent() {\n    ɵsetRootDomAdapter(new BrowserDomAdapter());\n  }\n  onAndCancel(el, evt, listener) {\n    el.addEventListener(evt, listener);\n    return () => {\n      el.removeEventListener(evt, listener);\n    };\n  }\n  dispatchEvent(el, evt) {\n    el.dispatchEvent(evt);\n  }\n  remove(node) {\n    node.remove();\n  }\n  createElement(tagName, doc) {\n    doc = doc || this.getDefaultDocument();\n    return doc.createElement(tagName);\n  }\n  createHtmlDocument() {\n    return document.implementation.createHTMLDocument('fakeTitle');\n  }\n  getDefaultDocument() {\n    return document;\n  }\n  isElementNode(node) {\n    return node.nodeType === Node.ELEMENT_NODE;\n  }\n  isShadowRoot(node) {\n    return node instanceof DocumentFragment;\n  }\n  /** @deprecated No longer being used in Ivy code. To be removed in version 14. */\n  getGlobalEventTarget(doc, target) {\n    if (target === 'window') {\n      return window;\n    }\n    if (target === 'document') {\n      return doc;\n    }\n    if (target === 'body') {\n      return doc.body;\n    }\n    return null;\n  }\n  getBaseHref(doc) {\n    const href = getBaseElementHref();\n    return href == null ? null : relativePath(href);\n  }\n  resetBaseElement() {\n    baseElement = null;\n  }\n  getUserAgent() {\n    return window.navigator.userAgent;\n  }\n  getCookie(name) {\n    return ɵparseCookieValue(document.cookie, name);\n  }\n}\nlet baseElement = null;\nfunction getBaseElementHref() {\n  baseElement = baseElement || document.querySelector('base');\n  return baseElement ? baseElement.getAttribute('href') : null;\n}\nfunction relativePath(url) {\n  // The base URL doesn't really matter, we just need it so relative paths have something\n  // to resolve against. In the browser `HTMLBaseElement.href` is always absolute.\n  return new URL(url, document.baseURI).pathname;\n}\nclass BrowserGetTestability {\n  addToWindow(registry) {\n    ɵglobal['getAngularTestability'] = (elem, findInAncestors = true) => {\n      const testability = registry.findTestabilityInTree(elem, findInAncestors);\n      if (testability == null) {\n        throw new ɵRuntimeError(5103 /* RuntimeErrorCode.TESTABILITY_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) && 'Could not find testability for element.');\n      }\n      return testability;\n    };\n    ɵglobal['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n    ɵglobal['getAllAngularRootElements'] = () => registry.getAllRootElements();\n    const whenAllStable = callback => {\n      const testabilities = ɵglobal['getAllAngularTestabilities']();\n      let count = testabilities.length;\n      const decrement = function () {\n        count--;\n        if (count == 0) {\n          callback();\n        }\n      };\n      testabilities.forEach(testability => {\n        testability.whenStable(decrement);\n      });\n    };\n    if (!ɵglobal['frameworkStabilizers']) {\n      ɵglobal['frameworkStabilizers'] = [];\n    }\n    ɵglobal['frameworkStabilizers'].push(whenAllStable);\n  }\n  findTestabilityInTree(registry, elem, findInAncestors) {\n    if (elem == null) {\n      return null;\n    }\n    const t = registry.getTestability(elem);\n    if (t != null) {\n      return t;\n    } else if (!findInAncestors) {\n      return null;\n    }\n    if (ɵgetDOM().isShadowRoot(elem)) {\n      return this.findTestabilityInTree(registry, elem.host, true);\n    }\n    return this.findTestabilityInTree(registry, elem.parentElement, true);\n  }\n}\n\n/**\n * A factory for `HttpXhrBackend` that uses the `XMLHttpRequest` browser API.\n */\nclass BrowserXhr {\n  build() {\n    return new XMLHttpRequest();\n  }\n  static {\n    this.ɵfac = function BrowserXhr_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BrowserXhr)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: BrowserXhr,\n      factory: BrowserXhr.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserXhr, [{\n    type: Injectable\n  }], null, null);\n})();\n\n/**\n * The injection token for plugins of the `EventManager` service.\n *\n * @publicApi\n */\nconst EVENT_MANAGER_PLUGINS = new InjectionToken(ngDevMode ? 'EventManagerPlugins' : '');\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\nclass EventManager {\n  /**\n   * Initializes an instance of the event-manager service.\n   */\n  constructor(plugins, _zone) {\n    this._zone = _zone;\n    this._eventNameToPlugin = new Map();\n    plugins.forEach(plugin => {\n      plugin.manager = this;\n    });\n    this._plugins = plugins.slice().reverse();\n  }\n  /**\n   * Registers a handler for a specific element and event.\n   *\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns  A callback function that can be used to remove the handler.\n   */\n  addEventListener(element, eventName, handler) {\n    const plugin = this._findPluginFor(eventName);\n    return plugin.addEventListener(element, eventName, handler);\n  }\n  /**\n   * Retrieves the compilation zone in which event listeners are registered.\n   */\n  getZone() {\n    return this._zone;\n  }\n  /** @internal */\n  _findPluginFor(eventName) {\n    let plugin = this._eventNameToPlugin.get(eventName);\n    if (plugin) {\n      return plugin;\n    }\n    const plugins = this._plugins;\n    plugin = plugins.find(plugin => plugin.supports(eventName));\n    if (!plugin) {\n      throw new ɵRuntimeError(5101 /* RuntimeErrorCode.NO_PLUGIN_FOR_EVENT */, (typeof ngDevMode === 'undefined' || ngDevMode) && `No event manager plugin found for event ${eventName}`);\n    }\n    this._eventNameToPlugin.set(eventName, plugin);\n    return plugin;\n  }\n  static {\n    this.ɵfac = function EventManager_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EventManager)(i0.ɵɵinject(EVENT_MANAGER_PLUGINS), i0.ɵɵinject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: EventManager,\n      factory: EventManager.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(EventManager, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [EVENT_MANAGER_PLUGINS]\n    }]\n  }, {\n    type: i0.NgZone\n  }], null);\n})();\n/**\n * The plugin definition for the `EventManager` class\n *\n * It can be used as a base class to create custom manager plugins, i.e. you can create your own\n * class that extends the `EventManagerPlugin` one.\n *\n * @publicApi\n */\nclass EventManagerPlugin {\n  // TODO: remove (has some usage in G3)\n  constructor(_doc) {\n    this._doc = _doc;\n  }\n}\n\n/** The style elements attribute name used to set value of `APP_ID` token. */\nconst APP_ID_ATTRIBUTE_NAME = 'ng-app-id';\nclass SharedStylesHost {\n  constructor(doc, appId, nonce, platformId = {}) {\n    this.doc = doc;\n    this.appId = appId;\n    this.nonce = nonce;\n    this.platformId = platformId;\n    // Maps all registered host nodes to a list of style nodes that have been added to the host node.\n    this.styleRef = new Map();\n    this.hostNodes = new Set();\n    this.styleNodesInDOM = this.collectServerRenderedStyles();\n    this.platformIsServer = isPlatformServer(platformId);\n    this.resetHostNodes();\n  }\n  addStyles(styles) {\n    for (const style of styles) {\n      const usageCount = this.changeUsageCount(style, 1);\n      if (usageCount === 1) {\n        this.onStyleAdded(style);\n      }\n    }\n  }\n  removeStyles(styles) {\n    for (const style of styles) {\n      const usageCount = this.changeUsageCount(style, -1);\n      if (usageCount <= 0) {\n        this.onStyleRemoved(style);\n      }\n    }\n  }\n  ngOnDestroy() {\n    const styleNodesInDOM = this.styleNodesInDOM;\n    if (styleNodesInDOM) {\n      styleNodesInDOM.forEach(node => node.remove());\n      styleNodesInDOM.clear();\n    }\n    for (const style of this.getAllStyles()) {\n      this.onStyleRemoved(style);\n    }\n    this.resetHostNodes();\n  }\n  addHost(hostNode) {\n    this.hostNodes.add(hostNode);\n    for (const style of this.getAllStyles()) {\n      this.addStyleToHost(hostNode, style);\n    }\n  }\n  removeHost(hostNode) {\n    this.hostNodes.delete(hostNode);\n  }\n  getAllStyles() {\n    return this.styleRef.keys();\n  }\n  onStyleAdded(style) {\n    for (const host of this.hostNodes) {\n      this.addStyleToHost(host, style);\n    }\n  }\n  onStyleRemoved(style) {\n    const styleRef = this.styleRef;\n    styleRef.get(style)?.elements?.forEach(node => node.remove());\n    styleRef.delete(style);\n  }\n  collectServerRenderedStyles() {\n    const styles = this.doc.head?.querySelectorAll(`style[${APP_ID_ATTRIBUTE_NAME}=\"${this.appId}\"]`);\n    if (styles?.length) {\n      const styleMap = new Map();\n      styles.forEach(style => {\n        if (style.textContent != null) {\n          styleMap.set(style.textContent, style);\n        }\n      });\n      return styleMap;\n    }\n    return null;\n  }\n  changeUsageCount(style, delta) {\n    const map = this.styleRef;\n    if (map.has(style)) {\n      const styleRefValue = map.get(style);\n      styleRefValue.usage += delta;\n      return styleRefValue.usage;\n    }\n    map.set(style, {\n      usage: delta,\n      elements: []\n    });\n    return delta;\n  }\n  getStyleElement(host, style) {\n    const styleNodesInDOM = this.styleNodesInDOM;\n    const styleEl = styleNodesInDOM?.get(style);\n    if (styleEl?.parentNode === host) {\n      // `styleNodesInDOM` cannot be undefined due to the above `styleNodesInDOM?.get`.\n      styleNodesInDOM.delete(style);\n      styleEl.removeAttribute(APP_ID_ATTRIBUTE_NAME);\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        // This attribute is solely used for debugging purposes.\n        styleEl.setAttribute('ng-style-reused', '');\n      }\n      return styleEl;\n    } else {\n      const styleEl = this.doc.createElement('style');\n      if (this.nonce) {\n        styleEl.setAttribute('nonce', this.nonce);\n      }\n      styleEl.textContent = style;\n      if (this.platformIsServer) {\n        styleEl.setAttribute(APP_ID_ATTRIBUTE_NAME, this.appId);\n      }\n      host.appendChild(styleEl);\n      return styleEl;\n    }\n  }\n  addStyleToHost(host, style) {\n    const styleEl = this.getStyleElement(host, style);\n    const styleRef = this.styleRef;\n    const styleElRef = styleRef.get(style)?.elements;\n    if (styleElRef) {\n      styleElRef.push(styleEl);\n    } else {\n      styleRef.set(style, {\n        elements: [styleEl],\n        usage: 1\n      });\n    }\n  }\n  resetHostNodes() {\n    const hostNodes = this.hostNodes;\n    hostNodes.clear();\n    // Re-add the head element back since this is the default host.\n    hostNodes.add(this.doc.head);\n  }\n  static {\n    this.ɵfac = function SharedStylesHost_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || SharedStylesHost)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(APP_ID), i0.ɵɵinject(CSP_NONCE, 8), i0.ɵɵinject(PLATFORM_ID));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: SharedStylesHost,\n      factory: SharedStylesHost.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SharedStylesHost, [{\n    type: Injectable\n  }], () => [{\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [APP_ID]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CSP_NONCE]\n    }, {\n      type: Optional\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }], null);\n})();\nconst NAMESPACE_URIS = {\n  'svg': 'http://www.w3.org/2000/svg',\n  'xhtml': 'http://www.w3.org/1999/xhtml',\n  'xlink': 'http://www.w3.org/1999/xlink',\n  'xml': 'http://www.w3.org/XML/1998/namespace',\n  'xmlns': 'http://www.w3.org/2000/xmlns/',\n  'math': 'http://www.w3.org/1998/Math/MathML'\n};\nconst COMPONENT_REGEX = /%COMP%/g;\nconst COMPONENT_VARIABLE = '%COMP%';\nconst HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nconst CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n/**\n * The default value for the `REMOVE_STYLES_ON_COMPONENT_DESTROY` DI token.\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT = true;\n/**\n * A DI token that indicates whether styles\n * of destroyed components should be removed from DOM.\n *\n * By default, the value is set to `true`.\n * @publicApi\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY = new InjectionToken(ngDevMode ? 'RemoveStylesOnCompDestroy' : '', {\n  providedIn: 'root',\n  factory: () => REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT\n});\nfunction shimContentAttribute(componentShortId) {\n  return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimHostAttribute(componentShortId) {\n  return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimStylesContent(compId, styles) {\n  return styles.map(s => s.replace(COMPONENT_REGEX, compId));\n}\nclass DomRendererFactory2 {\n  constructor(eventManager, sharedStylesHost, appId, removeStylesOnCompDestroy, doc, platformId, ngZone, nonce = null) {\n    this.eventManager = eventManager;\n    this.sharedStylesHost = sharedStylesHost;\n    this.appId = appId;\n    this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n    this.doc = doc;\n    this.platformId = platformId;\n    this.ngZone = ngZone;\n    this.nonce = nonce;\n    this.rendererByCompId = new Map();\n    this.platformIsServer = isPlatformServer(platformId);\n    this.defaultRenderer = new DefaultDomRenderer2(eventManager, doc, ngZone, this.platformIsServer);\n  }\n  createRenderer(element, type) {\n    if (!element || !type) {\n      return this.defaultRenderer;\n    }\n    if (this.platformIsServer && type.encapsulation === ViewEncapsulation.ShadowDom) {\n      // Domino does not support shadow DOM.\n      type = {\n        ...type,\n        encapsulation: ViewEncapsulation.Emulated\n      };\n    }\n    const renderer = this.getOrCreateRenderer(element, type);\n    // Renderers have different logic due to different encapsulation behaviours.\n    // Ex: for emulated, an attribute is added to the element.\n    if (renderer instanceof EmulatedEncapsulationDomRenderer2) {\n      renderer.applyToHost(element);\n    } else if (renderer instanceof NoneEncapsulationDomRenderer) {\n      renderer.applyStyles();\n    }\n    return renderer;\n  }\n  getOrCreateRenderer(element, type) {\n    const rendererByCompId = this.rendererByCompId;\n    let renderer = rendererByCompId.get(type.id);\n    if (!renderer) {\n      const doc = this.doc;\n      const ngZone = this.ngZone;\n      const eventManager = this.eventManager;\n      const sharedStylesHost = this.sharedStylesHost;\n      const removeStylesOnCompDestroy = this.removeStylesOnCompDestroy;\n      const platformIsServer = this.platformIsServer;\n      switch (type.encapsulation) {\n        case ViewEncapsulation.Emulated:\n          renderer = new EmulatedEncapsulationDomRenderer2(eventManager, sharedStylesHost, type, this.appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer);\n          break;\n        case ViewEncapsulation.ShadowDom:\n          return new ShadowDomRenderer(eventManager, sharedStylesHost, element, type, doc, ngZone, this.nonce, platformIsServer);\n        default:\n          renderer = new NoneEncapsulationDomRenderer(eventManager, sharedStylesHost, type, removeStylesOnCompDestroy, doc, ngZone, platformIsServer);\n          break;\n      }\n      rendererByCompId.set(type.id, renderer);\n    }\n    return renderer;\n  }\n  ngOnDestroy() {\n    this.rendererByCompId.clear();\n  }\n  static {\n    this.ɵfac = function DomRendererFactory2_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DomRendererFactory2)(i0.ɵɵinject(EventManager), i0.ɵɵinject(SharedStylesHost), i0.ɵɵinject(APP_ID), i0.ɵɵinject(REMOVE_STYLES_ON_COMPONENT_DESTROY), i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(CSP_NONCE));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DomRendererFactory2,\n      factory: DomRendererFactory2.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomRendererFactory2, [{\n    type: Injectable\n  }], () => [{\n    type: EventManager\n  }, {\n    type: SharedStylesHost\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [APP_ID]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [REMOVE_STYLES_ON_COMPONENT_DESTROY]\n    }]\n  }, {\n    type: Document,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: Object,\n    decorators: [{\n      type: Inject,\n      args: [PLATFORM_ID]\n    }]\n  }, {\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [CSP_NONCE]\n    }]\n  }], null);\n})();\nclass DefaultDomRenderer2 {\n  constructor(eventManager, doc, ngZone, platformIsServer) {\n    this.eventManager = eventManager;\n    this.doc = doc;\n    this.ngZone = ngZone;\n    this.platformIsServer = platformIsServer;\n    this.data = Object.create(null);\n    /**\n     * By default this renderer throws when encountering synthetic properties\n     * This can be disabled for example by the AsyncAnimationRendererFactory\n     */\n    this.throwOnSyntheticProps = true;\n    this.destroyNode = null;\n  }\n  destroy() {}\n  createElement(name, namespace) {\n    if (namespace) {\n      // TODO: `|| namespace` was added in\n      // https://github.com/angular/angular/commit/2b9cc8503d48173492c29f5a271b61126104fbdb to\n      // support how Ivy passed around the namespace URI rather than short name at the time. It did\n      // not, however extend the support to other parts of the system (setAttribute, setAttribute,\n      // and the ServerRenderer). We should decide what exactly the semantics for dealing with\n      // namespaces should be and make it consistent.\n      // Related issues:\n      // https://github.com/angular/angular/issues/44028\n      // https://github.com/angular/angular/issues/44883\n      return this.doc.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n    }\n    return this.doc.createElement(name);\n  }\n  createComment(value) {\n    return this.doc.createComment(value);\n  }\n  createText(value) {\n    return this.doc.createTextNode(value);\n  }\n  appendChild(parent, newChild) {\n    const targetParent = isTemplateNode(parent) ? parent.content : parent;\n    targetParent.appendChild(newChild);\n  }\n  insertBefore(parent, newChild, refChild) {\n    if (parent) {\n      const targetParent = isTemplateNode(parent) ? parent.content : parent;\n      targetParent.insertBefore(newChild, refChild);\n    }\n  }\n  removeChild(_parent, oldChild) {\n    oldChild.remove();\n  }\n  selectRootElement(selectorOrNode, preserveContent) {\n    let el = typeof selectorOrNode === 'string' ? this.doc.querySelector(selectorOrNode) : selectorOrNode;\n    if (!el) {\n      throw new ɵRuntimeError(-5104 /* RuntimeErrorCode.ROOT_NODE_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) && `The selector \"${selectorOrNode}\" did not match any elements`);\n    }\n    if (!preserveContent) {\n      el.textContent = '';\n    }\n    return el;\n  }\n  parentNode(node) {\n    return node.parentNode;\n  }\n  nextSibling(node) {\n    return node.nextSibling;\n  }\n  setAttribute(el, name, value, namespace) {\n    if (namespace) {\n      name = namespace + ':' + name;\n      const namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.setAttributeNS(namespaceUri, name, value);\n      } else {\n        el.setAttribute(name, value);\n      }\n    } else {\n      el.setAttribute(name, value);\n    }\n  }\n  removeAttribute(el, name, namespace) {\n    if (namespace) {\n      const namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.removeAttributeNS(namespaceUri, name);\n      } else {\n        el.removeAttribute(`${namespace}:${name}`);\n      }\n    } else {\n      el.removeAttribute(name);\n    }\n  }\n  addClass(el, name) {\n    el.classList.add(name);\n  }\n  removeClass(el, name) {\n    el.classList.remove(name);\n  }\n  setStyle(el, style, value, flags) {\n    if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n      el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n    } else {\n      el.style[style] = value;\n    }\n  }\n  removeStyle(el, style, flags) {\n    if (flags & RendererStyleFlags2.DashCase) {\n      // removeProperty has no effect when used on camelCased properties.\n      el.style.removeProperty(style);\n    } else {\n      el.style[style] = '';\n    }\n  }\n  setProperty(el, name, value) {\n    if (el == null) {\n      return;\n    }\n    (typeof ngDevMode === 'undefined' || ngDevMode) && this.throwOnSyntheticProps && checkNoSyntheticProp(name, 'property');\n    el[name] = value;\n  }\n  setValue(node, value) {\n    node.nodeValue = value;\n  }\n  listen(target, event, callback) {\n    (typeof ngDevMode === 'undefined' || ngDevMode) && this.throwOnSyntheticProps && checkNoSyntheticProp(event, 'listener');\n    if (typeof target === 'string') {\n      target = ɵgetDOM().getGlobalEventTarget(this.doc, target);\n      if (!target) {\n        throw new Error(`Unsupported event target ${target} for event ${event}`);\n      }\n    }\n    return this.eventManager.addEventListener(target, event, this.decoratePreventDefault(callback));\n  }\n  decoratePreventDefault(eventHandler) {\n    // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n    // decoratePreventDefault or is a listener added outside the Angular context so it can handle\n    // the two differently. In the first case, the special '__ngUnwrap__' token is passed to the\n    // unwrap the listener (see below).\n    return event => {\n      // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n      // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The\n      // debug_node can inspect the listener toString contents for the existence of this special\n      // token. Because the token is a string literal, it is ensured to not be modified by compiled\n      // code.\n      if (event === '__ngUnwrap__') {\n        return eventHandler;\n      }\n      // Run the event handler inside the ngZone because event handlers are not patched\n      // by Zone on the server. This is required only for tests.\n      const allowDefaultBehavior = this.platformIsServer ? this.ngZone.runGuarded(() => eventHandler(event)) : eventHandler(event);\n      if (allowDefaultBehavior === false) {\n        event.preventDefault();\n      }\n      return undefined;\n    };\n  }\n}\nconst AT_CHARCODE = (() => '@'.charCodeAt(0))();\nfunction checkNoSyntheticProp(name, nameKind) {\n  if (name.charCodeAt(0) === AT_CHARCODE) {\n    throw new ɵRuntimeError(5105 /* RuntimeErrorCode.UNEXPECTED_SYNTHETIC_PROPERTY */, `Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Either \\`BrowserAnimationsModule\\` or \\`NoopAnimationsModule\\` are imported in your application.\n  - There is corresponding configuration for the animation named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.io/api/core/Component#animations).`);\n  }\n}\nfunction isTemplateNode(node) {\n  return node.tagName === 'TEMPLATE' && node.content !== undefined;\n}\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n  constructor(eventManager, sharedStylesHost, hostEl, component, doc, ngZone, nonce, platformIsServer) {\n    super(eventManager, doc, ngZone, platformIsServer);\n    this.sharedStylesHost = sharedStylesHost;\n    this.hostEl = hostEl;\n    this.shadowRoot = hostEl.attachShadow({\n      mode: 'open'\n    });\n    this.sharedStylesHost.addHost(this.shadowRoot);\n    const styles = shimStylesContent(component.id, component.styles);\n    for (const style of styles) {\n      const styleEl = document.createElement('style');\n      if (nonce) {\n        styleEl.setAttribute('nonce', nonce);\n      }\n      styleEl.textContent = style;\n      this.shadowRoot.appendChild(styleEl);\n    }\n  }\n  nodeOrShadowRoot(node) {\n    return node === this.hostEl ? this.shadowRoot : node;\n  }\n  appendChild(parent, newChild) {\n    return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n  }\n  insertBefore(parent, newChild, refChild) {\n    return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n  }\n  removeChild(_parent, oldChild) {\n    return super.removeChild(null, oldChild);\n  }\n  parentNode(node) {\n    return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n  }\n  destroy() {\n    this.sharedStylesHost.removeHost(this.shadowRoot);\n  }\n}\nclass NoneEncapsulationDomRenderer extends DefaultDomRenderer2 {\n  constructor(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, compId) {\n    super(eventManager, doc, ngZone, platformIsServer);\n    this.sharedStylesHost = sharedStylesHost;\n    this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n    this.styles = compId ? shimStylesContent(compId, component.styles) : component.styles;\n  }\n  applyStyles() {\n    this.sharedStylesHost.addStyles(this.styles);\n  }\n  destroy() {\n    if (!this.removeStylesOnCompDestroy) {\n      return;\n    }\n    this.sharedStylesHost.removeStyles(this.styles);\n  }\n}\nclass EmulatedEncapsulationDomRenderer2 extends NoneEncapsulationDomRenderer {\n  constructor(eventManager, sharedStylesHost, component, appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer) {\n    const compId = appId + '-' + component.id;\n    super(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, compId);\n    this.contentAttr = shimContentAttribute(compId);\n    this.hostAttr = shimHostAttribute(compId);\n  }\n  applyToHost(element) {\n    this.applyStyles();\n    this.setAttribute(element, this.hostAttr, '');\n  }\n  createElement(parent, name) {\n    const el = super.createElement(parent, name);\n    super.setAttribute(el, this.contentAttr, '');\n    return el;\n  }\n}\nclass DomEventsPlugin extends EventManagerPlugin {\n  constructor(doc) {\n    super(doc);\n  }\n  // This plugin should come last in the list of plugins, because it accepts all\n  // events.\n  supports(eventName) {\n    return true;\n  }\n  addEventListener(element, eventName, handler) {\n    element.addEventListener(eventName, handler, false);\n    return () => this.removeEventListener(element, eventName, handler);\n  }\n  removeEventListener(target, eventName, callback) {\n    return target.removeEventListener(eventName, callback);\n  }\n  static {\n    this.ɵfac = function DomEventsPlugin_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DomEventsPlugin)(i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DomEventsPlugin,\n      factory: DomEventsPlugin.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomEventsPlugin, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * Defines supported modifiers for key events.\n */\nconst MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift'];\n// The following values are here for cross-browser compatibility and to match the W3C standard\n// cf https://www.w3.org/TR/DOM-Level-3-Events-key/\nconst _keyMap = {\n  '\\b': 'Backspace',\n  '\\t': 'Tab',\n  '\\x7F': 'Delete',\n  '\\x1B': 'Escape',\n  'Del': 'Delete',\n  'Esc': 'Escape',\n  'Left': 'ArrowLeft',\n  'Right': 'ArrowRight',\n  'Up': 'ArrowUp',\n  'Down': 'ArrowDown',\n  'Menu': 'ContextMenu',\n  'Scroll': 'ScrollLock',\n  'Win': 'OS'\n};\n/**\n * Retrieves modifiers from key-event objects.\n */\nconst MODIFIER_KEY_GETTERS = {\n  'alt': event => event.altKey,\n  'control': event => event.ctrlKey,\n  'meta': event => event.metaKey,\n  'shift': event => event.shiftKey\n};\n/**\n * A browser plug-in that provides support for handling of key events in Angular.\n */\nclass KeyEventsPlugin extends EventManagerPlugin {\n  /**\n   * Initializes an instance of the browser plug-in.\n   * @param doc The document in which key events will be detected.\n   */\n  constructor(doc) {\n    super(doc);\n  }\n  /**\n   * Reports whether a named key event is supported.\n   * @param eventName The event name to query.\n   * @return True if the named key event is supported.\n   */\n  supports(eventName) {\n    return KeyEventsPlugin.parseEventName(eventName) != null;\n  }\n  /**\n   * Registers a handler for a specific element and key event.\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the key event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns The key event that was registered.\n   */\n  addEventListener(element, eventName, handler) {\n    const parsedEvent = KeyEventsPlugin.parseEventName(eventName);\n    const outsideHandler = KeyEventsPlugin.eventCallback(parsedEvent['fullKey'], handler, this.manager.getZone());\n    return this.manager.getZone().runOutsideAngular(() => {\n      return ɵgetDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler);\n    });\n  }\n  /**\n   * Parses the user provided full keyboard event definition and normalizes it for\n   * later internal use. It ensures the string is all lowercase, converts special\n   * characters to a standard spelling, and orders all the values consistently.\n   *\n   * @param eventName The name of the key event to listen for.\n   * @returns an object with the full, normalized string, and the dom event name\n   * or null in the case when the event doesn't match a keyboard event.\n   */\n  static parseEventName(eventName) {\n    const parts = eventName.toLowerCase().split('.');\n    const domEventName = parts.shift();\n    if (parts.length === 0 || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n      return null;\n    }\n    const key = KeyEventsPlugin._normalizeKey(parts.pop());\n    let fullKey = '';\n    let codeIX = parts.indexOf('code');\n    if (codeIX > -1) {\n      parts.splice(codeIX, 1);\n      fullKey = 'code.';\n    }\n    MODIFIER_KEYS.forEach(modifierName => {\n      const index = parts.indexOf(modifierName);\n      if (index > -1) {\n        parts.splice(index, 1);\n        fullKey += modifierName + '.';\n      }\n    });\n    fullKey += key;\n    if (parts.length != 0 || key.length === 0) {\n      // returning null instead of throwing to let another plugin process the event\n      return null;\n    }\n    // NOTE: Please don't rewrite this as so, as it will break JSCompiler property renaming.\n    //       The code must remain in the `result['domEventName']` form.\n    // return {domEventName, fullKey};\n    const result = {};\n    result['domEventName'] = domEventName;\n    result['fullKey'] = fullKey;\n    return result;\n  }\n  /**\n   * Determines whether the actual keys pressed match the configured key code string.\n   * The `fullKeyCode` event is normalized in the `parseEventName` method when the\n   * event is attached to the DOM during the `addEventListener` call. This is unseen\n   * by the end user and is normalized for internal consistency and parsing.\n   *\n   * @param event The keyboard event.\n   * @param fullKeyCode The normalized user defined expected key event string\n   * @returns boolean.\n   */\n  static matchEventFullKeyCode(event, fullKeyCode) {\n    let keycode = _keyMap[event.key] || event.key;\n    let key = '';\n    if (fullKeyCode.indexOf('code.') > -1) {\n      keycode = event.code;\n      key = 'code.';\n    }\n    // the keycode could be unidentified so we have to check here\n    if (keycode == null || !keycode) return false;\n    keycode = keycode.toLowerCase();\n    if (keycode === ' ') {\n      keycode = 'space'; // for readability\n    } else if (keycode === '.') {\n      keycode = 'dot'; // because '.' is used as a separator in event names\n    }\n    MODIFIER_KEYS.forEach(modifierName => {\n      if (modifierName !== keycode) {\n        const modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n        if (modifierGetter(event)) {\n          key += modifierName + '.';\n        }\n      }\n    });\n    key += keycode;\n    return key === fullKeyCode;\n  }\n  /**\n   * Configures a handler callback for a key event.\n   * @param fullKey The event name that combines all simultaneous keystrokes.\n   * @param handler The function that responds to the key event.\n   * @param zone The zone in which the event occurred.\n   * @returns A callback function.\n   */\n  static eventCallback(fullKey, handler, zone) {\n    return event => {\n      if (KeyEventsPlugin.matchEventFullKeyCode(event, fullKey)) {\n        zone.runGuarded(() => handler(event));\n      }\n    };\n  }\n  /** @internal */\n  static _normalizeKey(keyName) {\n    return keyName === 'esc' ? 'escape' : keyName;\n  }\n  static {\n    this.ɵfac = function KeyEventsPlugin_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || KeyEventsPlugin)(i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: KeyEventsPlugin,\n      factory: KeyEventsPlugin.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(KeyEventsPlugin, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * Bootstraps an instance of an Angular application and renders a standalone component as the\n * application's root component. More information about standalone components can be found in [this\n * guide](guide/components/importing).\n *\n * @usageNotes\n * The root component passed into this function *must* be a standalone one (should have the\n * `standalone: true` flag in the `@Component` decorator config).\n *\n * ```typescript\n * @Component({\n *   standalone: true,\n *   template: 'Hello world!'\n * })\n * class RootComponent {}\n *\n * const appRef: ApplicationRef = await bootstrapApplication(RootComponent);\n * ```\n *\n * You can add the list of providers that should be available in the application injector by\n * specifying the `providers` field in an object passed as the second argument:\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     {provide: BACKEND_URL, useValue: 'https://yourdomain.com/api'}\n *   ]\n * });\n * ```\n *\n * The `importProvidersFrom` helper method can be used to collect all providers from any\n * existing NgModule (and transitively from all NgModules that it imports):\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     importProvidersFrom(SomeNgModule)\n *   ]\n * });\n * ```\n *\n * Note: the `bootstrapApplication` method doesn't include [Testability](api/core/Testability) by\n * default. You can add [Testability](api/core/Testability) by getting the list of necessary\n * providers using `provideProtractorTestingSupport()` function and adding them into the `providers`\n * array, for example:\n *\n * ```typescript\n * import {provideProtractorTestingSupport} from '@angular/platform-browser';\n *\n * await bootstrapApplication(RootComponent, {providers: [provideProtractorTestingSupport()]});\n * ```\n *\n * @param rootComponent A reference to a standalone component that should be rendered.\n * @param options Extra configuration for the bootstrap operation, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction bootstrapApplication(rootComponent, options) {\n  return ɵinternalCreateApplication({\n    rootComponent,\n    ...createProvidersConfig(options)\n  });\n}\n/**\n * Create an instance of an Angular application without bootstrapping any components. This is useful\n * for the situation where one wants to decouple application environment creation (a platform and\n * associated injectors) from rendering components on a screen. Components can be subsequently\n * bootstrapped on the returned `ApplicationRef`.\n *\n * @param options Extra configuration for the application environment, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction createApplication(options) {\n  return ɵinternalCreateApplication(createProvidersConfig(options));\n}\nfunction createProvidersConfig(options) {\n  return {\n    appProviders: [...BROWSER_MODULE_PROVIDERS, ...(options?.providers ?? [])],\n    platformProviders: INTERNAL_BROWSER_PLATFORM_PROVIDERS\n  };\n}\n/**\n * Returns a set of providers required to setup [Testability](api/core/Testability) for an\n * application bootstrapped using the `bootstrapApplication` function. The set of providers is\n * needed to support testing an application with Protractor (which relies on the Testability APIs\n * to be present).\n *\n * @returns An array of providers required to setup Testability for an application and make it\n *     available for testing using Protractor.\n *\n * @publicApi\n */\nfunction provideProtractorTestingSupport() {\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideProtractorTestingSupport` call results in app\n  // code.\n  return [...TESTABILITY_PROVIDERS];\n}\nfunction initDomAdapter() {\n  BrowserDomAdapter.makeCurrent();\n}\nfunction errorHandler() {\n  return new ErrorHandler();\n}\nfunction _document() {\n  // Tell ivy about the global document\n  ɵsetDocument(document);\n  return document;\n}\nconst INTERNAL_BROWSER_PLATFORM_PROVIDERS = [{\n  provide: PLATFORM_ID,\n  useValue: ɵPLATFORM_BROWSER_ID\n}, {\n  provide: PLATFORM_INITIALIZER,\n  useValue: initDomAdapter,\n  multi: true\n}, {\n  provide: DOCUMENT,\n  useFactory: _document,\n  deps: []\n}];\n/**\n * A factory function that returns a `PlatformRef` instance associated with browser service\n * providers.\n *\n * @publicApi\n */\nconst platformBrowser = createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\n/**\n * Internal marker to signal whether providers from the `BrowserModule` are already present in DI.\n * This is needed to avoid loading `BrowserModule` providers twice. We can't rely on the\n * `BrowserModule` presence itself, since the standalone-based bootstrap just imports\n * `BrowserModule` providers without referencing the module itself.\n */\nconst BROWSER_MODULE_PROVIDERS_MARKER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'BrowserModule Providers Marker' : '');\nconst TESTABILITY_PROVIDERS = [{\n  provide: ɵTESTABILITY_GETTER,\n  useClass: BrowserGetTestability,\n  deps: []\n}, {\n  provide: ɵTESTABILITY,\n  useClass: Testability,\n  deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n}, {\n  provide: Testability,\n  // Also provide as `Testability` for backwards-compatibility.\n  useClass: Testability,\n  deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER]\n}];\nconst BROWSER_MODULE_PROVIDERS = [{\n  provide: ɵINJECTOR_SCOPE,\n  useValue: 'root'\n}, {\n  provide: ErrorHandler,\n  useFactory: errorHandler,\n  deps: []\n}, {\n  provide: EVENT_MANAGER_PLUGINS,\n  useClass: DomEventsPlugin,\n  multi: true,\n  deps: [DOCUMENT, NgZone, PLATFORM_ID]\n}, {\n  provide: EVENT_MANAGER_PLUGINS,\n  useClass: KeyEventsPlugin,\n  multi: true,\n  deps: [DOCUMENT]\n}, DomRendererFactory2, SharedStylesHost, EventManager, {\n  provide: RendererFactory2,\n  useExisting: DomRendererFactory2\n}, {\n  provide: XhrFactory,\n  useClass: BrowserXhr,\n  deps: []\n}, typeof ngDevMode === 'undefined' || ngDevMode ? {\n  provide: BROWSER_MODULE_PROVIDERS_MARKER,\n  useValue: true\n} : []];\n/**\n * Exports required infrastructure for all Angular apps.\n * Included by default in all Angular apps created with the CLI\n * `new` command.\n * Re-exports `CommonModule` and `ApplicationModule`, making their\n * exports and providers available to all apps.\n *\n * @publicApi\n */\nclass BrowserModule {\n  constructor(providersAlreadyPresent) {\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && providersAlreadyPresent) {\n      throw new ɵRuntimeError(5100 /* RuntimeErrorCode.BROWSER_MODULE_ALREADY_LOADED */, `Providers from the \\`BrowserModule\\` have already been loaded. If you need access ` + `to common directives such as NgIf and NgFor, import the \\`CommonModule\\` instead.`);\n    }\n  }\n  /**\n   * Configures a browser-based app to transition from a server-rendered app, if\n   * one is present on the page.\n   *\n   * @param params An object containing an identifier for the app to transition.\n   * The ID must match between the client and server versions of the app.\n   * @returns The reconfigured `BrowserModule` to import into the app's root `AppModule`.\n   *\n   * @deprecated Use {@link APP_ID} instead to set the application ID.\n   */\n  static withServerTransition(params) {\n    return {\n      ngModule: BrowserModule,\n      providers: [{\n        provide: APP_ID,\n        useValue: params.appId\n      }]\n    };\n  }\n  static {\n    this.ɵfac = function BrowserModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || BrowserModule)(i0.ɵɵinject(BROWSER_MODULE_PROVIDERS_MARKER, 12));\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: BrowserModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n      imports: [CommonModule, ApplicationModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(BrowserModule, [{\n    type: NgModule,\n    args: [{\n      providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n      exports: [CommonModule, ApplicationModule]\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: SkipSelf\n    }, {\n      type: Inject,\n      args: [BROWSER_MODULE_PROVIDERS_MARKER]\n    }]\n  }], null);\n})();\n\n/**\n * A service for managing HTML `<meta>` tags.\n *\n * Properties of the `MetaDefinition` object match the attributes of the\n * HTML `<meta>` tag. These tags define document metadata that is important for\n * things like configuring a Content Security Policy, defining browser compatibility\n * and security settings, setting HTTP Headers, defining rich content for social sharing,\n * and Search Engine Optimization (SEO).\n *\n * To identify specific `<meta>` tags in a document, use an attribute selection\n * string in the format `\"tag_attribute='value string'\"`.\n * For example, an `attrSelector` value of `\"name='description'\"` matches a tag\n * whose `name` attribute has the value `\"description\"`.\n * Selectors are used with the `querySelector()` Document method,\n * in the format `meta[{attrSelector}]`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see [Document.querySelector()](https://developer.mozilla.org/docs/Web/API/Document/querySelector)\n *\n *\n * @publicApi\n */\nclass Meta {\n  constructor(_doc) {\n    this._doc = _doc;\n    this._dom = ɵgetDOM();\n  }\n  /**\n   * Retrieves or creates a specific `<meta>` tag element in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * If an existing element is found, it is returned and is not modified in any way.\n   * @param tag The definition of a `<meta>` element to match or create.\n   * @param forceCreation True to create a new element without checking whether one already exists.\n   * @returns The existing element with the same attributes and values if found,\n   * the new element if no match is found, or `null` if the tag parameter is not defined.\n   */\n  addTag(tag, forceCreation = false) {\n    if (!tag) return null;\n    return this._getOrCreateElement(tag, forceCreation);\n  }\n  /**\n   * Retrieves or creates a set of `<meta>` tag elements in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * @param tags An array of tag definitions to match or create.\n   * @param forceCreation True to create new elements without checking whether they already exist.\n   * @returns The matching elements if found, or the new elements.\n   */\n  addTags(tags, forceCreation = false) {\n    if (!tags) return [];\n    return tags.reduce((result, tag) => {\n      if (tag) {\n        result.push(this._getOrCreateElement(tag, forceCreation));\n      }\n      return result;\n    }, []);\n  }\n  /**\n   * Retrieves a `<meta>` tag element in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching element, if any.\n   */\n  getTag(attrSelector) {\n    if (!attrSelector) return null;\n    return this._doc.querySelector(`meta[${attrSelector}]`) || null;\n  }\n  /**\n   * Retrieves a set of `<meta>` tag elements in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching elements, if any.\n   */\n  getTags(attrSelector) {\n    if (!attrSelector) return [];\n    const list /*NodeList*/ = this._doc.querySelectorAll(`meta[${attrSelector}]`);\n    return list ? [].slice.call(list) : [];\n  }\n  /**\n   * Modifies an existing `<meta>` tag element in the current HTML document.\n   * @param tag The tag description with which to replace the existing tag content.\n   * @param selector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   * If not supplied, matches a tag with the same `name` or `property` attribute value as the\n   * replacement tag.\n   * @return The modified element.\n   */\n  updateTag(tag, selector) {\n    if (!tag) return null;\n    selector = selector || this._parseSelector(tag);\n    const meta = this.getTag(selector);\n    if (meta) {\n      return this._setMetaElementAttributes(tag, meta);\n    }\n    return this._getOrCreateElement(tag, true);\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param attrSelector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   */\n  removeTag(attrSelector) {\n    this.removeTagElement(this.getTag(attrSelector));\n  }\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param meta The tag definition to match against to identify an existing tag.\n   */\n  removeTagElement(meta) {\n    if (meta) {\n      this._dom.remove(meta);\n    }\n  }\n  _getOrCreateElement(meta, forceCreation = false) {\n    if (!forceCreation) {\n      const selector = this._parseSelector(meta);\n      // It's allowed to have multiple elements with the same name so it's not enough to\n      // just check that element with the same name already present on the page. We also need to\n      // check if element has tag attributes\n      const elem = this.getTags(selector).filter(elem => this._containsAttributes(meta, elem))[0];\n      if (elem !== undefined) return elem;\n    }\n    const element = this._dom.createElement('meta');\n    this._setMetaElementAttributes(meta, element);\n    const head = this._doc.getElementsByTagName('head')[0];\n    head.appendChild(element);\n    return element;\n  }\n  _setMetaElementAttributes(tag, el) {\n    Object.keys(tag).forEach(prop => el.setAttribute(this._getMetaKeyMap(prop), tag[prop]));\n    return el;\n  }\n  _parseSelector(tag) {\n    const attr = tag.name ? 'name' : 'property';\n    return `${attr}=\"${tag[attr]}\"`;\n  }\n  _containsAttributes(tag, elem) {\n    return Object.keys(tag).every(key => elem.getAttribute(this._getMetaKeyMap(key)) === tag[key]);\n  }\n  _getMetaKeyMap(prop) {\n    return META_KEYS_MAP[prop] || prop;\n  }\n  static {\n    this.ɵfac = function Meta_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || Meta)(i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: Meta,\n      factory: Meta.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Meta, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n/**\n * Mapping for MetaDefinition properties with their correct meta attribute names\n */\nconst META_KEYS_MAP = {\n  httpEquiv: 'http-equiv'\n};\n\n/**\n * A service that can be used to get and set the title of a current HTML document.\n *\n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n *\n * @publicApi\n */\nclass Title {\n  constructor(_doc) {\n    this._doc = _doc;\n  }\n  /**\n   * Get the title of the current HTML document.\n   */\n  getTitle() {\n    return this._doc.title;\n  }\n  /**\n   * Set the title of the current HTML document.\n   * @param newTitle\n   */\n  setTitle(newTitle) {\n    this._doc.title = newTitle || '';\n  }\n  static {\n    this.ɵfac = function Title_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || Title)(i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: Title,\n      factory: Title.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Title, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param value The value to export.\n */\nfunction exportNgVar(name, value) {\n  if (typeof COMPILED === 'undefined' || !COMPILED) {\n    // Note: we can't export `ng` when using closure enhanced optimization as:\n    // - closure declares globals itself for minified names, which sometimes clobber our `ng` global\n    // - we can't declare a closure extern as the namespace `ng` is already used within Google\n    //   for typings for angularJS (via `goog.provide('ng....')`).\n    const ng = ɵglobal['ng'] = ɵglobal['ng'] || {};\n    ng[name] = value;\n  }\n}\nclass ChangeDetectionPerfRecord {\n  constructor(msPerTick, numTicks) {\n    this.msPerTick = msPerTick;\n    this.numTicks = numTicks;\n  }\n}\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\nclass AngularProfiler {\n  constructor(ref) {\n    this.appRef = ref.injector.get(ApplicationRef);\n  }\n  // tslint:disable:no-console\n  /**\n   * Exercises change detection in a loop and then prints the average amount of\n   * time in milliseconds how long a single round of change detection takes for\n   * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n   * of 500 milliseconds.\n   *\n   * Optionally, a user may pass a `config` parameter containing a map of\n   * options. Supported options are:\n   *\n   * `record` (boolean) - causes the profiler to record a CPU profile while\n   * it exercises the change detector. Example:\n   *\n   * ```\n   * ng.profiler.timeChangeDetection({record: true})\n   * ```\n   */\n  timeChangeDetection(config) {\n    const record = config && config['record'];\n    const profileName = 'Change Detection';\n    // Profiler is not available in Android browsers without dev tools opened\n    if (record && 'profile' in console && typeof console.profile === 'function') {\n      console.profile(profileName);\n    }\n    const start = performance.now();\n    let numTicks = 0;\n    while (numTicks < 5 || performance.now() - start < 500) {\n      this.appRef.tick();\n      numTicks++;\n    }\n    const end = performance.now();\n    if (record && 'profileEnd' in console && typeof console.profileEnd === 'function') {\n      console.profileEnd(profileName);\n    }\n    const msPerTick = (end - start) / numTicks;\n    console.log(`ran ${numTicks} change detection cycles`);\n    console.log(`${msPerTick.toFixed(2)} ms per check`);\n    return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n  }\n}\nconst PROFILER_GLOBAL_NAME = 'profiler';\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n *\n * Usage:\n *\n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n *\n * @publicApi\n */\nfunction enableDebugTools(ref) {\n  exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n  return ref;\n}\n/**\n * Disables Angular tools.\n *\n * @publicApi\n */\nfunction disableDebugTools() {\n  exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\n\n/**\n * Predicates for use with {@link DebugElement}'s query functions.\n *\n * @publicApi\n */\nclass By {\n  /**\n   * Match all nodes.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n   */\n  static all() {\n    return () => true;\n  }\n  /**\n   * Match elements by the given CSS selector.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n   */\n  static css(selector) {\n    return debugElement => {\n      return debugElement.nativeElement != null ? elementMatches(debugElement.nativeElement, selector) : false;\n    };\n  }\n  /**\n   * Match nodes that have the given directive present.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n   */\n  static directive(type) {\n    return debugNode => debugNode.providerTokens.indexOf(type) !== -1;\n  }\n}\nfunction elementMatches(n, selector) {\n  if (ɵgetDOM().isElementNode(n)) {\n    return n.matches && n.matches(selector) || n.msMatchesSelector && n.msMatchesSelector(selector) || n.webkitMatchesSelector && n.webkitMatchesSelector(selector);\n  }\n  return false;\n}\n\n/**\n * Supported HammerJS recognizer event names.\n */\nconst EVENT_NAMES = {\n  // pan\n  'pan': true,\n  'panstart': true,\n  'panmove': true,\n  'panend': true,\n  'pancancel': true,\n  'panleft': true,\n  'panright': true,\n  'panup': true,\n  'pandown': true,\n  // pinch\n  'pinch': true,\n  'pinchstart': true,\n  'pinchmove': true,\n  'pinchend': true,\n  'pinchcancel': true,\n  'pinchin': true,\n  'pinchout': true,\n  // press\n  'press': true,\n  'pressup': true,\n  // rotate\n  'rotate': true,\n  'rotatestart': true,\n  'rotatemove': true,\n  'rotateend': true,\n  'rotatecancel': true,\n  // swipe\n  'swipe': true,\n  'swipeleft': true,\n  'swiperight': true,\n  'swipeup': true,\n  'swipedown': true,\n  // tap\n  'tap': true,\n  'doubletap': true\n};\n/**\n * DI token for providing [HammerJS](https://hammerjs.github.io/) support to Angular.\n * @see {@link HammerGestureConfig}\n *\n * @ngModule HammerModule\n * @publicApi\n */\nconst HAMMER_GESTURE_CONFIG = new InjectionToken('HammerGestureConfig');\n/**\n * Injection token used to provide a HammerLoader to Angular.\n *\n * @see {@link HammerLoader}\n *\n * @publicApi\n */\nconst HAMMER_LOADER = new InjectionToken('HammerLoader');\n/**\n * An injectable [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n * for gesture recognition. Configures specific event recognition.\n * @publicApi\n */\nclass HammerGestureConfig {\n  constructor() {\n    /**\n     * A set of supported event names for gestures to be used in Angular.\n     * Angular supports all built-in recognizers, as listed in\n     * [HammerJS documentation](https://hammerjs.github.io/).\n     */\n    this.events = [];\n    /**\n     * Maps gesture event names to a set of configuration options\n     * that specify overrides to the default values for specific properties.\n     *\n     * The key is a supported event name to be configured,\n     * and the options object contains a set of properties, with override values\n     * to be applied to the named recognizer event.\n     * For example, to disable recognition of the rotate event, specify\n     *  `{\"rotate\": {\"enable\": false}}`.\n     *\n     * Properties that are not present take the HammerJS default values.\n     * For information about which properties are supported for which events,\n     * and their allowed and default values, see\n     * [HammerJS documentation](https://hammerjs.github.io/).\n     *\n     */\n    this.overrides = {};\n  }\n  /**\n   * Creates a [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n   * and attaches it to a given HTML element.\n   * @param element The element that will recognize gestures.\n   * @returns A HammerJS event-manager object.\n   */\n  buildHammer(element) {\n    const mc = new Hammer(element, this.options);\n    mc.get('pinch').set({\n      enable: true\n    });\n    mc.get('rotate').set({\n      enable: true\n    });\n    for (const eventName in this.overrides) {\n      mc.get(eventName).set(this.overrides[eventName]);\n    }\n    return mc;\n  }\n  static {\n    this.ɵfac = function HammerGestureConfig_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HammerGestureConfig)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: HammerGestureConfig,\n      factory: HammerGestureConfig.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGestureConfig, [{\n    type: Injectable\n  }], null, null);\n})();\n/**\n * Event plugin that adds Hammer support to an application.\n *\n * @ngModule HammerModule\n */\nclass HammerGesturesPlugin extends EventManagerPlugin {\n  constructor(doc, _config, console, loader) {\n    super(doc);\n    this._config = _config;\n    this.console = console;\n    this.loader = loader;\n    this._loaderPromise = null;\n  }\n  supports(eventName) {\n    if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n      return false;\n    }\n    if (!window.Hammer && !this.loader) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        this.console.warn(`The \"${eventName}\" event cannot be bound because Hammer.JS is not ` + `loaded and no custom loader has been specified.`);\n      }\n      return false;\n    }\n    return true;\n  }\n  addEventListener(element, eventName, handler) {\n    const zone = this.manager.getZone();\n    eventName = eventName.toLowerCase();\n    // If Hammer is not present but a loader is specified, we defer adding the event listener\n    // until Hammer is loaded.\n    if (!window.Hammer && this.loader) {\n      this._loaderPromise = this._loaderPromise || zone.runOutsideAngular(() => this.loader());\n      // This `addEventListener` method returns a function to remove the added listener.\n      // Until Hammer is loaded, the returned function needs to *cancel* the registration rather\n      // than remove anything.\n      let cancelRegistration = false;\n      let deregister = () => {\n        cancelRegistration = true;\n      };\n      zone.runOutsideAngular(() => this._loaderPromise.then(() => {\n        // If Hammer isn't actually loaded when the custom loader resolves, give up.\n        if (!window.Hammer) {\n          if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            this.console.warn(`The custom HAMMER_LOADER completed, but Hammer.JS is not present.`);\n          }\n          deregister = () => {};\n          return;\n        }\n        if (!cancelRegistration) {\n          // Now that Hammer is loaded and the listener is being loaded for real,\n          // the deregistration function changes from canceling registration to\n          // removal.\n          deregister = this.addEventListener(element, eventName, handler);\n        }\n      }).catch(() => {\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n          this.console.warn(`The \"${eventName}\" event cannot be bound because the custom ` + `Hammer.JS loader failed.`);\n        }\n        deregister = () => {};\n      }));\n      // Return a function that *executes* `deregister` (and not `deregister` itself) so that we\n      // can change the behavior of `deregister` once the listener is added. Using a closure in\n      // this way allows us to avoid any additional data structures to track listener removal.\n      return () => {\n        deregister();\n      };\n    }\n    return zone.runOutsideAngular(() => {\n      // Creating the manager bind events, must be done outside of angular\n      const mc = this._config.buildHammer(element);\n      const callback = function (eventObj) {\n        zone.runGuarded(function () {\n          handler(eventObj);\n        });\n      };\n      mc.on(eventName, callback);\n      return () => {\n        mc.off(eventName, callback);\n        // destroy mc to prevent memory leak\n        if (typeof mc.destroy === 'function') {\n          mc.destroy();\n        }\n      };\n    });\n  }\n  isCustomEvent(eventName) {\n    return this._config.events.indexOf(eventName) > -1;\n  }\n  static {\n    this.ɵfac = function HammerGesturesPlugin_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HammerGesturesPlugin)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(HAMMER_GESTURE_CONFIG), i0.ɵɵinject(i0.ɵConsole), i0.ɵɵinject(HAMMER_LOADER, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: HammerGesturesPlugin,\n      factory: HammerGesturesPlugin.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerGesturesPlugin, [{\n    type: Injectable\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: HammerGestureConfig,\n    decorators: [{\n      type: Inject,\n      args: [HAMMER_GESTURE_CONFIG]\n    }]\n  }, {\n    type: i0.ɵConsole\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [HAMMER_LOADER]\n    }]\n  }], null);\n})();\n/**\n * Adds support for HammerJS.\n *\n * Import this module at the root of your application so that Angular can work with\n * HammerJS to detect gesture events.\n *\n * Note that applications still need to include the HammerJS script itself. This module\n * simply sets up the coordination layer between HammerJS and Angular's `EventManager`.\n *\n * @publicApi\n */\nclass HammerModule {\n  static {\n    this.ɵfac = function HammerModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || HammerModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: HammerModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [{\n        provide: EVENT_MANAGER_PLUGINS,\n        useClass: HammerGesturesPlugin,\n        multi: true,\n        deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n      }, {\n        provide: HAMMER_GESTURE_CONFIG,\n        useClass: HammerGestureConfig,\n        deps: []\n      }]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(HammerModule, [{\n    type: NgModule,\n    args: [{\n      providers: [{\n        provide: EVENT_MANAGER_PLUGINS,\n        useClass: HammerGesturesPlugin,\n        multi: true,\n        deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]]\n      }, {\n        provide: HAMMER_GESTURE_CONFIG,\n        useClass: HammerGestureConfig,\n        deps: []\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n *\n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n *\n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n *\n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n *\n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n *\n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n *\n * @security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](https://g.co/ng/security).\n *\n * @publicApi\n */\nclass DomSanitizer {\n  static {\n    this.ɵfac = function DomSanitizer_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DomSanitizer)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DomSanitizer,\n      factory: function DomSanitizer_Factory(__ngFactoryType__) {\n        let __ngConditionalFactory__ = null;\n        if (__ngFactoryType__) {\n          __ngConditionalFactory__ = new (__ngFactoryType__ || DomSanitizer)();\n        } else {\n          __ngConditionalFactory__ = i0.ɵɵinject(DomSanitizerImpl);\n        }\n        return __ngConditionalFactory__;\n      },\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root',\n      useExisting: forwardRef(() => DomSanitizerImpl)\n    }]\n  }], null, null);\n})();\nclass DomSanitizerImpl extends DomSanitizer {\n  constructor(_doc) {\n    super();\n    this._doc = _doc;\n  }\n  sanitize(ctx, value) {\n    if (value == null) return null;\n    switch (ctx) {\n      case SecurityContext.NONE:\n        return value;\n      case SecurityContext.HTML:\n        if (ɵallowSanitizationBypassAndThrow(value, \"HTML\" /* BypassType.Html */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        return ɵ_sanitizeHtml(this._doc, String(value)).toString();\n      case SecurityContext.STYLE:\n        if (ɵallowSanitizationBypassAndThrow(value, \"Style\" /* BypassType.Style */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        return value;\n      case SecurityContext.SCRIPT:\n        if (ɵallowSanitizationBypassAndThrow(value, \"Script\" /* BypassType.Script */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        throw new ɵRuntimeError(5200 /* RuntimeErrorCode.SANITIZATION_UNSAFE_SCRIPT */, (typeof ngDevMode === 'undefined' || ngDevMode) && 'unsafe value used in a script context');\n      case SecurityContext.URL:\n        if (ɵallowSanitizationBypassAndThrow(value, \"URL\" /* BypassType.Url */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        return ɵ_sanitizeUrl(String(value));\n      case SecurityContext.RESOURCE_URL:\n        if (ɵallowSanitizationBypassAndThrow(value, \"ResourceURL\" /* BypassType.ResourceUrl */)) {\n          return ɵunwrapSafeValue(value);\n        }\n        throw new ɵRuntimeError(5201 /* RuntimeErrorCode.SANITIZATION_UNSAFE_RESOURCE_URL */, (typeof ngDevMode === 'undefined' || ngDevMode) && `unsafe value used in a resource URL context (see ${ɵXSS_SECURITY_URL})`);\n      default:\n        throw new ɵRuntimeError(5202 /* RuntimeErrorCode.SANITIZATION_UNEXPECTED_CTX */, (typeof ngDevMode === 'undefined' || ngDevMode) && `Unexpected SecurityContext ${ctx} (see ${ɵXSS_SECURITY_URL})`);\n    }\n  }\n  bypassSecurityTrustHtml(value) {\n    return ɵbypassSanitizationTrustHtml(value);\n  }\n  bypassSecurityTrustStyle(value) {\n    return ɵbypassSanitizationTrustStyle(value);\n  }\n  bypassSecurityTrustScript(value) {\n    return ɵbypassSanitizationTrustScript(value);\n  }\n  bypassSecurityTrustUrl(value) {\n    return ɵbypassSanitizationTrustUrl(value);\n  }\n  bypassSecurityTrustResourceUrl(value) {\n    return ɵbypassSanitizationTrustResourceUrl(value);\n  }\n  static {\n    this.ɵfac = function DomSanitizerImpl_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DomSanitizerImpl)(i0.ɵɵinject(DOCUMENT));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: DomSanitizerImpl,\n      factory: DomSanitizerImpl.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DomSanitizerImpl, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }], null);\n})();\n\n/**\n * The list of features as an enum to uniquely type each `HydrationFeature`.\n * @see {@link HydrationFeature}\n *\n * @publicApi\n */\nvar HydrationFeatureKind;\n(function (HydrationFeatureKind) {\n  HydrationFeatureKind[HydrationFeatureKind[\"NoHttpTransferCache\"] = 0] = \"NoHttpTransferCache\";\n  HydrationFeatureKind[HydrationFeatureKind[\"HttpTransferCacheOptions\"] = 1] = \"HttpTransferCacheOptions\";\n  HydrationFeatureKind[HydrationFeatureKind[\"I18nSupport\"] = 2] = \"I18nSupport\";\n  HydrationFeatureKind[HydrationFeatureKind[\"EventReplay\"] = 3] = \"EventReplay\";\n})(HydrationFeatureKind || (HydrationFeatureKind = {}));\n/**\n * Helper function to create an object that represents a Hydration feature.\n */\nfunction hydrationFeature(ɵkind, ɵproviders = [], ɵoptions = {}) {\n  return {\n    ɵkind,\n    ɵproviders\n  };\n}\n/**\n * Disables HTTP transfer cache. Effectively causes HTTP requests to be performed twice: once on the\n * server and other one on the browser.\n *\n * @publicApi\n */\nfunction withNoHttpTransferCache() {\n  // This feature has no providers and acts as a flag that turns off\n  // HTTP transfer cache (which otherwise is turned on by default).\n  return hydrationFeature(HydrationFeatureKind.NoHttpTransferCache);\n}\n/**\n * The function accepts an object, which allows to configure cache parameters,\n * such as which headers should be included (no headers are included by default),\n * whether POST requests should be cached or a callback function to determine if a\n * particular request should be cached.\n *\n * @publicApi\n */\nfunction withHttpTransferCacheOptions(options) {\n  // This feature has no providers and acts as a flag to pass options to the HTTP transfer cache.\n  return hydrationFeature(HydrationFeatureKind.HttpTransferCacheOptions, ɵwithHttpTransferCache(options));\n}\n/**\n * Enables support for hydrating i18n blocks.\n *\n * @developerPreview\n * @publicApi\n */\nfunction withI18nSupport() {\n  return hydrationFeature(HydrationFeatureKind.I18nSupport, ɵwithI18nSupport());\n}\n/**\n * Enables support for replaying user events (e.g. `click`s) that happened on a page\n * before hydration logic has completed. Once an application is hydrated, all captured\n * events are replayed and relevant event listeners are executed.\n *\n * @usageNotes\n *\n * Basic example of how you can enable event replay in your application when\n * `bootstrapApplication` function is used:\n * ```\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration(withEventReplay())]\n * });\n * ```\n * @developerPreview\n * @publicApi\n * @see {@link provideClientHydration}\n */\nfunction withEventReplay() {\n  return hydrationFeature(HydrationFeatureKind.EventReplay, ɵwithEventReplay());\n}\n/**\n * Returns an `ENVIRONMENT_INITIALIZER` token setup with a function\n * that verifies whether compatible ZoneJS was used in an application\n * and logs a warning in a console if it's not the case.\n */\nfunction provideZoneJsCompatibilityDetector() {\n  return [{\n    provide: ENVIRONMENT_INITIALIZER,\n    useValue: () => {\n      const ngZone = inject(NgZone);\n      const isZoneless = inject(ɵZONELESS_ENABLED);\n      // Checking `ngZone instanceof NgZone` would be insufficient here,\n      // because custom implementations might use NgZone as a base class.\n      if (!isZoneless && ngZone.constructor !== NgZone) {\n        const console = inject(ɵConsole);\n        const message = ɵformatRuntimeError(-5000 /* RuntimeErrorCode.UNSUPPORTED_ZONEJS_INSTANCE */, 'Angular detected that hydration was enabled for an application ' + 'that uses a custom or a noop Zone.js implementation. ' + 'This is not yet a fully supported configuration.');\n        // tslint:disable-next-line:no-console\n        console.warn(message);\n      }\n    },\n    multi: true\n  }];\n}\n/**\n * Sets up providers necessary to enable hydration functionality for the application.\n *\n * By default, the function enables the recommended set of features for the optimal\n * performance for most of the applications. It includes the following features:\n *\n * * Reconciling DOM hydration. Learn more about it [here](guide/hydration).\n * * [`HttpClient`](api/common/http/HttpClient) response caching while running on the server and\n * transferring this cache to the client to avoid extra HTTP requests. Learn more about data caching\n * [here](guide/ssr#caching-data-when-using-httpclient).\n *\n * These functions allow you to disable some of the default features or enable new ones:\n *\n * * {@link withNoHttpTransferCache} to disable HTTP transfer cache\n * * {@link withHttpTransferCacheOptions} to configure some HTTP transfer cache options\n * * {@link withI18nSupport} to enable hydration support for i18n blocks\n * * {@link withEventReplay} to enable support for replaying user events\n *\n * @usageNotes\n *\n * Basic example of how you can enable hydration in your application when\n * `bootstrapApplication` function is used:\n * ```\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration()]\n * });\n * ```\n *\n * Alternatively if you are using NgModules, you would add `provideClientHydration`\n * to your root app module's provider list.\n * ```\n * @NgModule({\n *   declarations: [RootCmp],\n *   bootstrap: [RootCmp],\n *   providers: [provideClientHydration()],\n * })\n * export class AppModule {}\n * ```\n *\n * @see {@link withNoHttpTransferCache}\n * @see {@link withHttpTransferCacheOptions}\n * @see {@link withI18nSupport}\n * @see {@link withEventReplay}\n *\n * @param features Optional features to configure additional router behaviors.\n * @returns A set of providers to enable hydration.\n *\n * @publicApi\n */\nfunction provideClientHydration(...features) {\n  const providers = [];\n  const featuresKind = new Set();\n  const hasHttpTransferCacheOptions = featuresKind.has(HydrationFeatureKind.HttpTransferCacheOptions);\n  for (const {\n    ɵproviders,\n    ɵkind\n  } of features) {\n    featuresKind.add(ɵkind);\n    if (ɵproviders.length) {\n      providers.push(ɵproviders);\n    }\n  }\n  if (typeof ngDevMode !== 'undefined' && ngDevMode && featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) && hasHttpTransferCacheOptions) {\n    // TODO: Make this a runtime error\n    throw new Error('Configuration error: found both withHttpTransferCacheOptions() and withNoHttpTransferCache() in the same call to provideClientHydration(), which is a contradiction.');\n  }\n  return makeEnvironmentProviders([typeof ngDevMode !== 'undefined' && ngDevMode ? provideZoneJsCompatibilityDetector() : [], ɵwithDomHydration(), featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) || hasHttpTransferCacheOptions ? [] : ɵwithHttpTransferCache({}), providers]);\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('18.2.13');\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserModule, By, DomSanitizer, EVENT_MANAGER_PLUGINS, EventManager, EventManagerPlugin, HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerModule, HydrationFeatureKind, Meta, REMOVE_STYLES_ON_COMPONENT_DESTROY, Title, VERSION, bootstrapApplication, createApplication, disableDebugTools, enableDebugTools, platformBrowser, provideClientHydration, provideProtractorTestingSupport, withEventReplay, withHttpTransferCacheOptions, withI18nSupport, withNoHttpTransferCache, BrowserDomAdapter as ɵBrowserDomAdapter, BrowserGetTestability as ɵBrowserGetTestability, DomEventsPlugin as ɵDomEventsPlugin, DomRendererFactory2 as ɵDomRendererFactory2, DomSanitizerImpl as ɵDomSanitizerImpl, HammerGesturesPlugin as ɵHammerGesturesPlugin, INTERNAL_BROWSER_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS, KeyEventsPlugin as ɵKeyEventsPlugin, SharedStylesHost as ɵSharedStylesHost, initDomAdapter as ɵinitDomAdapter };", "map": {"version": 3, "names": ["ɵDomAdapter", "ɵsetRootDomAdapter", "ɵparseCookieValue", "ɵgetDOM", "isPlatformServer", "DOCUMENT", "ɵPLATFORM_BROWSER_ID", "XhrFactory", "CommonModule", "i0", "ɵglobal", "ɵRuntimeError", "Injectable", "InjectionToken", "Inject", "APP_ID", "CSP_NONCE", "PLATFORM_ID", "Optional", "ViewEncapsulation", "RendererStyleFlags2", "ɵinternalCreateApplication", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵsetDocument", "PLATFORM_INITIALIZER", "createPlatformFactory", "platformCore", "ɵTESTABILITY_GETTER", "ɵTESTABILITY", "Testability", "NgZone", "TestabilityRegistry", "ɵINJECTOR_SCOPE", "RendererFactory2", "ApplicationModule", "NgModule", "SkipSelf", "ApplicationRef", "ɵConsole", "forwardRef", "ɵXSS_SECURITY_URL", "SecurityContext", "ɵallowSanitizationBypassAndThrow", "ɵunwrapSafeValue", "ɵ_sanitizeUrl", "ɵ_sanitizeHtml", "ɵbypassSanitizationTrustHtml", "ɵbypassSanitizationTrustStyle", "ɵbypassSanitizationTrustScript", "ɵbypassSanitizationTrustUrl", "ɵbypassSanitizationTrustResourceUrl", "ɵwithI18nSupport", "ɵwithEventReplay", "ENVIRONMENT_INITIALIZER", "inject", "ɵZONELESS_ENABLED", "ɵformatRuntimeError", "makeEnvironmentProviders", "ɵwithDomHydration", "Version", "ɵwithHttpTransferCache", "GenericBrowserDomAdapter", "constructor", "arguments", "supportsDOMEvents", "BrowserDomAdapter", "makeCurrent", "onAndCancel", "el", "evt", "listener", "addEventListener", "removeEventListener", "dispatchEvent", "remove", "node", "createElement", "tagName", "doc", "getDefaultDocument", "createHtmlDocument", "document", "implementation", "createHTMLDocument", "isElementNode", "nodeType", "Node", "ELEMENT_NODE", "isShadowRoot", "DocumentFragment", "getGlobalEventTarget", "target", "window", "body", "getBaseHref", "href", "getBaseElementHref", "relativePath", "resetBaseElement", "baseElement", "getUserAgent", "navigator", "userAgent", "<PERSON><PERSON><PERSON><PERSON>", "name", "cookie", "querySelector", "getAttribute", "url", "URL", "baseURI", "pathname", "BrowserGetTestability", "addToWindow", "registry", "elem", "findInAncestors", "testability", "findTestabilityInTree", "ngDevMode", "getAllTestabilities", "getAllRootElements", "whenAllStable", "callback", "testabilities", "count", "length", "decrement", "for<PERSON>ach", "whenStable", "push", "t", "getTestability", "host", "parentElement", "BrowserXhr", "build", "XMLHttpRequest", "ɵfac", "BrowserXhr_Factory", "__ngFactoryType__", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ɵsetClassMetadata", "type", "EVENT_MANAGER_PLUGINS", "EventManager", "plugins", "_zone", "_eventNameToPlugin", "Map", "plugin", "manager", "_plugins", "slice", "reverse", "element", "eventName", "handler", "_findPluginFor", "getZone", "get", "find", "supports", "set", "EventManager_Factory", "ɵɵinject", "undefined", "decorators", "args", "EventManagerPlugin", "_doc", "APP_ID_ATTRIBUTE_NAME", "SharedStylesHost", "appId", "nonce", "platformId", "styleRef", "hostNodes", "Set", "styleNodesInDOM", "collectServerRenderedStyles", "platformIsServer", "resetHostNodes", "addStyles", "styles", "style", "usageCount", "changeUsageCount", "onStyleAdded", "removeStyles", "onStyleRemoved", "ngOnDestroy", "clear", "getAllStyles", "addHost", "hostNode", "add", "addStyleToHost", "removeHost", "delete", "keys", "elements", "head", "querySelectorAll", "styleMap", "textContent", "delta", "map", "has", "styleRefValue", "usage", "getStyleElement", "styleEl", "parentNode", "removeAttribute", "setAttribute", "append<PERSON><PERSON><PERSON>", "styleElRef", "SharedStylesHost_Factory", "Document", "NAMESPACE_URIS", "COMPONENT_REGEX", "COMPONENT_VARIABLE", "HOST_ATTR", "CONTENT_ATTR", "REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT", "REMOVE_STYLES_ON_COMPONENT_DESTROY", "providedIn", "shimContentAttribute", "componentShortId", "replace", "shimHostAttribute", "shim<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "compId", "s", "DomRendererFactory2", "eventManager", "sharedStylesHost", "removeStylesOnCompDestroy", "ngZone", "rendererByCompId", "defaultRenderer", "DefaultDomRenderer2", "<PERSON><PERSON><PERSON><PERSON>", "encapsulation", "ShadowDom", "Emulated", "renderer", "getOr<PERSON><PERSON><PERSON><PERSON><PERSON>", "EmulatedEncapsulationDomRenderer2", "applyToHost", "NoneEncapsulationDomRenderer", "applyStyles", "id", "ShadowDom<PERSON><PERSON><PERSON>", "DomRendererFactory2_Factory", "Object", "data", "create", "throwOnSyntheticProps", "destroyNode", "destroy", "namespace", "createElementNS", "createComment", "value", "createText", "createTextNode", "parent", "<PERSON><PERSON><PERSON><PERSON>", "targetParent", "isTemplateNode", "content", "insertBefore", "refChild", "<PERSON><PERSON><PERSON><PERSON>", "_parent", "<PERSON><PERSON><PERSON><PERSON>", "selectRootElement", "selectorOrNode", "preserve<PERSON><PERSON>nt", "nextS<PERSON>ling", "namespaceUri", "setAttributeNS", "removeAttributeNS", "addClass", "classList", "removeClass", "setStyle", "flags", "DashCase", "Important", "setProperty", "removeStyle", "removeProperty", "checkNoSyntheticProp", "setValue", "nodeValue", "listen", "event", "Error", "decoratePreventDefault", "<PERSON><PERSON><PERSON><PERSON>", "allowDefaultBehavior", "runGuarded", "preventDefault", "AT_CHARCODE", "charCodeAt", "<PERSON><PERSON><PERSON>", "hostEl", "component", "shadowRoot", "attachShadow", "mode", "nodeOrShadowRoot", "contentAttr", "hostAttr", "DomEventsPlugin", "DomEventsPlugin_Factory", "MODIFIER_KEYS", "_keyMap", "MODIFIER_KEY_GETTERS", "altKey", "ctrl<PERSON>ey", "metaKey", "shift<PERSON>ey", "KeyEventsPlugin", "parseEventName", "parsedEvent", "outsideH<PERSON>ler", "eventCallback", "runOutsideAngular", "parts", "toLowerCase", "split", "domEventName", "shift", "key", "_normalizeKey", "pop", "<PERSON><PERSON><PERSON>", "codeIX", "indexOf", "splice", "modifierName", "index", "result", "matchEventFullKeyCode", "fullKeyCode", "keycode", "code", "modifierGetter", "zone", "keyName", "KeyEventsPlugin_Factory", "bootstrapApplication", "rootComponent", "options", "createProvidersConfig", "createApplication", "appProviders", "BROWSER_MODULE_PROVIDERS", "providers", "platformProviders", "INTERNAL_BROWSER_PLATFORM_PROVIDERS", "provideProtractorTestingSupport", "TESTABILITY_PROVIDERS", "initDomAdapter", "<PERSON><PERSON><PERSON><PERSON>", "_document", "provide", "useValue", "multi", "useFactory", "deps", "platformBrowser", "BROWSER_MODULE_PROVIDERS_MARKER", "useClass", "useExisting", "BrowserModule", "providersAlreadyPresent", "withServerTransition", "params", "ngModule", "BrowserModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "Meta", "_dom", "addTag", "tag", "forceCreation", "_getOrCreateElement", "addTags", "tags", "reduce", "getTag", "attrSelector", "getTags", "list", "call", "updateTag", "selector", "_parseSelector", "meta", "_setMetaElementAttributes", "removeTag", "removeTagElement", "filter", "_containsAttributes", "getElementsByTagName", "prop", "_getMetaKeyMap", "attr", "every", "META_KEYS_MAP", "Meta_Factory", "httpEquiv", "Title", "getTitle", "title", "setTitle", "newTitle", "Title_Factory", "exportNgVar", "COMPILED", "ng", "ChangeDetectionPerfRecord", "msPerTick", "numTicks", "AngularProfiler", "ref", "appRef", "injector", "timeChangeDetection", "config", "record", "profileName", "console", "profile", "start", "performance", "now", "tick", "end", "profileEnd", "log", "toFixed", "PROFILER_GLOBAL_NAME", "enableDebugTools", "disableDebugTools", "By", "all", "css", "debugElement", "nativeElement", "elementMatches", "directive", "debugNode", "providerTokens", "n", "matches", "msMatchesSelector", "webkitMatchesSelector", "EVENT_NAMES", "HAMMER_GESTURE_CONFIG", "HAMMER_LOADER", "HammerGestureConfig", "events", "overrides", "buildHammer", "mc", "Hammer", "enable", "HammerGestureConfig_Factory", "HammerGesturesPlugin", "_config", "loader", "_loaderPromise", "hasOwnProperty", "isCustomEvent", "warn", "cancelRegistration", "deregister", "then", "catch", "eventObj", "on", "off", "HammerGesturesPlugin_Factory", "HammerModule", "HammerModule_Factory", "Dom<PERSON><PERSON><PERSON>zer", "DomSanitizer_Factory", "__ngConditionalFactory__", "DomSanitizerImpl", "sanitize", "ctx", "NONE", "HTML", "String", "toString", "STYLE", "SCRIPT", "RESOURCE_URL", "bypassSecurityTrustHtml", "bypassSecurityTrustStyle", "bypassSecurityTrustScript", "bypassSecurityTrustUrl", "bypassSecurityTrustResourceUrl", "DomSanitizerImpl_Factory", "HydrationFeatureKind", "hydrationFeature", "ɵkind", "ɵproviders", "ɵoptions", "withNoHttpTransferCache", "NoHttpTransferCache", "withHttpTransferCacheOptions", "HttpTransferCacheOptions", "withI18nSupport", "I18nSupport", "withEventReplay", "EventReplay", "provideZoneJsCompatibilityDetector", "isZoneless", "message", "provideClientHydration", "features", "featuresKind", "hasHttpTransferCacheOptions", "VERSION", "ɵBrowserDomAdapter", "ɵBrowserGetTestability", "ɵDomEventsPlugin", "ɵDomRendererFactory2", "ɵDomSanitizerImpl", "ɵHammerGesturesPlugin", "ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS", "ɵKeyEventsPlugin", "ɵSharedStylesHost", "ɵinitDomAdapter"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/monthsary-website/node_modules/@angular/platform-browser/fesm2022/platform-browser.mjs"], "sourcesContent": ["/**\n * @license Angular v18.2.13\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { ɵDomAdapter, ɵsetRootDomAdapter, ɵparseCookieValue, ɵgetDOM, isPlatformServer, DOCUMENT, ɵPLATFORM_BROWSER_ID, XhrFactory, CommonModule } from '@angular/common';\nexport { ɵgetDOM } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { ɵglobal, ɵRuntimeError, Injectable, InjectionToken, Inject, APP_ID, CSP_NONCE, PLATFORM_ID, Optional, ViewEncapsulation, RendererStyleFlags2, ɵinternalCreateApplication, ErrorHandler, ɵsetDocument, PLATFORM_INITIALIZER, createPlatformFactory, platformCore, ɵTESTABILITY_GETTER, ɵTESTABILITY, Testability, NgZone, TestabilityRegistry, ɵINJECTOR_SCOPE, RendererFactory2, ApplicationModule, NgModule, SkipSelf, ApplicationRef, ɵConsole, forwardRef, ɵXSS_SECURITY_URL, SecurityContext, ɵallowSanitizationBypassAndThrow, ɵunwrapSafeValue, ɵ_sanitizeUrl, ɵ_sanitizeHtml, ɵbypassSanitizationTrustHtml, ɵbypassSanitizationTrustStyle, ɵbypassSanitizationTrustScript, ɵbypassSanitizationTrustUrl, ɵbypassSanitizationTrustResourceUrl, ɵwithI18nSupport, ɵwithEventReplay, ENVIRONMENT_INITIALIZER, inject, ɵZONELESS_ENABLED, ɵformatRuntimeError, makeEnvironmentProviders, ɵwithDomHydration, Version } from '@angular/core';\nimport { ɵwithHttpTransferCache } from '@angular/common/http';\n\n/**\n * Provides DOM operations in any browser environment.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nclass GenericBrowserDomAdapter extends ɵDomAdapter {\n    constructor() {\n        super(...arguments);\n        this.supportsDOMEvents = true;\n    }\n}\n\n/**\n * A `DomAdapter` powered by full browser DOM APIs.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\n/* tslint:disable:requireParameterType no-console */\nclass BrowserDomAdapter extends GenericBrowserDomAdapter {\n    static makeCurrent() {\n        ɵsetRootDomAdapter(new BrowserDomAdapter());\n    }\n    onAndCancel(el, evt, listener) {\n        el.addEventListener(evt, listener);\n        return () => {\n            el.removeEventListener(evt, listener);\n        };\n    }\n    dispatchEvent(el, evt) {\n        el.dispatchEvent(evt);\n    }\n    remove(node) {\n        node.remove();\n    }\n    createElement(tagName, doc) {\n        doc = doc || this.getDefaultDocument();\n        return doc.createElement(tagName);\n    }\n    createHtmlDocument() {\n        return document.implementation.createHTMLDocument('fakeTitle');\n    }\n    getDefaultDocument() {\n        return document;\n    }\n    isElementNode(node) {\n        return node.nodeType === Node.ELEMENT_NODE;\n    }\n    isShadowRoot(node) {\n        return node instanceof DocumentFragment;\n    }\n    /** @deprecated No longer being used in Ivy code. To be removed in version 14. */\n    getGlobalEventTarget(doc, target) {\n        if (target === 'window') {\n            return window;\n        }\n        if (target === 'document') {\n            return doc;\n        }\n        if (target === 'body') {\n            return doc.body;\n        }\n        return null;\n    }\n    getBaseHref(doc) {\n        const href = getBaseElementHref();\n        return href == null ? null : relativePath(href);\n    }\n    resetBaseElement() {\n        baseElement = null;\n    }\n    getUserAgent() {\n        return window.navigator.userAgent;\n    }\n    getCookie(name) {\n        return ɵparseCookieValue(document.cookie, name);\n    }\n}\nlet baseElement = null;\nfunction getBaseElementHref() {\n    baseElement = baseElement || document.querySelector('base');\n    return baseElement ? baseElement.getAttribute('href') : null;\n}\nfunction relativePath(url) {\n    // The base URL doesn't really matter, we just need it so relative paths have something\n    // to resolve against. In the browser `HTMLBaseElement.href` is always absolute.\n    return new URL(url, document.baseURI).pathname;\n}\n\nclass BrowserGetTestability {\n    addToWindow(registry) {\n        ɵglobal['getAngularTestability'] = (elem, findInAncestors = true) => {\n            const testability = registry.findTestabilityInTree(elem, findInAncestors);\n            if (testability == null) {\n                throw new ɵRuntimeError(5103 /* RuntimeErrorCode.TESTABILITY_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    'Could not find testability for element.');\n            }\n            return testability;\n        };\n        ɵglobal['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n        ɵglobal['getAllAngularRootElements'] = () => registry.getAllRootElements();\n        const whenAllStable = (callback) => {\n            const testabilities = ɵglobal['getAllAngularTestabilities']();\n            let count = testabilities.length;\n            const decrement = function () {\n                count--;\n                if (count == 0) {\n                    callback();\n                }\n            };\n            testabilities.forEach((testability) => {\n                testability.whenStable(decrement);\n            });\n        };\n        if (!ɵglobal['frameworkStabilizers']) {\n            ɵglobal['frameworkStabilizers'] = [];\n        }\n        ɵglobal['frameworkStabilizers'].push(whenAllStable);\n    }\n    findTestabilityInTree(registry, elem, findInAncestors) {\n        if (elem == null) {\n            return null;\n        }\n        const t = registry.getTestability(elem);\n        if (t != null) {\n            return t;\n        }\n        else if (!findInAncestors) {\n            return null;\n        }\n        if (ɵgetDOM().isShadowRoot(elem)) {\n            return this.findTestabilityInTree(registry, elem.host, true);\n        }\n        return this.findTestabilityInTree(registry, elem.parentElement, true);\n    }\n}\n\n/**\n * A factory for `HttpXhrBackend` that uses the `XMLHttpRequest` browser API.\n */\nclass BrowserXhr {\n    build() {\n        return new XMLHttpRequest();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: BrowserXhr, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: BrowserXhr }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: BrowserXhr, decorators: [{\n            type: Injectable\n        }] });\n\n/**\n * The injection token for plugins of the `EventManager` service.\n *\n * @publicApi\n */\nconst EVENT_MANAGER_PLUGINS = new InjectionToken(ngDevMode ? 'EventManagerPlugins' : '');\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\nclass EventManager {\n    /**\n     * Initializes an instance of the event-manager service.\n     */\n    constructor(plugins, _zone) {\n        this._zone = _zone;\n        this._eventNameToPlugin = new Map();\n        plugins.forEach((plugin) => {\n            plugin.manager = this;\n        });\n        this._plugins = plugins.slice().reverse();\n    }\n    /**\n     * Registers a handler for a specific element and event.\n     *\n     * @param element The HTML element to receive event notifications.\n     * @param eventName The name of the event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @returns  A callback function that can be used to remove the handler.\n     */\n    addEventListener(element, eventName, handler) {\n        const plugin = this._findPluginFor(eventName);\n        return plugin.addEventListener(element, eventName, handler);\n    }\n    /**\n     * Retrieves the compilation zone in which event listeners are registered.\n     */\n    getZone() {\n        return this._zone;\n    }\n    /** @internal */\n    _findPluginFor(eventName) {\n        let plugin = this._eventNameToPlugin.get(eventName);\n        if (plugin) {\n            return plugin;\n        }\n        const plugins = this._plugins;\n        plugin = plugins.find((plugin) => plugin.supports(eventName));\n        if (!plugin) {\n            throw new ɵRuntimeError(5101 /* RuntimeErrorCode.NO_PLUGIN_FOR_EVENT */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                `No event manager plugin found for event ${eventName}`);\n        }\n        this._eventNameToPlugin.set(eventName, plugin);\n        return plugin;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: EventManager, deps: [{ token: EVENT_MANAGER_PLUGINS }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: EventManager }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: EventManager, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [EVENT_MANAGER_PLUGINS]\n                }] }, { type: i0.NgZone }] });\n/**\n * The plugin definition for the `EventManager` class\n *\n * It can be used as a base class to create custom manager plugins, i.e. you can create your own\n * class that extends the `EventManagerPlugin` one.\n *\n * @publicApi\n */\nclass EventManagerPlugin {\n    // TODO: remove (has some usage in G3)\n    constructor(_doc) {\n        this._doc = _doc;\n    }\n}\n\n/** The style elements attribute name used to set value of `APP_ID` token. */\nconst APP_ID_ATTRIBUTE_NAME = 'ng-app-id';\nclass SharedStylesHost {\n    constructor(doc, appId, nonce, platformId = {}) {\n        this.doc = doc;\n        this.appId = appId;\n        this.nonce = nonce;\n        this.platformId = platformId;\n        // Maps all registered host nodes to a list of style nodes that have been added to the host node.\n        this.styleRef = new Map();\n        this.hostNodes = new Set();\n        this.styleNodesInDOM = this.collectServerRenderedStyles();\n        this.platformIsServer = isPlatformServer(platformId);\n        this.resetHostNodes();\n    }\n    addStyles(styles) {\n        for (const style of styles) {\n            const usageCount = this.changeUsageCount(style, 1);\n            if (usageCount === 1) {\n                this.onStyleAdded(style);\n            }\n        }\n    }\n    removeStyles(styles) {\n        for (const style of styles) {\n            const usageCount = this.changeUsageCount(style, -1);\n            if (usageCount <= 0) {\n                this.onStyleRemoved(style);\n            }\n        }\n    }\n    ngOnDestroy() {\n        const styleNodesInDOM = this.styleNodesInDOM;\n        if (styleNodesInDOM) {\n            styleNodesInDOM.forEach((node) => node.remove());\n            styleNodesInDOM.clear();\n        }\n        for (const style of this.getAllStyles()) {\n            this.onStyleRemoved(style);\n        }\n        this.resetHostNodes();\n    }\n    addHost(hostNode) {\n        this.hostNodes.add(hostNode);\n        for (const style of this.getAllStyles()) {\n            this.addStyleToHost(hostNode, style);\n        }\n    }\n    removeHost(hostNode) {\n        this.hostNodes.delete(hostNode);\n    }\n    getAllStyles() {\n        return this.styleRef.keys();\n    }\n    onStyleAdded(style) {\n        for (const host of this.hostNodes) {\n            this.addStyleToHost(host, style);\n        }\n    }\n    onStyleRemoved(style) {\n        const styleRef = this.styleRef;\n        styleRef.get(style)?.elements?.forEach((node) => node.remove());\n        styleRef.delete(style);\n    }\n    collectServerRenderedStyles() {\n        const styles = this.doc.head?.querySelectorAll(`style[${APP_ID_ATTRIBUTE_NAME}=\"${this.appId}\"]`);\n        if (styles?.length) {\n            const styleMap = new Map();\n            styles.forEach((style) => {\n                if (style.textContent != null) {\n                    styleMap.set(style.textContent, style);\n                }\n            });\n            return styleMap;\n        }\n        return null;\n    }\n    changeUsageCount(style, delta) {\n        const map = this.styleRef;\n        if (map.has(style)) {\n            const styleRefValue = map.get(style);\n            styleRefValue.usage += delta;\n            return styleRefValue.usage;\n        }\n        map.set(style, { usage: delta, elements: [] });\n        return delta;\n    }\n    getStyleElement(host, style) {\n        const styleNodesInDOM = this.styleNodesInDOM;\n        const styleEl = styleNodesInDOM?.get(style);\n        if (styleEl?.parentNode === host) {\n            // `styleNodesInDOM` cannot be undefined due to the above `styleNodesInDOM?.get`.\n            styleNodesInDOM.delete(style);\n            styleEl.removeAttribute(APP_ID_ATTRIBUTE_NAME);\n            if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                // This attribute is solely used for debugging purposes.\n                styleEl.setAttribute('ng-style-reused', '');\n            }\n            return styleEl;\n        }\n        else {\n            const styleEl = this.doc.createElement('style');\n            if (this.nonce) {\n                styleEl.setAttribute('nonce', this.nonce);\n            }\n            styleEl.textContent = style;\n            if (this.platformIsServer) {\n                styleEl.setAttribute(APP_ID_ATTRIBUTE_NAME, this.appId);\n            }\n            host.appendChild(styleEl);\n            return styleEl;\n        }\n    }\n    addStyleToHost(host, style) {\n        const styleEl = this.getStyleElement(host, style);\n        const styleRef = this.styleRef;\n        const styleElRef = styleRef.get(style)?.elements;\n        if (styleElRef) {\n            styleElRef.push(styleEl);\n        }\n        else {\n            styleRef.set(style, { elements: [styleEl], usage: 1 });\n        }\n    }\n    resetHostNodes() {\n        const hostNodes = this.hostNodes;\n        hostNodes.clear();\n        // Re-add the head element back since this is the default host.\n        hostNodes.add(this.doc.head);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: SharedStylesHost, deps: [{ token: DOCUMENT }, { token: APP_ID }, { token: CSP_NONCE, optional: true }, { token: PLATFORM_ID }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: SharedStylesHost }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: SharedStylesHost, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [APP_ID]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CSP_NONCE]\n                }, {\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }] });\n\nconst NAMESPACE_URIS = {\n    'svg': 'http://www.w3.org/2000/svg',\n    'xhtml': 'http://www.w3.org/1999/xhtml',\n    'xlink': 'http://www.w3.org/1999/xlink',\n    'xml': 'http://www.w3.org/XML/1998/namespace',\n    'xmlns': 'http://www.w3.org/2000/xmlns/',\n    'math': 'http://www.w3.org/1998/Math/MathML',\n};\nconst COMPONENT_REGEX = /%COMP%/g;\nconst COMPONENT_VARIABLE = '%COMP%';\nconst HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nconst CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n/**\n * The default value for the `REMOVE_STYLES_ON_COMPONENT_DESTROY` DI token.\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT = true;\n/**\n * A DI token that indicates whether styles\n * of destroyed components should be removed from DOM.\n *\n * By default, the value is set to `true`.\n * @publicApi\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY = new InjectionToken(ngDevMode ? 'RemoveStylesOnCompDestroy' : '', {\n    providedIn: 'root',\n    factory: () => REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT,\n});\nfunction shimContentAttribute(componentShortId) {\n    return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimHostAttribute(componentShortId) {\n    return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\nfunction shimStylesContent(compId, styles) {\n    return styles.map((s) => s.replace(COMPONENT_REGEX, compId));\n}\nclass DomRendererFactory2 {\n    constructor(eventManager, sharedStylesHost, appId, removeStylesOnCompDestroy, doc, platformId, ngZone, nonce = null) {\n        this.eventManager = eventManager;\n        this.sharedStylesHost = sharedStylesHost;\n        this.appId = appId;\n        this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n        this.doc = doc;\n        this.platformId = platformId;\n        this.ngZone = ngZone;\n        this.nonce = nonce;\n        this.rendererByCompId = new Map();\n        this.platformIsServer = isPlatformServer(platformId);\n        this.defaultRenderer = new DefaultDomRenderer2(eventManager, doc, ngZone, this.platformIsServer);\n    }\n    createRenderer(element, type) {\n        if (!element || !type) {\n            return this.defaultRenderer;\n        }\n        if (this.platformIsServer && type.encapsulation === ViewEncapsulation.ShadowDom) {\n            // Domino does not support shadow DOM.\n            type = { ...type, encapsulation: ViewEncapsulation.Emulated };\n        }\n        const renderer = this.getOrCreateRenderer(element, type);\n        // Renderers have different logic due to different encapsulation behaviours.\n        // Ex: for emulated, an attribute is added to the element.\n        if (renderer instanceof EmulatedEncapsulationDomRenderer2) {\n            renderer.applyToHost(element);\n        }\n        else if (renderer instanceof NoneEncapsulationDomRenderer) {\n            renderer.applyStyles();\n        }\n        return renderer;\n    }\n    getOrCreateRenderer(element, type) {\n        const rendererByCompId = this.rendererByCompId;\n        let renderer = rendererByCompId.get(type.id);\n        if (!renderer) {\n            const doc = this.doc;\n            const ngZone = this.ngZone;\n            const eventManager = this.eventManager;\n            const sharedStylesHost = this.sharedStylesHost;\n            const removeStylesOnCompDestroy = this.removeStylesOnCompDestroy;\n            const platformIsServer = this.platformIsServer;\n            switch (type.encapsulation) {\n                case ViewEncapsulation.Emulated:\n                    renderer = new EmulatedEncapsulationDomRenderer2(eventManager, sharedStylesHost, type, this.appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer);\n                    break;\n                case ViewEncapsulation.ShadowDom:\n                    return new ShadowDomRenderer(eventManager, sharedStylesHost, element, type, doc, ngZone, this.nonce, platformIsServer);\n                default:\n                    renderer = new NoneEncapsulationDomRenderer(eventManager, sharedStylesHost, type, removeStylesOnCompDestroy, doc, ngZone, platformIsServer);\n                    break;\n            }\n            rendererByCompId.set(type.id, renderer);\n        }\n        return renderer;\n    }\n    ngOnDestroy() {\n        this.rendererByCompId.clear();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: DomRendererFactory2, deps: [{ token: EventManager }, { token: SharedStylesHost }, { token: APP_ID }, { token: REMOVE_STYLES_ON_COMPONENT_DESTROY }, { token: DOCUMENT }, { token: PLATFORM_ID }, { token: i0.NgZone }, { token: CSP_NONCE }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: DomRendererFactory2 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: DomRendererFactory2, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: EventManager }, { type: SharedStylesHost }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [APP_ID]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [REMOVE_STYLES_ON_COMPONENT_DESTROY]\n                }] }, { type: Document, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: Object, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [CSP_NONCE]\n                }] }] });\nclass DefaultDomRenderer2 {\n    constructor(eventManager, doc, ngZone, platformIsServer) {\n        this.eventManager = eventManager;\n        this.doc = doc;\n        this.ngZone = ngZone;\n        this.platformIsServer = platformIsServer;\n        this.data = Object.create(null);\n        /**\n         * By default this renderer throws when encountering synthetic properties\n         * This can be disabled for example by the AsyncAnimationRendererFactory\n         */\n        this.throwOnSyntheticProps = true;\n        this.destroyNode = null;\n    }\n    destroy() { }\n    createElement(name, namespace) {\n        if (namespace) {\n            // TODO: `|| namespace` was added in\n            // https://github.com/angular/angular/commit/2b9cc8503d48173492c29f5a271b61126104fbdb to\n            // support how Ivy passed around the namespace URI rather than short name at the time. It did\n            // not, however extend the support to other parts of the system (setAttribute, setAttribute,\n            // and the ServerRenderer). We should decide what exactly the semantics for dealing with\n            // namespaces should be and make it consistent.\n            // Related issues:\n            // https://github.com/angular/angular/issues/44028\n            // https://github.com/angular/angular/issues/44883\n            return this.doc.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n        }\n        return this.doc.createElement(name);\n    }\n    createComment(value) {\n        return this.doc.createComment(value);\n    }\n    createText(value) {\n        return this.doc.createTextNode(value);\n    }\n    appendChild(parent, newChild) {\n        const targetParent = isTemplateNode(parent) ? parent.content : parent;\n        targetParent.appendChild(newChild);\n    }\n    insertBefore(parent, newChild, refChild) {\n        if (parent) {\n            const targetParent = isTemplateNode(parent) ? parent.content : parent;\n            targetParent.insertBefore(newChild, refChild);\n        }\n    }\n    removeChild(_parent, oldChild) {\n        oldChild.remove();\n    }\n    selectRootElement(selectorOrNode, preserveContent) {\n        let el = typeof selectorOrNode === 'string' ? this.doc.querySelector(selectorOrNode) : selectorOrNode;\n        if (!el) {\n            throw new ɵRuntimeError(-5104 /* RuntimeErrorCode.ROOT_NODE_NOT_FOUND */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                `The selector \"${selectorOrNode}\" did not match any elements`);\n        }\n        if (!preserveContent) {\n            el.textContent = '';\n        }\n        return el;\n    }\n    parentNode(node) {\n        return node.parentNode;\n    }\n    nextSibling(node) {\n        return node.nextSibling;\n    }\n    setAttribute(el, name, value, namespace) {\n        if (namespace) {\n            name = namespace + ':' + name;\n            const namespaceUri = NAMESPACE_URIS[namespace];\n            if (namespaceUri) {\n                el.setAttributeNS(namespaceUri, name, value);\n            }\n            else {\n                el.setAttribute(name, value);\n            }\n        }\n        else {\n            el.setAttribute(name, value);\n        }\n    }\n    removeAttribute(el, name, namespace) {\n        if (namespace) {\n            const namespaceUri = NAMESPACE_URIS[namespace];\n            if (namespaceUri) {\n                el.removeAttributeNS(namespaceUri, name);\n            }\n            else {\n                el.removeAttribute(`${namespace}:${name}`);\n            }\n        }\n        else {\n            el.removeAttribute(name);\n        }\n    }\n    addClass(el, name) {\n        el.classList.add(name);\n    }\n    removeClass(el, name) {\n        el.classList.remove(name);\n    }\n    setStyle(el, style, value, flags) {\n        if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n            el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n        }\n        else {\n            el.style[style] = value;\n        }\n    }\n    removeStyle(el, style, flags) {\n        if (flags & RendererStyleFlags2.DashCase) {\n            // removeProperty has no effect when used on camelCased properties.\n            el.style.removeProperty(style);\n        }\n        else {\n            el.style[style] = '';\n        }\n    }\n    setProperty(el, name, value) {\n        if (el == null) {\n            return;\n        }\n        (typeof ngDevMode === 'undefined' || ngDevMode) &&\n            this.throwOnSyntheticProps &&\n            checkNoSyntheticProp(name, 'property');\n        el[name] = value;\n    }\n    setValue(node, value) {\n        node.nodeValue = value;\n    }\n    listen(target, event, callback) {\n        (typeof ngDevMode === 'undefined' || ngDevMode) &&\n            this.throwOnSyntheticProps &&\n            checkNoSyntheticProp(event, 'listener');\n        if (typeof target === 'string') {\n            target = ɵgetDOM().getGlobalEventTarget(this.doc, target);\n            if (!target) {\n                throw new Error(`Unsupported event target ${target} for event ${event}`);\n            }\n        }\n        return this.eventManager.addEventListener(target, event, this.decoratePreventDefault(callback));\n    }\n    decoratePreventDefault(eventHandler) {\n        // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n        // decoratePreventDefault or is a listener added outside the Angular context so it can handle\n        // the two differently. In the first case, the special '__ngUnwrap__' token is passed to the\n        // unwrap the listener (see below).\n        return (event) => {\n            // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n            // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The\n            // debug_node can inspect the listener toString contents for the existence of this special\n            // token. Because the token is a string literal, it is ensured to not be modified by compiled\n            // code.\n            if (event === '__ngUnwrap__') {\n                return eventHandler;\n            }\n            // Run the event handler inside the ngZone because event handlers are not patched\n            // by Zone on the server. This is required only for tests.\n            const allowDefaultBehavior = this.platformIsServer\n                ? this.ngZone.runGuarded(() => eventHandler(event))\n                : eventHandler(event);\n            if (allowDefaultBehavior === false) {\n                event.preventDefault();\n            }\n            return undefined;\n        };\n    }\n}\nconst AT_CHARCODE = (() => '@'.charCodeAt(0))();\nfunction checkNoSyntheticProp(name, nameKind) {\n    if (name.charCodeAt(0) === AT_CHARCODE) {\n        throw new ɵRuntimeError(5105 /* RuntimeErrorCode.UNEXPECTED_SYNTHETIC_PROPERTY */, `Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Either \\`BrowserAnimationsModule\\` or \\`NoopAnimationsModule\\` are imported in your application.\n  - There is corresponding configuration for the animation named \\`${name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.io/api/core/Component#animations).`);\n    }\n}\nfunction isTemplateNode(node) {\n    return node.tagName === 'TEMPLATE' && node.content !== undefined;\n}\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n    constructor(eventManager, sharedStylesHost, hostEl, component, doc, ngZone, nonce, platformIsServer) {\n        super(eventManager, doc, ngZone, platformIsServer);\n        this.sharedStylesHost = sharedStylesHost;\n        this.hostEl = hostEl;\n        this.shadowRoot = hostEl.attachShadow({ mode: 'open' });\n        this.sharedStylesHost.addHost(this.shadowRoot);\n        const styles = shimStylesContent(component.id, component.styles);\n        for (const style of styles) {\n            const styleEl = document.createElement('style');\n            if (nonce) {\n                styleEl.setAttribute('nonce', nonce);\n            }\n            styleEl.textContent = style;\n            this.shadowRoot.appendChild(styleEl);\n        }\n    }\n    nodeOrShadowRoot(node) {\n        return node === this.hostEl ? this.shadowRoot : node;\n    }\n    appendChild(parent, newChild) {\n        return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n    }\n    insertBefore(parent, newChild, refChild) {\n        return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n    }\n    removeChild(_parent, oldChild) {\n        return super.removeChild(null, oldChild);\n    }\n    parentNode(node) {\n        return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n    }\n    destroy() {\n        this.sharedStylesHost.removeHost(this.shadowRoot);\n    }\n}\nclass NoneEncapsulationDomRenderer extends DefaultDomRenderer2 {\n    constructor(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, compId) {\n        super(eventManager, doc, ngZone, platformIsServer);\n        this.sharedStylesHost = sharedStylesHost;\n        this.removeStylesOnCompDestroy = removeStylesOnCompDestroy;\n        this.styles = compId ? shimStylesContent(compId, component.styles) : component.styles;\n    }\n    applyStyles() {\n        this.sharedStylesHost.addStyles(this.styles);\n    }\n    destroy() {\n        if (!this.removeStylesOnCompDestroy) {\n            return;\n        }\n        this.sharedStylesHost.removeStyles(this.styles);\n    }\n}\nclass EmulatedEncapsulationDomRenderer2 extends NoneEncapsulationDomRenderer {\n    constructor(eventManager, sharedStylesHost, component, appId, removeStylesOnCompDestroy, doc, ngZone, platformIsServer) {\n        const compId = appId + '-' + component.id;\n        super(eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone, platformIsServer, compId);\n        this.contentAttr = shimContentAttribute(compId);\n        this.hostAttr = shimHostAttribute(compId);\n    }\n    applyToHost(element) {\n        this.applyStyles();\n        this.setAttribute(element, this.hostAttr, '');\n    }\n    createElement(parent, name) {\n        const el = super.createElement(parent, name);\n        super.setAttribute(el, this.contentAttr, '');\n        return el;\n    }\n}\n\nclass DomEventsPlugin extends EventManagerPlugin {\n    constructor(doc) {\n        super(doc);\n    }\n    // This plugin should come last in the list of plugins, because it accepts all\n    // events.\n    supports(eventName) {\n        return true;\n    }\n    addEventListener(element, eventName, handler) {\n        element.addEventListener(eventName, handler, false);\n        return () => this.removeEventListener(element, eventName, handler);\n    }\n    removeEventListener(target, eventName, callback) {\n        return target.removeEventListener(eventName, callback);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: DomEventsPlugin, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: DomEventsPlugin }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: DomEventsPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\n/**\n * Defines supported modifiers for key events.\n */\nconst MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift'];\n// The following values are here for cross-browser compatibility and to match the W3C standard\n// cf https://www.w3.org/TR/DOM-Level-3-Events-key/\nconst _keyMap = {\n    '\\b': 'Backspace',\n    '\\t': 'Tab',\n    '\\x7F': 'Delete',\n    '\\x1B': 'Escape',\n    'Del': 'Delete',\n    'Esc': 'Escape',\n    'Left': 'ArrowLeft',\n    'Right': 'ArrowRight',\n    'Up': 'ArrowUp',\n    'Down': 'ArrowDown',\n    'Menu': 'ContextMenu',\n    'Scroll': 'ScrollLock',\n    'Win': 'OS',\n};\n/**\n * Retrieves modifiers from key-event objects.\n */\nconst MODIFIER_KEY_GETTERS = {\n    'alt': (event) => event.altKey,\n    'control': (event) => event.ctrlKey,\n    'meta': (event) => event.metaKey,\n    'shift': (event) => event.shiftKey,\n};\n/**\n * A browser plug-in that provides support for handling of key events in Angular.\n */\nclass KeyEventsPlugin extends EventManagerPlugin {\n    /**\n     * Initializes an instance of the browser plug-in.\n     * @param doc The document in which key events will be detected.\n     */\n    constructor(doc) {\n        super(doc);\n    }\n    /**\n     * Reports whether a named key event is supported.\n     * @param eventName The event name to query.\n     * @return True if the named key event is supported.\n     */\n    supports(eventName) {\n        return KeyEventsPlugin.parseEventName(eventName) != null;\n    }\n    /**\n     * Registers a handler for a specific element and key event.\n     * @param element The HTML element to receive event notifications.\n     * @param eventName The name of the key event to listen for.\n     * @param handler A function to call when the notification occurs. Receives the\n     * event object as an argument.\n     * @returns The key event that was registered.\n     */\n    addEventListener(element, eventName, handler) {\n        const parsedEvent = KeyEventsPlugin.parseEventName(eventName);\n        const outsideHandler = KeyEventsPlugin.eventCallback(parsedEvent['fullKey'], handler, this.manager.getZone());\n        return this.manager.getZone().runOutsideAngular(() => {\n            return ɵgetDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler);\n        });\n    }\n    /**\n     * Parses the user provided full keyboard event definition and normalizes it for\n     * later internal use. It ensures the string is all lowercase, converts special\n     * characters to a standard spelling, and orders all the values consistently.\n     *\n     * @param eventName The name of the key event to listen for.\n     * @returns an object with the full, normalized string, and the dom event name\n     * or null in the case when the event doesn't match a keyboard event.\n     */\n    static parseEventName(eventName) {\n        const parts = eventName.toLowerCase().split('.');\n        const domEventName = parts.shift();\n        if (parts.length === 0 || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n            return null;\n        }\n        const key = KeyEventsPlugin._normalizeKey(parts.pop());\n        let fullKey = '';\n        let codeIX = parts.indexOf('code');\n        if (codeIX > -1) {\n            parts.splice(codeIX, 1);\n            fullKey = 'code.';\n        }\n        MODIFIER_KEYS.forEach((modifierName) => {\n            const index = parts.indexOf(modifierName);\n            if (index > -1) {\n                parts.splice(index, 1);\n                fullKey += modifierName + '.';\n            }\n        });\n        fullKey += key;\n        if (parts.length != 0 || key.length === 0) {\n            // returning null instead of throwing to let another plugin process the event\n            return null;\n        }\n        // NOTE: Please don't rewrite this as so, as it will break JSCompiler property renaming.\n        //       The code must remain in the `result['domEventName']` form.\n        // return {domEventName, fullKey};\n        const result = {};\n        result['domEventName'] = domEventName;\n        result['fullKey'] = fullKey;\n        return result;\n    }\n    /**\n     * Determines whether the actual keys pressed match the configured key code string.\n     * The `fullKeyCode` event is normalized in the `parseEventName` method when the\n     * event is attached to the DOM during the `addEventListener` call. This is unseen\n     * by the end user and is normalized for internal consistency and parsing.\n     *\n     * @param event The keyboard event.\n     * @param fullKeyCode The normalized user defined expected key event string\n     * @returns boolean.\n     */\n    static matchEventFullKeyCode(event, fullKeyCode) {\n        let keycode = _keyMap[event.key] || event.key;\n        let key = '';\n        if (fullKeyCode.indexOf('code.') > -1) {\n            keycode = event.code;\n            key = 'code.';\n        }\n        // the keycode could be unidentified so we have to check here\n        if (keycode == null || !keycode)\n            return false;\n        keycode = keycode.toLowerCase();\n        if (keycode === ' ') {\n            keycode = 'space'; // for readability\n        }\n        else if (keycode === '.') {\n            keycode = 'dot'; // because '.' is used as a separator in event names\n        }\n        MODIFIER_KEYS.forEach((modifierName) => {\n            if (modifierName !== keycode) {\n                const modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n                if (modifierGetter(event)) {\n                    key += modifierName + '.';\n                }\n            }\n        });\n        key += keycode;\n        return key === fullKeyCode;\n    }\n    /**\n     * Configures a handler callback for a key event.\n     * @param fullKey The event name that combines all simultaneous keystrokes.\n     * @param handler The function that responds to the key event.\n     * @param zone The zone in which the event occurred.\n     * @returns A callback function.\n     */\n    static eventCallback(fullKey, handler, zone) {\n        return (event) => {\n            if (KeyEventsPlugin.matchEventFullKeyCode(event, fullKey)) {\n                zone.runGuarded(() => handler(event));\n            }\n        };\n    }\n    /** @internal */\n    static _normalizeKey(keyName) {\n        return keyName === 'esc' ? 'escape' : keyName;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: KeyEventsPlugin, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: KeyEventsPlugin }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: KeyEventsPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\n/**\n * Bootstraps an instance of an Angular application and renders a standalone component as the\n * application's root component. More information about standalone components can be found in [this\n * guide](guide/components/importing).\n *\n * @usageNotes\n * The root component passed into this function *must* be a standalone one (should have the\n * `standalone: true` flag in the `@Component` decorator config).\n *\n * ```typescript\n * @Component({\n *   standalone: true,\n *   template: 'Hello world!'\n * })\n * class RootComponent {}\n *\n * const appRef: ApplicationRef = await bootstrapApplication(RootComponent);\n * ```\n *\n * You can add the list of providers that should be available in the application injector by\n * specifying the `providers` field in an object passed as the second argument:\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     {provide: BACKEND_URL, useValue: 'https://yourdomain.com/api'}\n *   ]\n * });\n * ```\n *\n * The `importProvidersFrom` helper method can be used to collect all providers from any\n * existing NgModule (and transitively from all NgModules that it imports):\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     importProvidersFrom(SomeNgModule)\n *   ]\n * });\n * ```\n *\n * Note: the `bootstrapApplication` method doesn't include [Testability](api/core/Testability) by\n * default. You can add [Testability](api/core/Testability) by getting the list of necessary\n * providers using `provideProtractorTestingSupport()` function and adding them into the `providers`\n * array, for example:\n *\n * ```typescript\n * import {provideProtractorTestingSupport} from '@angular/platform-browser';\n *\n * await bootstrapApplication(RootComponent, {providers: [provideProtractorTestingSupport()]});\n * ```\n *\n * @param rootComponent A reference to a standalone component that should be rendered.\n * @param options Extra configuration for the bootstrap operation, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction bootstrapApplication(rootComponent, options) {\n    return ɵinternalCreateApplication({ rootComponent, ...createProvidersConfig(options) });\n}\n/**\n * Create an instance of an Angular application without bootstrapping any components. This is useful\n * for the situation where one wants to decouple application environment creation (a platform and\n * associated injectors) from rendering components on a screen. Components can be subsequently\n * bootstrapped on the returned `ApplicationRef`.\n *\n * @param options Extra configuration for the application environment, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nfunction createApplication(options) {\n    return ɵinternalCreateApplication(createProvidersConfig(options));\n}\nfunction createProvidersConfig(options) {\n    return {\n        appProviders: [...BROWSER_MODULE_PROVIDERS, ...(options?.providers ?? [])],\n        platformProviders: INTERNAL_BROWSER_PLATFORM_PROVIDERS,\n    };\n}\n/**\n * Returns a set of providers required to setup [Testability](api/core/Testability) for an\n * application bootstrapped using the `bootstrapApplication` function. The set of providers is\n * needed to support testing an application with Protractor (which relies on the Testability APIs\n * to be present).\n *\n * @returns An array of providers required to setup Testability for an application and make it\n *     available for testing using Protractor.\n *\n * @publicApi\n */\nfunction provideProtractorTestingSupport() {\n    // Return a copy to prevent changes to the original array in case any in-place\n    // alterations are performed to the `provideProtractorTestingSupport` call results in app\n    // code.\n    return [...TESTABILITY_PROVIDERS];\n}\nfunction initDomAdapter() {\n    BrowserDomAdapter.makeCurrent();\n}\nfunction errorHandler() {\n    return new ErrorHandler();\n}\nfunction _document() {\n    // Tell ivy about the global document\n    ɵsetDocument(document);\n    return document;\n}\nconst INTERNAL_BROWSER_PLATFORM_PROVIDERS = [\n    { provide: PLATFORM_ID, useValue: ɵPLATFORM_BROWSER_ID },\n    { provide: PLATFORM_INITIALIZER, useValue: initDomAdapter, multi: true },\n    { provide: DOCUMENT, useFactory: _document, deps: [] },\n];\n/**\n * A factory function that returns a `PlatformRef` instance associated with browser service\n * providers.\n *\n * @publicApi\n */\nconst platformBrowser = createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\n/**\n * Internal marker to signal whether providers from the `BrowserModule` are already present in DI.\n * This is needed to avoid loading `BrowserModule` providers twice. We can't rely on the\n * `BrowserModule` presence itself, since the standalone-based bootstrap just imports\n * `BrowserModule` providers without referencing the module itself.\n */\nconst BROWSER_MODULE_PROVIDERS_MARKER = new InjectionToken(typeof ngDevMode === 'undefined' || ngDevMode ? 'BrowserModule Providers Marker' : '');\nconst TESTABILITY_PROVIDERS = [\n    {\n        provide: ɵTESTABILITY_GETTER,\n        useClass: BrowserGetTestability,\n        deps: [],\n    },\n    {\n        provide: ɵTESTABILITY,\n        useClass: Testability,\n        deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER],\n    },\n    {\n        provide: Testability, // Also provide as `Testability` for backwards-compatibility.\n        useClass: Testability,\n        deps: [NgZone, TestabilityRegistry, ɵTESTABILITY_GETTER],\n    },\n];\nconst BROWSER_MODULE_PROVIDERS = [\n    { provide: ɵINJECTOR_SCOPE, useValue: 'root' },\n    { provide: ErrorHandler, useFactory: errorHandler, deps: [] },\n    {\n        provide: EVENT_MANAGER_PLUGINS,\n        useClass: DomEventsPlugin,\n        multi: true,\n        deps: [DOCUMENT, NgZone, PLATFORM_ID],\n    },\n    { provide: EVENT_MANAGER_PLUGINS, useClass: KeyEventsPlugin, multi: true, deps: [DOCUMENT] },\n    DomRendererFactory2,\n    SharedStylesHost,\n    EventManager,\n    { provide: RendererFactory2, useExisting: DomRendererFactory2 },\n    { provide: XhrFactory, useClass: BrowserXhr, deps: [] },\n    typeof ngDevMode === 'undefined' || ngDevMode\n        ? { provide: BROWSER_MODULE_PROVIDERS_MARKER, useValue: true }\n        : [],\n];\n/**\n * Exports required infrastructure for all Angular apps.\n * Included by default in all Angular apps created with the CLI\n * `new` command.\n * Re-exports `CommonModule` and `ApplicationModule`, making their\n * exports and providers available to all apps.\n *\n * @publicApi\n */\nclass BrowserModule {\n    constructor(providersAlreadyPresent) {\n        if ((typeof ngDevMode === 'undefined' || ngDevMode) && providersAlreadyPresent) {\n            throw new ɵRuntimeError(5100 /* RuntimeErrorCode.BROWSER_MODULE_ALREADY_LOADED */, `Providers from the \\`BrowserModule\\` have already been loaded. If you need access ` +\n                `to common directives such as NgIf and NgFor, import the \\`CommonModule\\` instead.`);\n        }\n    }\n    /**\n     * Configures a browser-based app to transition from a server-rendered app, if\n     * one is present on the page.\n     *\n     * @param params An object containing an identifier for the app to transition.\n     * The ID must match between the client and server versions of the app.\n     * @returns The reconfigured `BrowserModule` to import into the app's root `AppModule`.\n     *\n     * @deprecated Use {@link APP_ID} instead to set the application ID.\n     */\n    static withServerTransition(params) {\n        return {\n            ngModule: BrowserModule,\n            providers: [{ provide: APP_ID, useValue: params.appId }],\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: BrowserModule, deps: [{ token: BROWSER_MODULE_PROVIDERS_MARKER, optional: true, skipSelf: true }], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.2.13\", ngImport: i0, type: BrowserModule, exports: [CommonModule, ApplicationModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: BrowserModule, providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS], imports: [CommonModule, ApplicationModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: BrowserModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n                    exports: [CommonModule, ApplicationModule],\n                }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }, {\n                    type: Inject,\n                    args: [BROWSER_MODULE_PROVIDERS_MARKER]\n                }] }] });\n\n/**\n * A service for managing HTML `<meta>` tags.\n *\n * Properties of the `MetaDefinition` object match the attributes of the\n * HTML `<meta>` tag. These tags define document metadata that is important for\n * things like configuring a Content Security Policy, defining browser compatibility\n * and security settings, setting HTTP Headers, defining rich content for social sharing,\n * and Search Engine Optimization (SEO).\n *\n * To identify specific `<meta>` tags in a document, use an attribute selection\n * string in the format `\"tag_attribute='value string'\"`.\n * For example, an `attrSelector` value of `\"name='description'\"` matches a tag\n * whose `name` attribute has the value `\"description\"`.\n * Selectors are used with the `querySelector()` Document method,\n * in the format `meta[{attrSelector}]`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see [Document.querySelector()](https://developer.mozilla.org/docs/Web/API/Document/querySelector)\n *\n *\n * @publicApi\n */\nclass Meta {\n    constructor(_doc) {\n        this._doc = _doc;\n        this._dom = ɵgetDOM();\n    }\n    /**\n     * Retrieves or creates a specific `<meta>` tag element in the current HTML document.\n     * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n     * values in the provided tag definition, and verifies that all other attribute values are equal.\n     * If an existing element is found, it is returned and is not modified in any way.\n     * @param tag The definition of a `<meta>` element to match or create.\n     * @param forceCreation True to create a new element without checking whether one already exists.\n     * @returns The existing element with the same attributes and values if found,\n     * the new element if no match is found, or `null` if the tag parameter is not defined.\n     */\n    addTag(tag, forceCreation = false) {\n        if (!tag)\n            return null;\n        return this._getOrCreateElement(tag, forceCreation);\n    }\n    /**\n     * Retrieves or creates a set of `<meta>` tag elements in the current HTML document.\n     * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n     * values in the provided tag definition, and verifies that all other attribute values are equal.\n     * @param tags An array of tag definitions to match or create.\n     * @param forceCreation True to create new elements without checking whether they already exist.\n     * @returns The matching elements if found, or the new elements.\n     */\n    addTags(tags, forceCreation = false) {\n        if (!tags)\n            return [];\n        return tags.reduce((result, tag) => {\n            if (tag) {\n                result.push(this._getOrCreateElement(tag, forceCreation));\n            }\n            return result;\n        }, []);\n    }\n    /**\n     * Retrieves a `<meta>` tag element in the current HTML document.\n     * @param attrSelector The tag attribute and value to match against, in the format\n     * `\"tag_attribute='value string'\"`.\n     * @returns The matching element, if any.\n     */\n    getTag(attrSelector) {\n        if (!attrSelector)\n            return null;\n        return this._doc.querySelector(`meta[${attrSelector}]`) || null;\n    }\n    /**\n     * Retrieves a set of `<meta>` tag elements in the current HTML document.\n     * @param attrSelector The tag attribute and value to match against, in the format\n     * `\"tag_attribute='value string'\"`.\n     * @returns The matching elements, if any.\n     */\n    getTags(attrSelector) {\n        if (!attrSelector)\n            return [];\n        const list /*NodeList*/ = this._doc.querySelectorAll(`meta[${attrSelector}]`);\n        return list ? [].slice.call(list) : [];\n    }\n    /**\n     * Modifies an existing `<meta>` tag element in the current HTML document.\n     * @param tag The tag description with which to replace the existing tag content.\n     * @param selector A tag attribute and value to match against, to identify\n     * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n     * If not supplied, matches a tag with the same `name` or `property` attribute value as the\n     * replacement tag.\n     * @return The modified element.\n     */\n    updateTag(tag, selector) {\n        if (!tag)\n            return null;\n        selector = selector || this._parseSelector(tag);\n        const meta = this.getTag(selector);\n        if (meta) {\n            return this._setMetaElementAttributes(tag, meta);\n        }\n        return this._getOrCreateElement(tag, true);\n    }\n    /**\n     * Removes an existing `<meta>` tag element from the current HTML document.\n     * @param attrSelector A tag attribute and value to match against, to identify\n     * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n     */\n    removeTag(attrSelector) {\n        this.removeTagElement(this.getTag(attrSelector));\n    }\n    /**\n     * Removes an existing `<meta>` tag element from the current HTML document.\n     * @param meta The tag definition to match against to identify an existing tag.\n     */\n    removeTagElement(meta) {\n        if (meta) {\n            this._dom.remove(meta);\n        }\n    }\n    _getOrCreateElement(meta, forceCreation = false) {\n        if (!forceCreation) {\n            const selector = this._parseSelector(meta);\n            // It's allowed to have multiple elements with the same name so it's not enough to\n            // just check that element with the same name already present on the page. We also need to\n            // check if element has tag attributes\n            const elem = this.getTags(selector).filter((elem) => this._containsAttributes(meta, elem))[0];\n            if (elem !== undefined)\n                return elem;\n        }\n        const element = this._dom.createElement('meta');\n        this._setMetaElementAttributes(meta, element);\n        const head = this._doc.getElementsByTagName('head')[0];\n        head.appendChild(element);\n        return element;\n    }\n    _setMetaElementAttributes(tag, el) {\n        Object.keys(tag).forEach((prop) => el.setAttribute(this._getMetaKeyMap(prop), tag[prop]));\n        return el;\n    }\n    _parseSelector(tag) {\n        const attr = tag.name ? 'name' : 'property';\n        return `${attr}=\"${tag[attr]}\"`;\n    }\n    _containsAttributes(tag, elem) {\n        return Object.keys(tag).every((key) => elem.getAttribute(this._getMetaKeyMap(key)) === tag[key]);\n    }\n    _getMetaKeyMap(prop) {\n        return META_KEYS_MAP[prop] || prop;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: Meta, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: Meta, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: Meta, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n/**\n * Mapping for MetaDefinition properties with their correct meta attribute names\n */\nconst META_KEYS_MAP = {\n    httpEquiv: 'http-equiv',\n};\n\n/**\n * A service that can be used to get and set the title of a current HTML document.\n *\n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n *\n * @publicApi\n */\nclass Title {\n    constructor(_doc) {\n        this._doc = _doc;\n    }\n    /**\n     * Get the title of the current HTML document.\n     */\n    getTitle() {\n        return this._doc.title;\n    }\n    /**\n     * Set the title of the current HTML document.\n     * @param newTitle\n     */\n    setTitle(newTitle) {\n        this._doc.title = newTitle || '';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: Title, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: Title, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: Title, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param value The value to export.\n */\nfunction exportNgVar(name, value) {\n    if (typeof COMPILED === 'undefined' || !COMPILED) {\n        // Note: we can't export `ng` when using closure enhanced optimization as:\n        // - closure declares globals itself for minified names, which sometimes clobber our `ng` global\n        // - we can't declare a closure extern as the namespace `ng` is already used within Google\n        //   for typings for angularJS (via `goog.provide('ng....')`).\n        const ng = (ɵglobal['ng'] = ɵglobal['ng'] || {});\n        ng[name] = value;\n    }\n}\n\nclass ChangeDetectionPerfRecord {\n    constructor(msPerTick, numTicks) {\n        this.msPerTick = msPerTick;\n        this.numTicks = numTicks;\n    }\n}\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\nclass AngularProfiler {\n    constructor(ref) {\n        this.appRef = ref.injector.get(ApplicationRef);\n    }\n    // tslint:disable:no-console\n    /**\n     * Exercises change detection in a loop and then prints the average amount of\n     * time in milliseconds how long a single round of change detection takes for\n     * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n     * of 500 milliseconds.\n     *\n     * Optionally, a user may pass a `config` parameter containing a map of\n     * options. Supported options are:\n     *\n     * `record` (boolean) - causes the profiler to record a CPU profile while\n     * it exercises the change detector. Example:\n     *\n     * ```\n     * ng.profiler.timeChangeDetection({record: true})\n     * ```\n     */\n    timeChangeDetection(config) {\n        const record = config && config['record'];\n        const profileName = 'Change Detection';\n        // Profiler is not available in Android browsers without dev tools opened\n        if (record && 'profile' in console && typeof console.profile === 'function') {\n            console.profile(profileName);\n        }\n        const start = performance.now();\n        let numTicks = 0;\n        while (numTicks < 5 || performance.now() - start < 500) {\n            this.appRef.tick();\n            numTicks++;\n        }\n        const end = performance.now();\n        if (record && 'profileEnd' in console && typeof console.profileEnd === 'function') {\n            console.profileEnd(profileName);\n        }\n        const msPerTick = (end - start) / numTicks;\n        console.log(`ran ${numTicks} change detection cycles`);\n        console.log(`${msPerTick.toFixed(2)} ms per check`);\n        return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n    }\n}\n\nconst PROFILER_GLOBAL_NAME = 'profiler';\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n *\n * Usage:\n *\n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n *\n * @publicApi\n */\nfunction enableDebugTools(ref) {\n    exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n    return ref;\n}\n/**\n * Disables Angular tools.\n *\n * @publicApi\n */\nfunction disableDebugTools() {\n    exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\n\n/**\n * Predicates for use with {@link DebugElement}'s query functions.\n *\n * @publicApi\n */\nclass By {\n    /**\n     * Match all nodes.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n     */\n    static all() {\n        return () => true;\n    }\n    /**\n     * Match elements by the given CSS selector.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n     */\n    static css(selector) {\n        return (debugElement) => {\n            return debugElement.nativeElement != null\n                ? elementMatches(debugElement.nativeElement, selector)\n                : false;\n        };\n    }\n    /**\n     * Match nodes that have the given directive present.\n     *\n     * @usageNotes\n     * ### Example\n     *\n     * {@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n     */\n    static directive(type) {\n        return (debugNode) => debugNode.providerTokens.indexOf(type) !== -1;\n    }\n}\nfunction elementMatches(n, selector) {\n    if (ɵgetDOM().isElementNode(n)) {\n        return ((n.matches && n.matches(selector)) ||\n            (n.msMatchesSelector && n.msMatchesSelector(selector)) ||\n            (n.webkitMatchesSelector && n.webkitMatchesSelector(selector)));\n    }\n    return false;\n}\n\n/**\n * Supported HammerJS recognizer event names.\n */\nconst EVENT_NAMES = {\n    // pan\n    'pan': true,\n    'panstart': true,\n    'panmove': true,\n    'panend': true,\n    'pancancel': true,\n    'panleft': true,\n    'panright': true,\n    'panup': true,\n    'pandown': true,\n    // pinch\n    'pinch': true,\n    'pinchstart': true,\n    'pinchmove': true,\n    'pinchend': true,\n    'pinchcancel': true,\n    'pinchin': true,\n    'pinchout': true,\n    // press\n    'press': true,\n    'pressup': true,\n    // rotate\n    'rotate': true,\n    'rotatestart': true,\n    'rotatemove': true,\n    'rotateend': true,\n    'rotatecancel': true,\n    // swipe\n    'swipe': true,\n    'swipeleft': true,\n    'swiperight': true,\n    'swipeup': true,\n    'swipedown': true,\n    // tap\n    'tap': true,\n    'doubletap': true,\n};\n/**\n * DI token for providing [HammerJS](https://hammerjs.github.io/) support to Angular.\n * @see {@link HammerGestureConfig}\n *\n * @ngModule HammerModule\n * @publicApi\n */\nconst HAMMER_GESTURE_CONFIG = new InjectionToken('HammerGestureConfig');\n/**\n * Injection token used to provide a HammerLoader to Angular.\n *\n * @see {@link HammerLoader}\n *\n * @publicApi\n */\nconst HAMMER_LOADER = new InjectionToken('HammerLoader');\n/**\n * An injectable [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n * for gesture recognition. Configures specific event recognition.\n * @publicApi\n */\nclass HammerGestureConfig {\n    constructor() {\n        /**\n         * A set of supported event names for gestures to be used in Angular.\n         * Angular supports all built-in recognizers, as listed in\n         * [HammerJS documentation](https://hammerjs.github.io/).\n         */\n        this.events = [];\n        /**\n         * Maps gesture event names to a set of configuration options\n         * that specify overrides to the default values for specific properties.\n         *\n         * The key is a supported event name to be configured,\n         * and the options object contains a set of properties, with override values\n         * to be applied to the named recognizer event.\n         * For example, to disable recognition of the rotate event, specify\n         *  `{\"rotate\": {\"enable\": false}}`.\n         *\n         * Properties that are not present take the HammerJS default values.\n         * For information about which properties are supported for which events,\n         * and their allowed and default values, see\n         * [HammerJS documentation](https://hammerjs.github.io/).\n         *\n         */\n        this.overrides = {};\n    }\n    /**\n     * Creates a [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n     * and attaches it to a given HTML element.\n     * @param element The element that will recognize gestures.\n     * @returns A HammerJS event-manager object.\n     */\n    buildHammer(element) {\n        const mc = new Hammer(element, this.options);\n        mc.get('pinch').set({ enable: true });\n        mc.get('rotate').set({ enable: true });\n        for (const eventName in this.overrides) {\n            mc.get(eventName).set(this.overrides[eventName]);\n        }\n        return mc;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HammerGestureConfig, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HammerGestureConfig }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HammerGestureConfig, decorators: [{\n            type: Injectable\n        }] });\n/**\n * Event plugin that adds Hammer support to an application.\n *\n * @ngModule HammerModule\n */\nclass HammerGesturesPlugin extends EventManagerPlugin {\n    constructor(doc, _config, console, loader) {\n        super(doc);\n        this._config = _config;\n        this.console = console;\n        this.loader = loader;\n        this._loaderPromise = null;\n    }\n    supports(eventName) {\n        if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n            return false;\n        }\n        if (!window.Hammer && !this.loader) {\n            if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                this.console.warn(`The \"${eventName}\" event cannot be bound because Hammer.JS is not ` +\n                    `loaded and no custom loader has been specified.`);\n            }\n            return false;\n        }\n        return true;\n    }\n    addEventListener(element, eventName, handler) {\n        const zone = this.manager.getZone();\n        eventName = eventName.toLowerCase();\n        // If Hammer is not present but a loader is specified, we defer adding the event listener\n        // until Hammer is loaded.\n        if (!window.Hammer && this.loader) {\n            this._loaderPromise = this._loaderPromise || zone.runOutsideAngular(() => this.loader());\n            // This `addEventListener` method returns a function to remove the added listener.\n            // Until Hammer is loaded, the returned function needs to *cancel* the registration rather\n            // than remove anything.\n            let cancelRegistration = false;\n            let deregister = () => {\n                cancelRegistration = true;\n            };\n            zone.runOutsideAngular(() => this._loaderPromise.then(() => {\n                // If Hammer isn't actually loaded when the custom loader resolves, give up.\n                if (!window.Hammer) {\n                    if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                        this.console.warn(`The custom HAMMER_LOADER completed, but Hammer.JS is not present.`);\n                    }\n                    deregister = () => { };\n                    return;\n                }\n                if (!cancelRegistration) {\n                    // Now that Hammer is loaded and the listener is being loaded for real,\n                    // the deregistration function changes from canceling registration to\n                    // removal.\n                    deregister = this.addEventListener(element, eventName, handler);\n                }\n            }).catch(() => {\n                if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                    this.console.warn(`The \"${eventName}\" event cannot be bound because the custom ` +\n                        `Hammer.JS loader failed.`);\n                }\n                deregister = () => { };\n            }));\n            // Return a function that *executes* `deregister` (and not `deregister` itself) so that we\n            // can change the behavior of `deregister` once the listener is added. Using a closure in\n            // this way allows us to avoid any additional data structures to track listener removal.\n            return () => {\n                deregister();\n            };\n        }\n        return zone.runOutsideAngular(() => {\n            // Creating the manager bind events, must be done outside of angular\n            const mc = this._config.buildHammer(element);\n            const callback = function (eventObj) {\n                zone.runGuarded(function () {\n                    handler(eventObj);\n                });\n            };\n            mc.on(eventName, callback);\n            return () => {\n                mc.off(eventName, callback);\n                // destroy mc to prevent memory leak\n                if (typeof mc.destroy === 'function') {\n                    mc.destroy();\n                }\n            };\n        });\n    }\n    isCustomEvent(eventName) {\n        return this._config.events.indexOf(eventName) > -1;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HammerGesturesPlugin, deps: [{ token: DOCUMENT }, { token: HAMMER_GESTURE_CONFIG }, { token: i0.ɵConsole }, { token: HAMMER_LOADER, optional: true }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HammerGesturesPlugin }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HammerGesturesPlugin, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: HammerGestureConfig, decorators: [{\n                    type: Inject,\n                    args: [HAMMER_GESTURE_CONFIG]\n                }] }, { type: i0.ɵConsole }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [HAMMER_LOADER]\n                }] }] });\n/**\n * Adds support for HammerJS.\n *\n * Import this module at the root of your application so that Angular can work with\n * HammerJS to detect gesture events.\n *\n * Note that applications still need to include the HammerJS script itself. This module\n * simply sets up the coordination layer between HammerJS and Angular's `EventManager`.\n *\n * @publicApi\n */\nclass HammerModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HammerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"18.2.13\", ngImport: i0, type: HammerModule }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HammerModule, providers: [\n            {\n                provide: EVENT_MANAGER_PLUGINS,\n                useClass: HammerGesturesPlugin,\n                multi: true,\n                deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]],\n            },\n            { provide: HAMMER_GESTURE_CONFIG, useClass: HammerGestureConfig, deps: [] },\n        ] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: HammerModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        {\n                            provide: EVENT_MANAGER_PLUGINS,\n                            useClass: HammerGesturesPlugin,\n                            multi: true,\n                            deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, ɵConsole, [new Optional(), HAMMER_LOADER]],\n                        },\n                        { provide: HAMMER_GESTURE_CONFIG, useClass: HammerGestureConfig, deps: [] },\n                    ],\n                }]\n        }] });\n\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n *\n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n *\n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n *\n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n *\n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n *\n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n *\n * @security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](https://g.co/ng/security).\n *\n * @publicApi\n */\nclass DomSanitizer {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: DomSanitizer, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: DomSanitizer, providedIn: 'root', useExisting: i0.forwardRef(() => DomSanitizerImpl) }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: DomSanitizer, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root', useExisting: forwardRef(() => DomSanitizerImpl) }]\n        }] });\nclass DomSanitizerImpl extends DomSanitizer {\n    constructor(_doc) {\n        super();\n        this._doc = _doc;\n    }\n    sanitize(ctx, value) {\n        if (value == null)\n            return null;\n        switch (ctx) {\n            case SecurityContext.NONE:\n                return value;\n            case SecurityContext.HTML:\n                if (ɵallowSanitizationBypassAndThrow(value, \"HTML\" /* BypassType.Html */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                return ɵ_sanitizeHtml(this._doc, String(value)).toString();\n            case SecurityContext.STYLE:\n                if (ɵallowSanitizationBypassAndThrow(value, \"Style\" /* BypassType.Style */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                return value;\n            case SecurityContext.SCRIPT:\n                if (ɵallowSanitizationBypassAndThrow(value, \"Script\" /* BypassType.Script */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                throw new ɵRuntimeError(5200 /* RuntimeErrorCode.SANITIZATION_UNSAFE_SCRIPT */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    'unsafe value used in a script context');\n            case SecurityContext.URL:\n                if (ɵallowSanitizationBypassAndThrow(value, \"URL\" /* BypassType.Url */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                return ɵ_sanitizeUrl(String(value));\n            case SecurityContext.RESOURCE_URL:\n                if (ɵallowSanitizationBypassAndThrow(value, \"ResourceURL\" /* BypassType.ResourceUrl */)) {\n                    return ɵunwrapSafeValue(value);\n                }\n                throw new ɵRuntimeError(5201 /* RuntimeErrorCode.SANITIZATION_UNSAFE_RESOURCE_URL */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    `unsafe value used in a resource URL context (see ${ɵXSS_SECURITY_URL})`);\n            default:\n                throw new ɵRuntimeError(5202 /* RuntimeErrorCode.SANITIZATION_UNEXPECTED_CTX */, (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                    `Unexpected SecurityContext ${ctx} (see ${ɵXSS_SECURITY_URL})`);\n        }\n    }\n    bypassSecurityTrustHtml(value) {\n        return ɵbypassSanitizationTrustHtml(value);\n    }\n    bypassSecurityTrustStyle(value) {\n        return ɵbypassSanitizationTrustStyle(value);\n    }\n    bypassSecurityTrustScript(value) {\n        return ɵbypassSanitizationTrustScript(value);\n    }\n    bypassSecurityTrustUrl(value) {\n        return ɵbypassSanitizationTrustUrl(value);\n    }\n    bypassSecurityTrustResourceUrl(value) {\n        return ɵbypassSanitizationTrustResourceUrl(value);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: DomSanitizerImpl, deps: [{ token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: DomSanitizerImpl, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"18.2.13\", ngImport: i0, type: DomSanitizerImpl, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }] });\n\n/**\n * The list of features as an enum to uniquely type each `HydrationFeature`.\n * @see {@link HydrationFeature}\n *\n * @publicApi\n */\nvar HydrationFeatureKind;\n(function (HydrationFeatureKind) {\n    HydrationFeatureKind[HydrationFeatureKind[\"NoHttpTransferCache\"] = 0] = \"NoHttpTransferCache\";\n    HydrationFeatureKind[HydrationFeatureKind[\"HttpTransferCacheOptions\"] = 1] = \"HttpTransferCacheOptions\";\n    HydrationFeatureKind[HydrationFeatureKind[\"I18nSupport\"] = 2] = \"I18nSupport\";\n    HydrationFeatureKind[HydrationFeatureKind[\"EventReplay\"] = 3] = \"EventReplay\";\n})(HydrationFeatureKind || (HydrationFeatureKind = {}));\n/**\n * Helper function to create an object that represents a Hydration feature.\n */\nfunction hydrationFeature(ɵkind, ɵproviders = [], ɵoptions = {}) {\n    return { ɵkind, ɵproviders };\n}\n/**\n * Disables HTTP transfer cache. Effectively causes HTTP requests to be performed twice: once on the\n * server and other one on the browser.\n *\n * @publicApi\n */\nfunction withNoHttpTransferCache() {\n    // This feature has no providers and acts as a flag that turns off\n    // HTTP transfer cache (which otherwise is turned on by default).\n    return hydrationFeature(HydrationFeatureKind.NoHttpTransferCache);\n}\n/**\n * The function accepts an object, which allows to configure cache parameters,\n * such as which headers should be included (no headers are included by default),\n * whether POST requests should be cached or a callback function to determine if a\n * particular request should be cached.\n *\n * @publicApi\n */\nfunction withHttpTransferCacheOptions(options) {\n    // This feature has no providers and acts as a flag to pass options to the HTTP transfer cache.\n    return hydrationFeature(HydrationFeatureKind.HttpTransferCacheOptions, ɵwithHttpTransferCache(options));\n}\n/**\n * Enables support for hydrating i18n blocks.\n *\n * @developerPreview\n * @publicApi\n */\nfunction withI18nSupport() {\n    return hydrationFeature(HydrationFeatureKind.I18nSupport, ɵwithI18nSupport());\n}\n/**\n * Enables support for replaying user events (e.g. `click`s) that happened on a page\n * before hydration logic has completed. Once an application is hydrated, all captured\n * events are replayed and relevant event listeners are executed.\n *\n * @usageNotes\n *\n * Basic example of how you can enable event replay in your application when\n * `bootstrapApplication` function is used:\n * ```\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration(withEventReplay())]\n * });\n * ```\n * @developerPreview\n * @publicApi\n * @see {@link provideClientHydration}\n */\nfunction withEventReplay() {\n    return hydrationFeature(HydrationFeatureKind.EventReplay, ɵwithEventReplay());\n}\n/**\n * Returns an `ENVIRONMENT_INITIALIZER` token setup with a function\n * that verifies whether compatible ZoneJS was used in an application\n * and logs a warning in a console if it's not the case.\n */\nfunction provideZoneJsCompatibilityDetector() {\n    return [\n        {\n            provide: ENVIRONMENT_INITIALIZER,\n            useValue: () => {\n                const ngZone = inject(NgZone);\n                const isZoneless = inject(ɵZONELESS_ENABLED);\n                // Checking `ngZone instanceof NgZone` would be insufficient here,\n                // because custom implementations might use NgZone as a base class.\n                if (!isZoneless && ngZone.constructor !== NgZone) {\n                    const console = inject(ɵConsole);\n                    const message = ɵformatRuntimeError(-5000 /* RuntimeErrorCode.UNSUPPORTED_ZONEJS_INSTANCE */, 'Angular detected that hydration was enabled for an application ' +\n                        'that uses a custom or a noop Zone.js implementation. ' +\n                        'This is not yet a fully supported configuration.');\n                    // tslint:disable-next-line:no-console\n                    console.warn(message);\n                }\n            },\n            multi: true,\n        },\n    ];\n}\n/**\n * Sets up providers necessary to enable hydration functionality for the application.\n *\n * By default, the function enables the recommended set of features for the optimal\n * performance for most of the applications. It includes the following features:\n *\n * * Reconciling DOM hydration. Learn more about it [here](guide/hydration).\n * * [`HttpClient`](api/common/http/HttpClient) response caching while running on the server and\n * transferring this cache to the client to avoid extra HTTP requests. Learn more about data caching\n * [here](guide/ssr#caching-data-when-using-httpclient).\n *\n * These functions allow you to disable some of the default features or enable new ones:\n *\n * * {@link withNoHttpTransferCache} to disable HTTP transfer cache\n * * {@link withHttpTransferCacheOptions} to configure some HTTP transfer cache options\n * * {@link withI18nSupport} to enable hydration support for i18n blocks\n * * {@link withEventReplay} to enable support for replaying user events\n *\n * @usageNotes\n *\n * Basic example of how you can enable hydration in your application when\n * `bootstrapApplication` function is used:\n * ```\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration()]\n * });\n * ```\n *\n * Alternatively if you are using NgModules, you would add `provideClientHydration`\n * to your root app module's provider list.\n * ```\n * @NgModule({\n *   declarations: [RootCmp],\n *   bootstrap: [RootCmp],\n *   providers: [provideClientHydration()],\n * })\n * export class AppModule {}\n * ```\n *\n * @see {@link withNoHttpTransferCache}\n * @see {@link withHttpTransferCacheOptions}\n * @see {@link withI18nSupport}\n * @see {@link withEventReplay}\n *\n * @param features Optional features to configure additional router behaviors.\n * @returns A set of providers to enable hydration.\n *\n * @publicApi\n */\nfunction provideClientHydration(...features) {\n    const providers = [];\n    const featuresKind = new Set();\n    const hasHttpTransferCacheOptions = featuresKind.has(HydrationFeatureKind.HttpTransferCacheOptions);\n    for (const { ɵproviders, ɵkind } of features) {\n        featuresKind.add(ɵkind);\n        if (ɵproviders.length) {\n            providers.push(ɵproviders);\n        }\n    }\n    if (typeof ngDevMode !== 'undefined' &&\n        ngDevMode &&\n        featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) &&\n        hasHttpTransferCacheOptions) {\n        // TODO: Make this a runtime error\n        throw new Error('Configuration error: found both withHttpTransferCacheOptions() and withNoHttpTransferCache() in the same call to provideClientHydration(), which is a contradiction.');\n    }\n    return makeEnvironmentProviders([\n        typeof ngDevMode !== 'undefined' && ngDevMode ? provideZoneJsCompatibilityDetector() : [],\n        ɵwithDomHydration(),\n        featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) || hasHttpTransferCacheOptions\n            ? []\n            : ɵwithHttpTransferCache({}),\n        providers,\n    ]);\n}\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser package.\n */\n/**\n * @publicApi\n */\nconst VERSION = new Version('18.2.13');\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { BrowserModule, By, DomSanitizer, EVENT_MANAGER_PLUGINS, EventManager, EventManagerPlugin, HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerModule, HydrationFeatureKind, Meta, REMOVE_STYLES_ON_COMPONENT_DESTROY, Title, VERSION, bootstrapApplication, createApplication, disableDebugTools, enableDebugTools, platformBrowser, provideClientHydration, provideProtractorTestingSupport, withEventReplay, withHttpTransferCacheOptions, withI18nSupport, withNoHttpTransferCache, BrowserDomAdapter as ɵBrowserDomAdapter, BrowserGetTestability as ɵBrowserGetTestability, DomEventsPlugin as ɵDomEventsPlugin, DomRendererFactory2 as ɵDomRendererFactory2, DomSanitizerImpl as ɵDomSanitizerImpl, HammerGesturesPlugin as ɵHammerGesturesPlugin, INTERNAL_BROWSER_PLATFORM_PROVIDERS as ɵINTERNAL_BROWSER_PLATFORM_PROVIDERS, KeyEventsPlugin as ɵKeyEventsPlugin, SharedStylesHost as ɵSharedStylesHost, initDomAdapter as ɵinitDomAdapter };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,SAASA,WAAW,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,OAAO,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,oBAAoB,EAAEC,UAAU,EAAEC,YAAY,QAAQ,iBAAiB;AACzK,SAASL,OAAO,QAAQ,iBAAiB;AACzC,OAAO,KAAKM,EAAE,MAAM,eAAe;AACnC,SAASC,OAAO,EAAEC,aAAa,EAAEC,UAAU,EAAEC,cAAc,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAEC,0BAA0B,EAAEC,YAAY,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,qBAAqB,EAAEC,YAAY,EAAEC,mBAAmB,EAAEC,YAAY,EAAEC,WAAW,EAAEC,MAAM,EAAEC,mBAAmB,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,iBAAiB,EAAEC,eAAe,EAAEC,gCAAgC,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,cAAc,EAAEC,4BAA4B,EAAEC,6BAA6B,EAAEC,8BAA8B,EAAEC,2BAA2B,EAAEC,mCAAmC,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAEC,wBAAwB,EAAEC,iBAAiB,EAAEC,OAAO,QAAQ,eAAe;AACr5B,SAASC,sBAAsB,QAAQ,sBAAsB;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,wBAAwB,SAAS7D,WAAW,CAAC;EAC/C8D,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGC,SAAS,CAAC;IACnB,IAAI,CAACC,iBAAiB,GAAG,IAAI;EACjC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,SAASJ,wBAAwB,CAAC;EACrD,OAAOK,WAAWA,CAAA,EAAG;IACjBjE,kBAAkB,CAAC,IAAIgE,iBAAiB,CAAC,CAAC,CAAC;EAC/C;EACAE,WAAWA,CAACC,EAAE,EAAEC,GAAG,EAAEC,QAAQ,EAAE;IAC3BF,EAAE,CAACG,gBAAgB,CAACF,GAAG,EAAEC,QAAQ,CAAC;IAClC,OAAO,MAAM;MACTF,EAAE,CAACI,mBAAmB,CAACH,GAAG,EAAEC,QAAQ,CAAC;IACzC,CAAC;EACL;EACAG,aAAaA,CAACL,EAAE,EAAEC,GAAG,EAAE;IACnBD,EAAE,CAACK,aAAa,CAACJ,GAAG,CAAC;EACzB;EACAK,MAAMA,CAACC,IAAI,EAAE;IACTA,IAAI,CAACD,MAAM,CAAC,CAAC;EACjB;EACAE,aAAaA,CAACC,OAAO,EAAEC,GAAG,EAAE;IACxBA,GAAG,GAAGA,GAAG,IAAI,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACtC,OAAOD,GAAG,CAACF,aAAa,CAACC,OAAO,CAAC;EACrC;EACAG,kBAAkBA,CAAA,EAAG;IACjB,OAAOC,QAAQ,CAACC,cAAc,CAACC,kBAAkB,CAAC,WAAW,CAAC;EAClE;EACAJ,kBAAkBA,CAAA,EAAG;IACjB,OAAOE,QAAQ;EACnB;EACAG,aAAaA,CAACT,IAAI,EAAE;IAChB,OAAOA,IAAI,CAACU,QAAQ,KAAKC,IAAI,CAACC,YAAY;EAC9C;EACAC,YAAYA,CAACb,IAAI,EAAE;IACf,OAAOA,IAAI,YAAYc,gBAAgB;EAC3C;EACA;EACAC,oBAAoBA,CAACZ,GAAG,EAAEa,MAAM,EAAE;IAC9B,IAAIA,MAAM,KAAK,QAAQ,EAAE;MACrB,OAAOC,MAAM;IACjB;IACA,IAAID,MAAM,KAAK,UAAU,EAAE;MACvB,OAAOb,GAAG;IACd;IACA,IAAIa,MAAM,KAAK,MAAM,EAAE;MACnB,OAAOb,GAAG,CAACe,IAAI;IACnB;IACA,OAAO,IAAI;EACf;EACAC,WAAWA,CAAChB,GAAG,EAAE;IACb,MAAMiB,IAAI,GAAGC,kBAAkB,CAAC,CAAC;IACjC,OAAOD,IAAI,IAAI,IAAI,GAAG,IAAI,GAAGE,YAAY,CAACF,IAAI,CAAC;EACnD;EACAG,gBAAgBA,CAAA,EAAG;IACfC,WAAW,GAAG,IAAI;EACtB;EACAC,YAAYA,CAAA,EAAG;IACX,OAAOR,MAAM,CAACS,SAAS,CAACC,SAAS;EACrC;EACAC,SAASA,CAACC,IAAI,EAAE;IACZ,OAAOtG,iBAAiB,CAAC+E,QAAQ,CAACwB,MAAM,EAAED,IAAI,CAAC;EACnD;AACJ;AACA,IAAIL,WAAW,GAAG,IAAI;AACtB,SAASH,kBAAkBA,CAAA,EAAG;EAC1BG,WAAW,GAAGA,WAAW,IAAIlB,QAAQ,CAACyB,aAAa,CAAC,MAAM,CAAC;EAC3D,OAAOP,WAAW,GAAGA,WAAW,CAACQ,YAAY,CAAC,MAAM,CAAC,GAAG,IAAI;AAChE;AACA,SAASV,YAAYA,CAACW,GAAG,EAAE;EACvB;EACA;EACA,OAAO,IAAIC,GAAG,CAACD,GAAG,EAAE3B,QAAQ,CAAC6B,OAAO,CAAC,CAACC,QAAQ;AAClD;AAEA,MAAMC,qBAAqB,CAAC;EACxBC,WAAWA,CAACC,QAAQ,EAAE;IAClBxG,OAAO,CAAC,uBAAuB,CAAC,GAAG,CAACyG,IAAI,EAAEC,eAAe,GAAG,IAAI,KAAK;MACjE,MAAMC,WAAW,GAAGH,QAAQ,CAACI,qBAAqB,CAACH,IAAI,EAAEC,eAAe,CAAC;MACzE,IAAIC,WAAW,IAAI,IAAI,EAAE;QACrB,MAAM,IAAI1G,aAAa,CAAC,IAAI,CAAC,8CAA8C,CAAC,OAAO4G,SAAS,KAAK,WAAW,IAAIA,SAAS,KACrH,yCAAyC,CAAC;MAClD;MACA,OAAOF,WAAW;IACtB,CAAC;IACD3G,OAAO,CAAC,4BAA4B,CAAC,GAAG,MAAMwG,QAAQ,CAACM,mBAAmB,CAAC,CAAC;IAC5E9G,OAAO,CAAC,2BAA2B,CAAC,GAAG,MAAMwG,QAAQ,CAACO,kBAAkB,CAAC,CAAC;IAC1E,MAAMC,aAAa,GAAIC,QAAQ,IAAK;MAChC,MAAMC,aAAa,GAAGlH,OAAO,CAAC,4BAA4B,CAAC,CAAC,CAAC;MAC7D,IAAImH,KAAK,GAAGD,aAAa,CAACE,MAAM;MAChC,MAAMC,SAAS,GAAG,SAAAA,CAAA,EAAY;QAC1BF,KAAK,EAAE;QACP,IAAIA,KAAK,IAAI,CAAC,EAAE;UACZF,QAAQ,CAAC,CAAC;QACd;MACJ,CAAC;MACDC,aAAa,CAACI,OAAO,CAAEX,WAAW,IAAK;QACnCA,WAAW,CAACY,UAAU,CAACF,SAAS,CAAC;MACrC,CAAC,CAAC;IACN,CAAC;IACD,IAAI,CAACrH,OAAO,CAAC,sBAAsB,CAAC,EAAE;MAClCA,OAAO,CAAC,sBAAsB,CAAC,GAAG,EAAE;IACxC;IACAA,OAAO,CAAC,sBAAsB,CAAC,CAACwH,IAAI,CAACR,aAAa,CAAC;EACvD;EACAJ,qBAAqBA,CAACJ,QAAQ,EAAEC,IAAI,EAAEC,eAAe,EAAE;IACnD,IAAID,IAAI,IAAI,IAAI,EAAE;MACd,OAAO,IAAI;IACf;IACA,MAAMgB,CAAC,GAAGjB,QAAQ,CAACkB,cAAc,CAACjB,IAAI,CAAC;IACvC,IAAIgB,CAAC,IAAI,IAAI,EAAE;MACX,OAAOA,CAAC;IACZ,CAAC,MACI,IAAI,CAACf,eAAe,EAAE;MACvB,OAAO,IAAI;IACf;IACA,IAAIjH,OAAO,CAAC,CAAC,CAACqF,YAAY,CAAC2B,IAAI,CAAC,EAAE;MAC9B,OAAO,IAAI,CAACG,qBAAqB,CAACJ,QAAQ,EAAEC,IAAI,CAACkB,IAAI,EAAE,IAAI,CAAC;IAChE;IACA,OAAO,IAAI,CAACf,qBAAqB,CAACJ,QAAQ,EAAEC,IAAI,CAACmB,aAAa,EAAE,IAAI,CAAC;EACzE;AACJ;;AAEA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACbC,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAIC,cAAc,CAAC,CAAC;EAC/B;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,mBAAAC,iBAAA;MAAA,YAAAA,iBAAA,IAAyFL,UAAU;IAAA,CAAoD;EAAE;EAC3K;IAAS,IAAI,CAACM,KAAK,kBAD8EpI,EAAE,CAAAqI,kBAAA;MAAAC,KAAA,EACYR,UAAU;MAAAS,OAAA,EAAVT,UAAU,CAAAG;IAAA,EAAG;EAAE;AAClI;AACA;EAAA,QAAAnB,SAAA,oBAAAA,SAAA,KAHqG9G,EAAE,CAAAwI,iBAAA,CAGXV,UAAU,EAAc,CAAC;IACzGW,IAAI,EAAEtI;EACV,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA,MAAMuI,qBAAqB,GAAG,IAAItI,cAAc,CAAC0G,SAAS,GAAG,qBAAqB,GAAG,EAAE,CAAC;AACxF;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6B,YAAY,CAAC;EACf;AACJ;AACA;EACItF,WAAWA,CAACuF,OAAO,EAAEC,KAAK,EAAE;IACxB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,kBAAkB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACnCH,OAAO,CAACrB,OAAO,CAAEyB,MAAM,IAAK;MACxBA,MAAM,CAACC,OAAO,GAAG,IAAI;IACzB,CAAC,CAAC;IACF,IAAI,CAACC,QAAQ,GAAGN,OAAO,CAACO,KAAK,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EAC7C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACItF,gBAAgBA,CAACuF,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAE;IAC1C,MAAMP,MAAM,GAAG,IAAI,CAACQ,cAAc,CAACF,SAAS,CAAC;IAC7C,OAAON,MAAM,CAAClF,gBAAgB,CAACuF,OAAO,EAAEC,SAAS,EAAEC,OAAO,CAAC;EAC/D;EACA;AACJ;AACA;EACIE,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACZ,KAAK;EACrB;EACA;EACAW,cAAcA,CAACF,SAAS,EAAE;IACtB,IAAIN,MAAM,GAAG,IAAI,CAACF,kBAAkB,CAACY,GAAG,CAACJ,SAAS,CAAC;IACnD,IAAIN,MAAM,EAAE;MACR,OAAOA,MAAM;IACjB;IACA,MAAMJ,OAAO,GAAG,IAAI,CAACM,QAAQ;IAC7BF,MAAM,GAAGJ,OAAO,CAACe,IAAI,CAAEX,MAAM,IAAKA,MAAM,CAACY,QAAQ,CAACN,SAAS,CAAC,CAAC;IAC7D,IAAI,CAACN,MAAM,EAAE;MACT,MAAM,IAAI9I,aAAa,CAAC,IAAI,CAAC,4CAA4C,CAAC,OAAO4G,SAAS,KAAK,WAAW,IAAIA,SAAS,KACnH,2CAA2CwC,SAAS,EAAE,CAAC;IAC/D;IACA,IAAI,CAACR,kBAAkB,CAACe,GAAG,CAACP,SAAS,EAAEN,MAAM,CAAC;IAC9C,OAAOA,MAAM;EACjB;EACA;IAAS,IAAI,CAACf,IAAI,YAAA6B,qBAAA3B,iBAAA;MAAA,YAAAA,iBAAA,IAAyFQ,YAAY,EAjEtB3I,EAAE,CAAA+J,QAAA,CAiEsCrB,qBAAqB,GAjE7D1I,EAAE,CAAA+J,QAAA,CAiEwE/J,EAAE,CAACqB,MAAM;IAAA,CAA6C;EAAE;EACnO;IAAS,IAAI,CAAC+G,KAAK,kBAlE8EpI,EAAE,CAAAqI,kBAAA;MAAAC,KAAA,EAkEYK,YAAY;MAAAJ,OAAA,EAAZI,YAAY,CAAAV;IAAA,EAAG;EAAE;AACpI;AACA;EAAA,QAAAnB,SAAA,oBAAAA,SAAA,KApEqG9G,EAAE,CAAAwI,iBAAA,CAoEXG,YAAY,EAAc,CAAC;IAC3GF,IAAI,EAAEtI;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEsI,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CxB,IAAI,EAAEpI,MAAM;MACZ6J,IAAI,EAAE,CAACxB,qBAAqB;IAChC,CAAC;EAAE,CAAC,EAAE;IAAED,IAAI,EAAEzI,EAAE,CAACqB;EAAO,CAAC,CAAC;AAAA;AAC1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8I,kBAAkB,CAAC;EACrB;EACA9G,WAAWA,CAAC+G,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;AACJ;;AAEA;AACA,MAAMC,qBAAqB,GAAG,WAAW;AACzC,MAAMC,gBAAgB,CAAC;EACnBjH,WAAWA,CAACgB,GAAG,EAAEkG,KAAK,EAAEC,KAAK,EAAEC,UAAU,GAAG,CAAC,CAAC,EAAE;IAC5C,IAAI,CAACpG,GAAG,GAAGA,GAAG;IACd,IAAI,CAACkG,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B;IACA,IAAI,CAACC,QAAQ,GAAG,IAAI3B,GAAG,CAAC,CAAC;IACzB,IAAI,CAAC4B,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,eAAe,GAAG,IAAI,CAACC,2BAA2B,CAAC,CAAC;IACzD,IAAI,CAACC,gBAAgB,GAAGpL,gBAAgB,CAAC8K,UAAU,CAAC;IACpD,IAAI,CAACO,cAAc,CAAC,CAAC;EACzB;EACAC,SAASA,CAACC,MAAM,EAAE;IACd,KAAK,MAAMC,KAAK,IAAID,MAAM,EAAE;MACxB,MAAME,UAAU,GAAG,IAAI,CAACC,gBAAgB,CAACF,KAAK,EAAE,CAAC,CAAC;MAClD,IAAIC,UAAU,KAAK,CAAC,EAAE;QAClB,IAAI,CAACE,YAAY,CAACH,KAAK,CAAC;MAC5B;IACJ;EACJ;EACAI,YAAYA,CAACL,MAAM,EAAE;IACjB,KAAK,MAAMC,KAAK,IAAID,MAAM,EAAE;MACxB,MAAME,UAAU,GAAG,IAAI,CAACC,gBAAgB,CAACF,KAAK,EAAE,CAAC,CAAC,CAAC;MACnD,IAAIC,UAAU,IAAI,CAAC,EAAE;QACjB,IAAI,CAACI,cAAc,CAACL,KAAK,CAAC;MAC9B;IACJ;EACJ;EACAM,WAAWA,CAAA,EAAG;IACV,MAAMZ,eAAe,GAAG,IAAI,CAACA,eAAe;IAC5C,IAAIA,eAAe,EAAE;MACjBA,eAAe,CAACtD,OAAO,CAAErD,IAAI,IAAKA,IAAI,CAACD,MAAM,CAAC,CAAC,CAAC;MAChD4G,eAAe,CAACa,KAAK,CAAC,CAAC;IAC3B;IACA,KAAK,MAAMP,KAAK,IAAI,IAAI,CAACQ,YAAY,CAAC,CAAC,EAAE;MACrC,IAAI,CAACH,cAAc,CAACL,KAAK,CAAC;IAC9B;IACA,IAAI,CAACH,cAAc,CAAC,CAAC;EACzB;EACAY,OAAOA,CAACC,QAAQ,EAAE;IACd,IAAI,CAAClB,SAAS,CAACmB,GAAG,CAACD,QAAQ,CAAC;IAC5B,KAAK,MAAMV,KAAK,IAAI,IAAI,CAACQ,YAAY,CAAC,CAAC,EAAE;MACrC,IAAI,CAACI,cAAc,CAACF,QAAQ,EAAEV,KAAK,CAAC;IACxC;EACJ;EACAa,UAAUA,CAACH,QAAQ,EAAE;IACjB,IAAI,CAAClB,SAAS,CAACsB,MAAM,CAACJ,QAAQ,CAAC;EACnC;EACAF,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACjB,QAAQ,CAACwB,IAAI,CAAC,CAAC;EAC/B;EACAZ,YAAYA,CAACH,KAAK,EAAE;IAChB,KAAK,MAAMvD,IAAI,IAAI,IAAI,CAAC+C,SAAS,EAAE;MAC/B,IAAI,CAACoB,cAAc,CAACnE,IAAI,EAAEuD,KAAK,CAAC;IACpC;EACJ;EACAK,cAAcA,CAACL,KAAK,EAAE;IAClB,MAAMT,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9BA,QAAQ,CAAChB,GAAG,CAACyB,KAAK,CAAC,EAAEgB,QAAQ,EAAE5E,OAAO,CAAErD,IAAI,IAAKA,IAAI,CAACD,MAAM,CAAC,CAAC,CAAC;IAC/DyG,QAAQ,CAACuB,MAAM,CAACd,KAAK,CAAC;EAC1B;EACAL,2BAA2BA,CAAA,EAAG;IAC1B,MAAMI,MAAM,GAAG,IAAI,CAAC7G,GAAG,CAAC+H,IAAI,EAAEC,gBAAgB,CAAC,SAAShC,qBAAqB,KAAK,IAAI,CAACE,KAAK,IAAI,CAAC;IACjG,IAAIW,MAAM,EAAE7D,MAAM,EAAE;MAChB,MAAMiF,QAAQ,GAAG,IAAIvD,GAAG,CAAC,CAAC;MAC1BmC,MAAM,CAAC3D,OAAO,CAAE4D,KAAK,IAAK;QACtB,IAAIA,KAAK,CAACoB,WAAW,IAAI,IAAI,EAAE;UAC3BD,QAAQ,CAACzC,GAAG,CAACsB,KAAK,CAACoB,WAAW,EAAEpB,KAAK,CAAC;QAC1C;MACJ,CAAC,CAAC;MACF,OAAOmB,QAAQ;IACnB;IACA,OAAO,IAAI;EACf;EACAjB,gBAAgBA,CAACF,KAAK,EAAEqB,KAAK,EAAE;IAC3B,MAAMC,GAAG,GAAG,IAAI,CAAC/B,QAAQ;IACzB,IAAI+B,GAAG,CAACC,GAAG,CAACvB,KAAK,CAAC,EAAE;MAChB,MAAMwB,aAAa,GAAGF,GAAG,CAAC/C,GAAG,CAACyB,KAAK,CAAC;MACpCwB,aAAa,CAACC,KAAK,IAAIJ,KAAK;MAC5B,OAAOG,aAAa,CAACC,KAAK;IAC9B;IACAH,GAAG,CAAC5C,GAAG,CAACsB,KAAK,EAAE;MAAEyB,KAAK,EAAEJ,KAAK;MAAEL,QAAQ,EAAE;IAAG,CAAC,CAAC;IAC9C,OAAOK,KAAK;EAChB;EACAK,eAAeA,CAACjF,IAAI,EAAEuD,KAAK,EAAE;IACzB,MAAMN,eAAe,GAAG,IAAI,CAACA,eAAe;IAC5C,MAAMiC,OAAO,GAAGjC,eAAe,EAAEnB,GAAG,CAACyB,KAAK,CAAC;IAC3C,IAAI2B,OAAO,EAAEC,UAAU,KAAKnF,IAAI,EAAE;MAC9B;MACAiD,eAAe,CAACoB,MAAM,CAACd,KAAK,CAAC;MAC7B2B,OAAO,CAACE,eAAe,CAAC3C,qBAAqB,CAAC;MAC9C,IAAI,OAAOvD,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;QAC/C;QACAgG,OAAO,CAACG,YAAY,CAAC,iBAAiB,EAAE,EAAE,CAAC;MAC/C;MACA,OAAOH,OAAO;IAClB,CAAC,MACI;MACD,MAAMA,OAAO,GAAG,IAAI,CAACzI,GAAG,CAACF,aAAa,CAAC,OAAO,CAAC;MAC/C,IAAI,IAAI,CAACqG,KAAK,EAAE;QACZsC,OAAO,CAACG,YAAY,CAAC,OAAO,EAAE,IAAI,CAACzC,KAAK,CAAC;MAC7C;MACAsC,OAAO,CAACP,WAAW,GAAGpB,KAAK;MAC3B,IAAI,IAAI,CAACJ,gBAAgB,EAAE;QACvB+B,OAAO,CAACG,YAAY,CAAC5C,qBAAqB,EAAE,IAAI,CAACE,KAAK,CAAC;MAC3D;MACA3C,IAAI,CAACsF,WAAW,CAACJ,OAAO,CAAC;MACzB,OAAOA,OAAO;IAClB;EACJ;EACAf,cAAcA,CAACnE,IAAI,EAAEuD,KAAK,EAAE;IACxB,MAAM2B,OAAO,GAAG,IAAI,CAACD,eAAe,CAACjF,IAAI,EAAEuD,KAAK,CAAC;IACjD,MAAMT,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,MAAMyC,UAAU,GAAGzC,QAAQ,CAAChB,GAAG,CAACyB,KAAK,CAAC,EAAEgB,QAAQ;IAChD,IAAIgB,UAAU,EAAE;MACZA,UAAU,CAAC1F,IAAI,CAACqF,OAAO,CAAC;IAC5B,CAAC,MACI;MACDpC,QAAQ,CAACb,GAAG,CAACsB,KAAK,EAAE;QAAEgB,QAAQ,EAAE,CAACW,OAAO,CAAC;QAAEF,KAAK,EAAE;MAAE,CAAC,CAAC;IAC1D;EACJ;EACA5B,cAAcA,CAAA,EAAG;IACb,MAAML,SAAS,GAAG,IAAI,CAACA,SAAS;IAChCA,SAAS,CAACe,KAAK,CAAC,CAAC;IACjB;IACAf,SAAS,CAACmB,GAAG,CAAC,IAAI,CAACzH,GAAG,CAAC+H,IAAI,CAAC;EAChC;EACA;IAAS,IAAI,CAACnE,IAAI,YAAAmF,yBAAAjF,iBAAA;MAAA,YAAAA,iBAAA,IAAyFmC,gBAAgB,EA3N1BtK,EAAE,CAAA+J,QAAA,CA2N0CnK,QAAQ,GA3NpDI,EAAE,CAAA+J,QAAA,CA2N+DzJ,MAAM,GA3NvEN,EAAE,CAAA+J,QAAA,CA2NkFxJ,SAAS,MA3N7FP,EAAE,CAAA+J,QAAA,CA2NwHvJ,WAAW;IAAA,CAA6C;EAAE;EACrR;IAAS,IAAI,CAAC4H,KAAK,kBA5N8EpI,EAAE,CAAAqI,kBAAA;MAAAC,KAAA,EA4NYgC,gBAAgB;MAAA/B,OAAA,EAAhB+B,gBAAgB,CAAArC;IAAA,EAAG;EAAE;AACxI;AACA;EAAA,QAAAnB,SAAA,oBAAAA,SAAA,KA9NqG9G,EAAE,CAAAwI,iBAAA,CA8NX8B,gBAAgB,EAAc,CAAC;IAC/G7B,IAAI,EAAEtI;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEsI,IAAI,EAAE4E,QAAQ;IAAEpD,UAAU,EAAE,CAAC;MAC9CxB,IAAI,EAAEpI,MAAM;MACZ6J,IAAI,EAAE,CAACtK,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAE6I,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCxB,IAAI,EAAEpI,MAAM;MACZ6J,IAAI,EAAE,CAAC5J,MAAM;IACjB,CAAC;EAAE,CAAC,EAAE;IAAEmI,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCxB,IAAI,EAAEpI,MAAM;MACZ6J,IAAI,EAAE,CAAC3J,SAAS;IACpB,CAAC,EAAE;MACCkI,IAAI,EAAEhI;IACV,CAAC;EAAE,CAAC,EAAE;IAAEgI,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCxB,IAAI,EAAEpI,MAAM;MACZ6J,IAAI,EAAE,CAAC1J,WAAW;IACtB,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAM8M,cAAc,GAAG;EACnB,KAAK,EAAE,4BAA4B;EACnC,OAAO,EAAE,8BAA8B;EACvC,OAAO,EAAE,8BAA8B;EACvC,KAAK,EAAE,sCAAsC;EAC7C,OAAO,EAAE,+BAA+B;EACxC,MAAM,EAAE;AACZ,CAAC;AACD,MAAMC,eAAe,GAAG,SAAS;AACjC,MAAMC,kBAAkB,GAAG,QAAQ;AACnC,MAAMC,SAAS,GAAG,WAAWD,kBAAkB,EAAE;AACjD,MAAME,YAAY,GAAG,cAAcF,kBAAkB,EAAE;AACvD;AACA;AACA;AACA,MAAMG,0CAA0C,GAAG,IAAI;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kCAAkC,GAAG,IAAIxN,cAAc,CAAC0G,SAAS,GAAG,2BAA2B,GAAG,EAAE,EAAE;EACxG+G,UAAU,EAAE,MAAM;EAClBtF,OAAO,EAAEA,CAAA,KAAMoF;AACnB,CAAC,CAAC;AACF,SAASG,oBAAoBA,CAACC,gBAAgB,EAAE;EAC5C,OAAOL,YAAY,CAACM,OAAO,CAACT,eAAe,EAAEQ,gBAAgB,CAAC;AAClE;AACA,SAASE,iBAAiBA,CAACF,gBAAgB,EAAE;EACzC,OAAON,SAAS,CAACO,OAAO,CAACT,eAAe,EAAEQ,gBAAgB,CAAC;AAC/D;AACA,SAASG,iBAAiBA,CAACC,MAAM,EAAEjD,MAAM,EAAE;EACvC,OAAOA,MAAM,CAACuB,GAAG,CAAE2B,CAAC,IAAKA,CAAC,CAACJ,OAAO,CAACT,eAAe,EAAEY,MAAM,CAAC,CAAC;AAChE;AACA,MAAME,mBAAmB,CAAC;EACtBhL,WAAWA,CAACiL,YAAY,EAAEC,gBAAgB,EAAEhE,KAAK,EAAEiE,yBAAyB,EAAEnK,GAAG,EAAEoG,UAAU,EAAEgE,MAAM,EAAEjE,KAAK,GAAG,IAAI,EAAE;IACjH,IAAI,CAAC8D,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAAChE,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACiE,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACnK,GAAG,GAAGA,GAAG;IACd,IAAI,CAACoG,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACgE,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACjE,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACkE,gBAAgB,GAAG,IAAI3F,GAAG,CAAC,CAAC;IACjC,IAAI,CAACgC,gBAAgB,GAAGpL,gBAAgB,CAAC8K,UAAU,CAAC;IACpD,IAAI,CAACkE,eAAe,GAAG,IAAIC,mBAAmB,CAACN,YAAY,EAAEjK,GAAG,EAAEoK,MAAM,EAAE,IAAI,CAAC1D,gBAAgB,CAAC;EACpG;EACA8D,cAAcA,CAACxF,OAAO,EAAEZ,IAAI,EAAE;IAC1B,IAAI,CAACY,OAAO,IAAI,CAACZ,IAAI,EAAE;MACnB,OAAO,IAAI,CAACkG,eAAe;IAC/B;IACA,IAAI,IAAI,CAAC5D,gBAAgB,IAAItC,IAAI,CAACqG,aAAa,KAAKpO,iBAAiB,CAACqO,SAAS,EAAE;MAC7E;MACAtG,IAAI,GAAG;QAAE,GAAGA,IAAI;QAAEqG,aAAa,EAAEpO,iBAAiB,CAACsO;MAAS,CAAC;IACjE;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACC,mBAAmB,CAAC7F,OAAO,EAAEZ,IAAI,CAAC;IACxD;IACA;IACA,IAAIwG,QAAQ,YAAYE,iCAAiC,EAAE;MACvDF,QAAQ,CAACG,WAAW,CAAC/F,OAAO,CAAC;IACjC,CAAC,MACI,IAAI4F,QAAQ,YAAYI,4BAA4B,EAAE;MACvDJ,QAAQ,CAACK,WAAW,CAAC,CAAC;IAC1B;IACA,OAAOL,QAAQ;EACnB;EACAC,mBAAmBA,CAAC7F,OAAO,EAAEZ,IAAI,EAAE;IAC/B,MAAMiG,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAC9C,IAAIO,QAAQ,GAAGP,gBAAgB,CAAChF,GAAG,CAACjB,IAAI,CAAC8G,EAAE,CAAC;IAC5C,IAAI,CAACN,QAAQ,EAAE;MACX,MAAM5K,GAAG,GAAG,IAAI,CAACA,GAAG;MACpB,MAAMoK,MAAM,GAAG,IAAI,CAACA,MAAM;MAC1B,MAAMH,YAAY,GAAG,IAAI,CAACA,YAAY;MACtC,MAAMC,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;MAC9C,MAAMC,yBAAyB,GAAG,IAAI,CAACA,yBAAyB;MAChE,MAAMzD,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;MAC9C,QAAQtC,IAAI,CAACqG,aAAa;QACtB,KAAKpO,iBAAiB,CAACsO,QAAQ;UAC3BC,QAAQ,GAAG,IAAIE,iCAAiC,CAACb,YAAY,EAAEC,gBAAgB,EAAE9F,IAAI,EAAE,IAAI,CAAC8B,KAAK,EAAEiE,yBAAyB,EAAEnK,GAAG,EAAEoK,MAAM,EAAE1D,gBAAgB,CAAC;UAC5J;QACJ,KAAKrK,iBAAiB,CAACqO,SAAS;UAC5B,OAAO,IAAIS,iBAAiB,CAAClB,YAAY,EAAEC,gBAAgB,EAAElF,OAAO,EAAEZ,IAAI,EAAEpE,GAAG,EAAEoK,MAAM,EAAE,IAAI,CAACjE,KAAK,EAAEO,gBAAgB,CAAC;QAC1H;UACIkE,QAAQ,GAAG,IAAII,4BAA4B,CAACf,YAAY,EAAEC,gBAAgB,EAAE9F,IAAI,EAAE+F,yBAAyB,EAAEnK,GAAG,EAAEoK,MAAM,EAAE1D,gBAAgB,CAAC;UAC3I;MACR;MACA2D,gBAAgB,CAAC7E,GAAG,CAACpB,IAAI,CAAC8G,EAAE,EAAEN,QAAQ,CAAC;IAC3C;IACA,OAAOA,QAAQ;EACnB;EACAxD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACiD,gBAAgB,CAAChD,KAAK,CAAC,CAAC;EACjC;EACA;IAAS,IAAI,CAACzD,IAAI,YAAAwH,4BAAAtH,iBAAA;MAAA,YAAAA,iBAAA,IAAyFkG,mBAAmB,EAhV7BrO,EAAE,CAAA+J,QAAA,CAgV6CpB,YAAY,GAhV3D3I,EAAE,CAAA+J,QAAA,CAgVsEO,gBAAgB,GAhVxFtK,EAAE,CAAA+J,QAAA,CAgVmGzJ,MAAM,GAhV3GN,EAAE,CAAA+J,QAAA,CAgVsH6D,kCAAkC,GAhV1J5N,EAAE,CAAA+J,QAAA,CAgVqKnK,QAAQ,GAhV/KI,EAAE,CAAA+J,QAAA,CAgV0LvJ,WAAW,GAhVvMR,EAAE,CAAA+J,QAAA,CAgVkN/J,EAAE,CAACqB,MAAM,GAhV7NrB,EAAE,CAAA+J,QAAA,CAgVwOxJ,SAAS;IAAA,CAA6C;EAAE;EACnY;IAAS,IAAI,CAAC6H,KAAK,kBAjV8EpI,EAAE,CAAAqI,kBAAA;MAAAC,KAAA,EAiVY+F,mBAAmB;MAAA9F,OAAA,EAAnB8F,mBAAmB,CAAApG;IAAA,EAAG;EAAE;AAC3I;AACA;EAAA,QAAAnB,SAAA,oBAAAA,SAAA,KAnVqG9G,EAAE,CAAAwI,iBAAA,CAmVX6F,mBAAmB,EAAc,CAAC;IAClH5F,IAAI,EAAEtI;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEsI,IAAI,EAAEE;EAAa,CAAC,EAAE;IAAEF,IAAI,EAAE6B;EAAiB,CAAC,EAAE;IAAE7B,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MACnGxB,IAAI,EAAEpI,MAAM;MACZ6J,IAAI,EAAE,CAAC5J,MAAM;IACjB,CAAC;EAAE,CAAC,EAAE;IAAEmI,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCxB,IAAI,EAAEpI,MAAM;MACZ6J,IAAI,EAAE,CAAC0D,kCAAkC;IAC7C,CAAC;EAAE,CAAC,EAAE;IAAEnF,IAAI,EAAE4E,QAAQ;IAAEpD,UAAU,EAAE,CAAC;MACjCxB,IAAI,EAAEpI,MAAM;MACZ6J,IAAI,EAAE,CAACtK,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAE6I,IAAI,EAAEiH,MAAM;IAAEzF,UAAU,EAAE,CAAC;MAC/BxB,IAAI,EAAEpI,MAAM;MACZ6J,IAAI,EAAE,CAAC1J,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAEiI,IAAI,EAAEzI,EAAE,CAACqB;EAAO,CAAC,EAAE;IAAEoH,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MACvDxB,IAAI,EAAEpI,MAAM;MACZ6J,IAAI,EAAE,CAAC3J,SAAS;IACpB,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB,MAAMqO,mBAAmB,CAAC;EACtBvL,WAAWA,CAACiL,YAAY,EAAEjK,GAAG,EAAEoK,MAAM,EAAE1D,gBAAgB,EAAE;IACrD,IAAI,CAACuD,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACjK,GAAG,GAAGA,GAAG;IACd,IAAI,CAACoK,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC1D,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAAC4E,IAAI,GAAGD,MAAM,CAACE,MAAM,CAAC,IAAI,CAAC;IAC/B;AACR;AACA;AACA;IACQ,IAAI,CAACC,qBAAqB,GAAG,IAAI;IACjC,IAAI,CAACC,WAAW,GAAG,IAAI;EAC3B;EACAC,OAAOA,CAAA,EAAG,CAAE;EACZ5L,aAAaA,CAAC4B,IAAI,EAAEiK,SAAS,EAAE;IAC3B,IAAIA,SAAS,EAAE;MACX;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,OAAO,IAAI,CAAC3L,GAAG,CAAC4L,eAAe,CAAC3C,cAAc,CAAC0C,SAAS,CAAC,IAAIA,SAAS,EAAEjK,IAAI,CAAC;IACjF;IACA,OAAO,IAAI,CAAC1B,GAAG,CAACF,aAAa,CAAC4B,IAAI,CAAC;EACvC;EACAmK,aAAaA,CAACC,KAAK,EAAE;IACjB,OAAO,IAAI,CAAC9L,GAAG,CAAC6L,aAAa,CAACC,KAAK,CAAC;EACxC;EACAC,UAAUA,CAACD,KAAK,EAAE;IACd,OAAO,IAAI,CAAC9L,GAAG,CAACgM,cAAc,CAACF,KAAK,CAAC;EACzC;EACAjD,WAAWA,CAACoD,MAAM,EAAEC,QAAQ,EAAE;IAC1B,MAAMC,YAAY,GAAGC,cAAc,CAACH,MAAM,CAAC,GAAGA,MAAM,CAACI,OAAO,GAAGJ,MAAM;IACrEE,YAAY,CAACtD,WAAW,CAACqD,QAAQ,CAAC;EACtC;EACAI,YAAYA,CAACL,MAAM,EAAEC,QAAQ,EAAEK,QAAQ,EAAE;IACrC,IAAIN,MAAM,EAAE;MACR,MAAME,YAAY,GAAGC,cAAc,CAACH,MAAM,CAAC,GAAGA,MAAM,CAACI,OAAO,GAAGJ,MAAM;MACrEE,YAAY,CAACG,YAAY,CAACJ,QAAQ,EAAEK,QAAQ,CAAC;IACjD;EACJ;EACAC,WAAWA,CAACC,OAAO,EAAEC,QAAQ,EAAE;IAC3BA,QAAQ,CAAC9M,MAAM,CAAC,CAAC;EACrB;EACA+M,iBAAiBA,CAACC,cAAc,EAAEC,eAAe,EAAE;IAC/C,IAAIvN,EAAE,GAAG,OAAOsN,cAAc,KAAK,QAAQ,GAAG,IAAI,CAAC5M,GAAG,CAAC4B,aAAa,CAACgL,cAAc,CAAC,GAAGA,cAAc;IACrG,IAAI,CAACtN,EAAE,EAAE;MACL,MAAM,IAAIzD,aAAa,CAAC,CAAC,IAAI,CAAC,4CAA4C,CAAC,OAAO4G,SAAS,KAAK,WAAW,IAAIA,SAAS,KACpH,iBAAiBmK,cAAc,8BAA8B,CAAC;IACtE;IACA,IAAI,CAACC,eAAe,EAAE;MAClBvN,EAAE,CAAC4I,WAAW,GAAG,EAAE;IACvB;IACA,OAAO5I,EAAE;EACb;EACAoJ,UAAUA,CAAC7I,IAAI,EAAE;IACb,OAAOA,IAAI,CAAC6I,UAAU;EAC1B;EACAoE,WAAWA,CAACjN,IAAI,EAAE;IACd,OAAOA,IAAI,CAACiN,WAAW;EAC3B;EACAlE,YAAYA,CAACtJ,EAAE,EAAEoC,IAAI,EAAEoK,KAAK,EAAEH,SAAS,EAAE;IACrC,IAAIA,SAAS,EAAE;MACXjK,IAAI,GAAGiK,SAAS,GAAG,GAAG,GAAGjK,IAAI;MAC7B,MAAMqL,YAAY,GAAG9D,cAAc,CAAC0C,SAAS,CAAC;MAC9C,IAAIoB,YAAY,EAAE;QACdzN,EAAE,CAAC0N,cAAc,CAACD,YAAY,EAAErL,IAAI,EAAEoK,KAAK,CAAC;MAChD,CAAC,MACI;QACDxM,EAAE,CAACsJ,YAAY,CAAClH,IAAI,EAAEoK,KAAK,CAAC;MAChC;IACJ,CAAC,MACI;MACDxM,EAAE,CAACsJ,YAAY,CAAClH,IAAI,EAAEoK,KAAK,CAAC;IAChC;EACJ;EACAnD,eAAeA,CAACrJ,EAAE,EAAEoC,IAAI,EAAEiK,SAAS,EAAE;IACjC,IAAIA,SAAS,EAAE;MACX,MAAMoB,YAAY,GAAG9D,cAAc,CAAC0C,SAAS,CAAC;MAC9C,IAAIoB,YAAY,EAAE;QACdzN,EAAE,CAAC2N,iBAAiB,CAACF,YAAY,EAAErL,IAAI,CAAC;MAC5C,CAAC,MACI;QACDpC,EAAE,CAACqJ,eAAe,CAAC,GAAGgD,SAAS,IAAIjK,IAAI,EAAE,CAAC;MAC9C;IACJ,CAAC,MACI;MACDpC,EAAE,CAACqJ,eAAe,CAACjH,IAAI,CAAC;IAC5B;EACJ;EACAwL,QAAQA,CAAC5N,EAAE,EAAEoC,IAAI,EAAE;IACfpC,EAAE,CAAC6N,SAAS,CAAC1F,GAAG,CAAC/F,IAAI,CAAC;EAC1B;EACA0L,WAAWA,CAAC9N,EAAE,EAAEoC,IAAI,EAAE;IAClBpC,EAAE,CAAC6N,SAAS,CAACvN,MAAM,CAAC8B,IAAI,CAAC;EAC7B;EACA2L,QAAQA,CAAC/N,EAAE,EAAEwH,KAAK,EAAEgF,KAAK,EAAEwB,KAAK,EAAE;IAC9B,IAAIA,KAAK,IAAIhR,mBAAmB,CAACiR,QAAQ,GAAGjR,mBAAmB,CAACkR,SAAS,CAAC,EAAE;MACxElO,EAAE,CAACwH,KAAK,CAAC2G,WAAW,CAAC3G,KAAK,EAAEgF,KAAK,EAAEwB,KAAK,GAAGhR,mBAAmB,CAACkR,SAAS,GAAG,WAAW,GAAG,EAAE,CAAC;IAChG,CAAC,MACI;MACDlO,EAAE,CAACwH,KAAK,CAACA,KAAK,CAAC,GAAGgF,KAAK;IAC3B;EACJ;EACA4B,WAAWA,CAACpO,EAAE,EAAEwH,KAAK,EAAEwG,KAAK,EAAE;IAC1B,IAAIA,KAAK,GAAGhR,mBAAmB,CAACiR,QAAQ,EAAE;MACtC;MACAjO,EAAE,CAACwH,KAAK,CAAC6G,cAAc,CAAC7G,KAAK,CAAC;IAClC,CAAC,MACI;MACDxH,EAAE,CAACwH,KAAK,CAACA,KAAK,CAAC,GAAG,EAAE;IACxB;EACJ;EACA2G,WAAWA,CAACnO,EAAE,EAAEoC,IAAI,EAAEoK,KAAK,EAAE;IACzB,IAAIxM,EAAE,IAAI,IAAI,EAAE;MACZ;IACJ;IACA,CAAC,OAAOmD,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC1C,IAAI,CAAC+I,qBAAqB,IAC1BoC,oBAAoB,CAAClM,IAAI,EAAE,UAAU,CAAC;IAC1CpC,EAAE,CAACoC,IAAI,CAAC,GAAGoK,KAAK;EACpB;EACA+B,QAAQA,CAAChO,IAAI,EAAEiM,KAAK,EAAE;IAClBjM,IAAI,CAACiO,SAAS,GAAGhC,KAAK;EAC1B;EACAiC,MAAMA,CAAClN,MAAM,EAAEmN,KAAK,EAAEnL,QAAQ,EAAE;IAC5B,CAAC,OAAOJ,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC1C,IAAI,CAAC+I,qBAAqB,IAC1BoC,oBAAoB,CAACI,KAAK,EAAE,UAAU,CAAC;IAC3C,IAAI,OAAOnN,MAAM,KAAK,QAAQ,EAAE;MAC5BA,MAAM,GAAGxF,OAAO,CAAC,CAAC,CAACuF,oBAAoB,CAAC,IAAI,CAACZ,GAAG,EAAEa,MAAM,CAAC;MACzD,IAAI,CAACA,MAAM,EAAE;QACT,MAAM,IAAIoN,KAAK,CAAC,4BAA4BpN,MAAM,cAAcmN,KAAK,EAAE,CAAC;MAC5E;IACJ;IACA,OAAO,IAAI,CAAC/D,YAAY,CAACxK,gBAAgB,CAACoB,MAAM,EAAEmN,KAAK,EAAE,IAAI,CAACE,sBAAsB,CAACrL,QAAQ,CAAC,CAAC;EACnG;EACAqL,sBAAsBA,CAACC,YAAY,EAAE;IACjC;IACA;IACA;IACA;IACA,OAAQH,KAAK,IAAK;MACd;MACA;MACA;MACA;MACA;MACA,IAAIA,KAAK,KAAK,cAAc,EAAE;QAC1B,OAAOG,YAAY;MACvB;MACA;MACA;MACA,MAAMC,oBAAoB,GAAG,IAAI,CAAC1H,gBAAgB,GAC5C,IAAI,CAAC0D,MAAM,CAACiE,UAAU,CAAC,MAAMF,YAAY,CAACH,KAAK,CAAC,CAAC,GACjDG,YAAY,CAACH,KAAK,CAAC;MACzB,IAAII,oBAAoB,KAAK,KAAK,EAAE;QAChCJ,KAAK,CAACM,cAAc,CAAC,CAAC;MAC1B;MACA,OAAO3I,SAAS;IACpB,CAAC;EACL;AACJ;AACA,MAAM4I,WAAW,GAAG,CAAC,MAAM,GAAG,CAACC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;AAC/C,SAASZ,oBAAoBA,CAAClM,IAAI,EAAE+M,QAAQ,EAAE;EAC1C,IAAI/M,IAAI,CAAC8M,UAAU,CAAC,CAAC,CAAC,KAAKD,WAAW,EAAE;IACpC,MAAM,IAAI1S,aAAa,CAAC,IAAI,CAAC,sDAAsD,wBAAwB4S,QAAQ,IAAI/M,IAAI;AACnI;AACA,qEAAqEA,IAAI,gIAAgI,CAAC;EACtM;AACJ;AACA,SAAS0K,cAAcA,CAACvM,IAAI,EAAE;EAC1B,OAAOA,IAAI,CAACE,OAAO,KAAK,UAAU,IAAIF,IAAI,CAACwM,OAAO,KAAK1G,SAAS;AACpE;AACA,MAAMwF,iBAAiB,SAASZ,mBAAmB,CAAC;EAChDvL,WAAWA,CAACiL,YAAY,EAAEC,gBAAgB,EAAEwE,MAAM,EAAEC,SAAS,EAAE3O,GAAG,EAAEoK,MAAM,EAAEjE,KAAK,EAAEO,gBAAgB,EAAE;IACjG,KAAK,CAACuD,YAAY,EAAEjK,GAAG,EAAEoK,MAAM,EAAE1D,gBAAgB,CAAC;IAClD,IAAI,CAACwD,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACwE,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACE,UAAU,GAAGF,MAAM,CAACG,YAAY,CAAC;MAAEC,IAAI,EAAE;IAAO,CAAC,CAAC;IACvD,IAAI,CAAC5E,gBAAgB,CAAC3C,OAAO,CAAC,IAAI,CAACqH,UAAU,CAAC;IAC9C,MAAM/H,MAAM,GAAGgD,iBAAiB,CAAC8E,SAAS,CAACzD,EAAE,EAAEyD,SAAS,CAAC9H,MAAM,CAAC;IAChE,KAAK,MAAMC,KAAK,IAAID,MAAM,EAAE;MACxB,MAAM4B,OAAO,GAAGtI,QAAQ,CAACL,aAAa,CAAC,OAAO,CAAC;MAC/C,IAAIqG,KAAK,EAAE;QACPsC,OAAO,CAACG,YAAY,CAAC,OAAO,EAAEzC,KAAK,CAAC;MACxC;MACAsC,OAAO,CAACP,WAAW,GAAGpB,KAAK;MAC3B,IAAI,CAAC8H,UAAU,CAAC/F,WAAW,CAACJ,OAAO,CAAC;IACxC;EACJ;EACAsG,gBAAgBA,CAAClP,IAAI,EAAE;IACnB,OAAOA,IAAI,KAAK,IAAI,CAAC6O,MAAM,GAAG,IAAI,CAACE,UAAU,GAAG/O,IAAI;EACxD;EACAgJ,WAAWA,CAACoD,MAAM,EAAEC,QAAQ,EAAE;IAC1B,OAAO,KAAK,CAACrD,WAAW,CAAC,IAAI,CAACkG,gBAAgB,CAAC9C,MAAM,CAAC,EAAEC,QAAQ,CAAC;EACrE;EACAI,YAAYA,CAACL,MAAM,EAAEC,QAAQ,EAAEK,QAAQ,EAAE;IACrC,OAAO,KAAK,CAACD,YAAY,CAAC,IAAI,CAACyC,gBAAgB,CAAC9C,MAAM,CAAC,EAAEC,QAAQ,EAAEK,QAAQ,CAAC;EAChF;EACAC,WAAWA,CAACC,OAAO,EAAEC,QAAQ,EAAE;IAC3B,OAAO,KAAK,CAACF,WAAW,CAAC,IAAI,EAAEE,QAAQ,CAAC;EAC5C;EACAhE,UAAUA,CAAC7I,IAAI,EAAE;IACb,OAAO,IAAI,CAACkP,gBAAgB,CAAC,KAAK,CAACrG,UAAU,CAAC,IAAI,CAACqG,gBAAgB,CAAClP,IAAI,CAAC,CAAC,CAAC;EAC/E;EACA6L,OAAOA,CAAA,EAAG;IACN,IAAI,CAACxB,gBAAgB,CAACvC,UAAU,CAAC,IAAI,CAACiH,UAAU,CAAC;EACrD;AACJ;AACA,MAAM5D,4BAA4B,SAAST,mBAAmB,CAAC;EAC3DvL,WAAWA,CAACiL,YAAY,EAAEC,gBAAgB,EAAEyE,SAAS,EAAExE,yBAAyB,EAAEnK,GAAG,EAAEoK,MAAM,EAAE1D,gBAAgB,EAAEoD,MAAM,EAAE;IACrH,KAAK,CAACG,YAAY,EAAEjK,GAAG,EAAEoK,MAAM,EAAE1D,gBAAgB,CAAC;IAClD,IAAI,CAACwD,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,yBAAyB,GAAGA,yBAAyB;IAC1D,IAAI,CAACtD,MAAM,GAAGiD,MAAM,GAAGD,iBAAiB,CAACC,MAAM,EAAE6E,SAAS,CAAC9H,MAAM,CAAC,GAAG8H,SAAS,CAAC9H,MAAM;EACzF;EACAoE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACf,gBAAgB,CAACtD,SAAS,CAAC,IAAI,CAACC,MAAM,CAAC;EAChD;EACA6E,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACvB,yBAAyB,EAAE;MACjC;IACJ;IACA,IAAI,CAACD,gBAAgB,CAAChD,YAAY,CAAC,IAAI,CAACL,MAAM,CAAC;EACnD;AACJ;AACA,MAAMiE,iCAAiC,SAASE,4BAA4B,CAAC;EACzEhM,WAAWA,CAACiL,YAAY,EAAEC,gBAAgB,EAAEyE,SAAS,EAAEzI,KAAK,EAAEiE,yBAAyB,EAAEnK,GAAG,EAAEoK,MAAM,EAAE1D,gBAAgB,EAAE;IACpH,MAAMoD,MAAM,GAAG5D,KAAK,GAAG,GAAG,GAAGyI,SAAS,CAACzD,EAAE;IACzC,KAAK,CAACjB,YAAY,EAAEC,gBAAgB,EAAEyE,SAAS,EAAExE,yBAAyB,EAAEnK,GAAG,EAAEoK,MAAM,EAAE1D,gBAAgB,EAAEoD,MAAM,CAAC;IAClH,IAAI,CAACkF,WAAW,GAAGvF,oBAAoB,CAACK,MAAM,CAAC;IAC/C,IAAI,CAACmF,QAAQ,GAAGrF,iBAAiB,CAACE,MAAM,CAAC;EAC7C;EACAiB,WAAWA,CAAC/F,OAAO,EAAE;IACjB,IAAI,CAACiG,WAAW,CAAC,CAAC;IAClB,IAAI,CAACrC,YAAY,CAAC5D,OAAO,EAAE,IAAI,CAACiK,QAAQ,EAAE,EAAE,CAAC;EACjD;EACAnP,aAAaA,CAACmM,MAAM,EAAEvK,IAAI,EAAE;IACxB,MAAMpC,EAAE,GAAG,KAAK,CAACQ,aAAa,CAACmM,MAAM,EAAEvK,IAAI,CAAC;IAC5C,KAAK,CAACkH,YAAY,CAACtJ,EAAE,EAAE,IAAI,CAAC0P,WAAW,EAAE,EAAE,CAAC;IAC5C,OAAO1P,EAAE;EACb;AACJ;AAEA,MAAM4P,eAAe,SAASpJ,kBAAkB,CAAC;EAC7C9G,WAAWA,CAACgB,GAAG,EAAE;IACb,KAAK,CAACA,GAAG,CAAC;EACd;EACA;EACA;EACAuF,QAAQA,CAACN,SAAS,EAAE;IAChB,OAAO,IAAI;EACf;EACAxF,gBAAgBA,CAACuF,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAE;IAC1CF,OAAO,CAACvF,gBAAgB,CAACwF,SAAS,EAAEC,OAAO,EAAE,KAAK,CAAC;IACnD,OAAO,MAAM,IAAI,CAACxF,mBAAmB,CAACsF,OAAO,EAAEC,SAAS,EAAEC,OAAO,CAAC;EACtE;EACAxF,mBAAmBA,CAACmB,MAAM,EAAEoE,SAAS,EAAEpC,QAAQ,EAAE;IAC7C,OAAOhC,MAAM,CAACnB,mBAAmB,CAACuF,SAAS,EAAEpC,QAAQ,CAAC;EAC1D;EACA;IAAS,IAAI,CAACe,IAAI,YAAAuL,wBAAArL,iBAAA;MAAA,YAAAA,iBAAA,IAAyFoL,eAAe,EA/mBzBvT,EAAE,CAAA+J,QAAA,CA+mByCnK,QAAQ;IAAA,CAA6C;EAAE;EACnM;IAAS,IAAI,CAACwI,KAAK,kBAhnB8EpI,EAAE,CAAAqI,kBAAA;MAAAC,KAAA,EAgnBYiL,eAAe;MAAAhL,OAAA,EAAfgL,eAAe,CAAAtL;IAAA,EAAG;EAAE;AACvI;AACA;EAAA,QAAAnB,SAAA,oBAAAA,SAAA,KAlnBqG9G,EAAE,CAAAwI,iBAAA,CAknBX+K,eAAe,EAAc,CAAC;IAC9G9K,IAAI,EAAEtI;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEsI,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CxB,IAAI,EAAEpI,MAAM;MACZ6J,IAAI,EAAE,CAACtK,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA,MAAM6T,aAAa,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC;AACzD;AACA;AACA,MAAMC,OAAO,GAAG;EACZ,IAAI,EAAE,WAAW;EACjB,IAAI,EAAE,KAAK;EACX,MAAM,EAAE,QAAQ;EAChB,MAAM,EAAE,QAAQ;EAChB,KAAK,EAAE,QAAQ;EACf,KAAK,EAAE,QAAQ;EACf,MAAM,EAAE,WAAW;EACnB,OAAO,EAAE,YAAY;EACrB,IAAI,EAAE,SAAS;EACf,MAAM,EAAE,WAAW;EACnB,MAAM,EAAE,aAAa;EACrB,QAAQ,EAAE,YAAY;EACtB,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA,MAAMC,oBAAoB,GAAG;EACzB,KAAK,EAAGtB,KAAK,IAAKA,KAAK,CAACuB,MAAM;EAC9B,SAAS,EAAGvB,KAAK,IAAKA,KAAK,CAACwB,OAAO;EACnC,MAAM,EAAGxB,KAAK,IAAKA,KAAK,CAACyB,OAAO;EAChC,OAAO,EAAGzB,KAAK,IAAKA,KAAK,CAAC0B;AAC9B,CAAC;AACD;AACA;AACA;AACA,MAAMC,eAAe,SAAS7J,kBAAkB,CAAC;EAC7C;AACJ;AACA;AACA;EACI9G,WAAWA,CAACgB,GAAG,EAAE;IACb,KAAK,CAACA,GAAG,CAAC;EACd;EACA;AACJ;AACA;AACA;AACA;EACIuF,QAAQA,CAACN,SAAS,EAAE;IAChB,OAAO0K,eAAe,CAACC,cAAc,CAAC3K,SAAS,CAAC,IAAI,IAAI;EAC5D;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIxF,gBAAgBA,CAACuF,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAE;IAC1C,MAAM2K,WAAW,GAAGF,eAAe,CAACC,cAAc,CAAC3K,SAAS,CAAC;IAC7D,MAAM6K,cAAc,GAAGH,eAAe,CAACI,aAAa,CAACF,WAAW,CAAC,SAAS,CAAC,EAAE3K,OAAO,EAAE,IAAI,CAACN,OAAO,CAACQ,OAAO,CAAC,CAAC,CAAC;IAC7G,OAAO,IAAI,CAACR,OAAO,CAACQ,OAAO,CAAC,CAAC,CAAC4K,iBAAiB,CAAC,MAAM;MAClD,OAAO3U,OAAO,CAAC,CAAC,CAACgE,WAAW,CAAC2F,OAAO,EAAE6K,WAAW,CAAC,cAAc,CAAC,EAAEC,cAAc,CAAC;IACtF,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOF,cAAcA,CAAC3K,SAAS,EAAE;IAC7B,MAAMgL,KAAK,GAAGhL,SAAS,CAACiL,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;IAChD,MAAMC,YAAY,GAAGH,KAAK,CAACI,KAAK,CAAC,CAAC;IAClC,IAAIJ,KAAK,CAACjN,MAAM,KAAK,CAAC,IAAI,EAAEoN,YAAY,KAAK,SAAS,IAAIA,YAAY,KAAK,OAAO,CAAC,EAAE;MACjF,OAAO,IAAI;IACf;IACA,MAAME,GAAG,GAAGX,eAAe,CAACY,aAAa,CAACN,KAAK,CAACO,GAAG,CAAC,CAAC,CAAC;IACtD,IAAIC,OAAO,GAAG,EAAE;IAChB,IAAIC,MAAM,GAAGT,KAAK,CAACU,OAAO,CAAC,MAAM,CAAC;IAClC,IAAID,MAAM,GAAG,CAAC,CAAC,EAAE;MACbT,KAAK,CAACW,MAAM,CAACF,MAAM,EAAE,CAAC,CAAC;MACvBD,OAAO,GAAG,OAAO;IACrB;IACArB,aAAa,CAAClM,OAAO,CAAE2N,YAAY,IAAK;MACpC,MAAMC,KAAK,GAAGb,KAAK,CAACU,OAAO,CAACE,YAAY,CAAC;MACzC,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAE;QACZb,KAAK,CAACW,MAAM,CAACE,KAAK,EAAE,CAAC,CAAC;QACtBL,OAAO,IAAII,YAAY,GAAG,GAAG;MACjC;IACJ,CAAC,CAAC;IACFJ,OAAO,IAAIH,GAAG;IACd,IAAIL,KAAK,CAACjN,MAAM,IAAI,CAAC,IAAIsN,GAAG,CAACtN,MAAM,KAAK,CAAC,EAAE;MACvC;MACA,OAAO,IAAI;IACf;IACA;IACA;IACA;IACA,MAAM+N,MAAM,GAAG,CAAC,CAAC;IACjBA,MAAM,CAAC,cAAc,CAAC,GAAGX,YAAY;IACrCW,MAAM,CAAC,SAAS,CAAC,GAAGN,OAAO;IAC3B,OAAOM,MAAM;EACjB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,qBAAqBA,CAAChD,KAAK,EAAEiD,WAAW,EAAE;IAC7C,IAAIC,OAAO,GAAG7B,OAAO,CAACrB,KAAK,CAACsC,GAAG,CAAC,IAAItC,KAAK,CAACsC,GAAG;IAC7C,IAAIA,GAAG,GAAG,EAAE;IACZ,IAAIW,WAAW,CAACN,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;MACnCO,OAAO,GAAGlD,KAAK,CAACmD,IAAI;MACpBb,GAAG,GAAG,OAAO;IACjB;IACA;IACA,IAAIY,OAAO,IAAI,IAAI,IAAI,CAACA,OAAO,EAC3B,OAAO,KAAK;IAChBA,OAAO,GAAGA,OAAO,CAAChB,WAAW,CAAC,CAAC;IAC/B,IAAIgB,OAAO,KAAK,GAAG,EAAE;MACjBA,OAAO,GAAG,OAAO,CAAC,CAAC;IACvB,CAAC,MACI,IAAIA,OAAO,KAAK,GAAG,EAAE;MACtBA,OAAO,GAAG,KAAK,CAAC,CAAC;IACrB;IACA9B,aAAa,CAAClM,OAAO,CAAE2N,YAAY,IAAK;MACpC,IAAIA,YAAY,KAAKK,OAAO,EAAE;QAC1B,MAAME,cAAc,GAAG9B,oBAAoB,CAACuB,YAAY,CAAC;QACzD,IAAIO,cAAc,CAACpD,KAAK,CAAC,EAAE;UACvBsC,GAAG,IAAIO,YAAY,GAAG,GAAG;QAC7B;MACJ;IACJ,CAAC,CAAC;IACFP,GAAG,IAAIY,OAAO;IACd,OAAOZ,GAAG,KAAKW,WAAW;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,OAAOlB,aAAaA,CAACU,OAAO,EAAEvL,OAAO,EAAEmM,IAAI,EAAE;IACzC,OAAQrD,KAAK,IAAK;MACd,IAAI2B,eAAe,CAACqB,qBAAqB,CAAChD,KAAK,EAAEyC,OAAO,CAAC,EAAE;QACvDY,IAAI,CAAChD,UAAU,CAAC,MAAMnJ,OAAO,CAAC8I,KAAK,CAAC,CAAC;MACzC;IACJ,CAAC;EACL;EACA;EACA,OAAOuC,aAAaA,CAACe,OAAO,EAAE;IAC1B,OAAOA,OAAO,KAAK,KAAK,GAAG,QAAQ,GAAGA,OAAO;EACjD;EACA;IAAS,IAAI,CAAC1N,IAAI,YAAA2N,wBAAAzN,iBAAA;MAAA,YAAAA,iBAAA,IAAyF6L,eAAe,EA3xBzBhU,EAAE,CAAA+J,QAAA,CA2xByCnK,QAAQ;IAAA,CAA6C;EAAE;EACnM;IAAS,IAAI,CAACwI,KAAK,kBA5xB8EpI,EAAE,CAAAqI,kBAAA;MAAAC,KAAA,EA4xBY0L,eAAe;MAAAzL,OAAA,EAAfyL,eAAe,CAAA/L;IAAA,EAAG;EAAE;AACvI;AACA;EAAA,QAAAnB,SAAA,oBAAAA,SAAA,KA9xBqG9G,EAAE,CAAAwI,iBAAA,CA8xBXwL,eAAe,EAAc,CAAC;IAC9GvL,IAAI,EAAEtI;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEsI,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CxB,IAAI,EAAEpI,MAAM;MACZ6J,IAAI,EAAE,CAACtK,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiW,oBAAoBA,CAACC,aAAa,EAAEC,OAAO,EAAE;EAClD,OAAOnV,0BAA0B,CAAC;IAAEkV,aAAa;IAAE,GAAGE,qBAAqB,CAACD,OAAO;EAAE,CAAC,CAAC;AAC3F;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,iBAAiBA,CAACF,OAAO,EAAE;EAChC,OAAOnV,0BAA0B,CAACoV,qBAAqB,CAACD,OAAO,CAAC,CAAC;AACrE;AACA,SAASC,qBAAqBA,CAACD,OAAO,EAAE;EACpC,OAAO;IACHG,YAAY,EAAE,CAAC,GAAGC,wBAAwB,EAAE,IAAIJ,OAAO,EAAEK,SAAS,IAAI,EAAE,CAAC,CAAC;IAC1EC,iBAAiB,EAAEC;EACvB,CAAC;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,+BAA+BA,CAAA,EAAG;EACvC;EACA;EACA;EACA,OAAO,CAAC,GAAGC,qBAAqB,CAAC;AACrC;AACA,SAASC,cAAcA,CAAA,EAAG;EACtBjT,iBAAiB,CAACC,WAAW,CAAC,CAAC;AACnC;AACA,SAASiT,YAAYA,CAAA,EAAG;EACpB,OAAO,IAAI7V,YAAY,CAAC,CAAC;AAC7B;AACA,SAAS8V,SAASA,CAAA,EAAG;EACjB;EACA7V,YAAY,CAAC0D,QAAQ,CAAC;EACtB,OAAOA,QAAQ;AACnB;AACA,MAAM8R,mCAAmC,GAAG,CACxC;EAAEM,OAAO,EAAEpW,WAAW;EAAEqW,QAAQ,EAAEhX;AAAqB,CAAC,EACxD;EAAE+W,OAAO,EAAE7V,oBAAoB;EAAE8V,QAAQ,EAAEJ,cAAc;EAAEK,KAAK,EAAE;AAAK,CAAC,EACxE;EAAEF,OAAO,EAAEhX,QAAQ;EAAEmX,UAAU,EAAEJ,SAAS;EAAEK,IAAI,EAAE;AAAG,CAAC,CACzD;AACD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAGjW,qBAAqB,CAACC,YAAY,EAAE,SAAS,EAAEqV,mCAAmC,CAAC;AAC3G;AACA;AACA;AACA;AACA;AACA;AACA,MAAMY,+BAA+B,GAAG,IAAI9W,cAAc,CAAC,OAAO0G,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG,gCAAgC,GAAG,EAAE,CAAC;AACjJ,MAAM0P,qBAAqB,GAAG,CAC1B;EACII,OAAO,EAAE1V,mBAAmB;EAC5BiW,QAAQ,EAAE5Q,qBAAqB;EAC/ByQ,IAAI,EAAE;AACV,CAAC,EACD;EACIJ,OAAO,EAAEzV,YAAY;EACrBgW,QAAQ,EAAE/V,WAAW;EACrB4V,IAAI,EAAE,CAAC3V,MAAM,EAAEC,mBAAmB,EAAEJ,mBAAmB;AAC3D,CAAC,EACD;EACI0V,OAAO,EAAExV,WAAW;EAAE;EACtB+V,QAAQ,EAAE/V,WAAW;EACrB4V,IAAI,EAAE,CAAC3V,MAAM,EAAEC,mBAAmB,EAAEJ,mBAAmB;AAC3D,CAAC,CACJ;AACD,MAAMiV,wBAAwB,GAAG,CAC7B;EAAES,OAAO,EAAErV,eAAe;EAAEsV,QAAQ,EAAE;AAAO,CAAC,EAC9C;EAAED,OAAO,EAAE/V,YAAY;EAAEkW,UAAU,EAAEL,YAAY;EAAEM,IAAI,EAAE;AAAG,CAAC,EAC7D;EACIJ,OAAO,EAAElO,qBAAqB;EAC9ByO,QAAQ,EAAE5D,eAAe;EACzBuD,KAAK,EAAE,IAAI;EACXE,IAAI,EAAE,CAACpX,QAAQ,EAAEyB,MAAM,EAAEb,WAAW;AACxC,CAAC,EACD;EAAEoW,OAAO,EAAElO,qBAAqB;EAAEyO,QAAQ,EAAEnD,eAAe;EAAE8C,KAAK,EAAE,IAAI;EAAEE,IAAI,EAAE,CAACpX,QAAQ;AAAE,CAAC,EAC5FyO,mBAAmB,EACnB/D,gBAAgB,EAChB3B,YAAY,EACZ;EAAEiO,OAAO,EAAEpV,gBAAgB;EAAE4V,WAAW,EAAE/I;AAAoB,CAAC,EAC/D;EAAEuI,OAAO,EAAE9W,UAAU;EAAEqX,QAAQ,EAAErP,UAAU;EAAEkP,IAAI,EAAE;AAAG,CAAC,EACvD,OAAOlQ,SAAS,KAAK,WAAW,IAAIA,SAAS,GACvC;EAAE8P,OAAO,EAAEM,+BAA+B;EAAEL,QAAQ,EAAE;AAAK,CAAC,GAC5D,EAAE,CACX;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMQ,aAAa,CAAC;EAChBhU,WAAWA,CAACiU,uBAAuB,EAAE;IACjC,IAAI,CAAC,OAAOxQ,SAAS,KAAK,WAAW,IAAIA,SAAS,KAAKwQ,uBAAuB,EAAE;MAC5E,MAAM,IAAIpX,aAAa,CAAC,IAAI,CAAC,sDAAsD,oFAAoF,GACnK,mFAAmF,CAAC;IAC5F;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOqX,oBAAoBA,CAACC,MAAM,EAAE;IAChC,OAAO;MACHC,QAAQ,EAAEJ,aAAa;MACvBjB,SAAS,EAAE,CAAC;QAAEQ,OAAO,EAAEtW,MAAM;QAAEuW,QAAQ,EAAEW,MAAM,CAACjN;MAAM,CAAC;IAC3D,CAAC;EACL;EACA;IAAS,IAAI,CAACtC,IAAI,YAAAyP,sBAAAvP,iBAAA;MAAA,YAAAA,iBAAA,IAAyFkP,aAAa,EA3+BvBrX,EAAE,CAAA+J,QAAA,CA2+BuCmN,+BAA+B;IAAA,CAA2E;EAAE;EACtP;IAAS,IAAI,CAACS,IAAI,kBA5+B+E3X,EAAE,CAAA4X,gBAAA;MAAAnP,IAAA,EA4+BS4O;IAAa,EAA+C;EAAE;EAC1K;IAAS,IAAI,CAACQ,IAAI,kBA7+B+E7X,EAAE,CAAA8X,gBAAA;MAAA1B,SAAA,EA6+BmC,CAAC,GAAGD,wBAAwB,EAAE,GAAGK,qBAAqB,CAAC;MAAAuB,OAAA,GAAYhY,YAAY,EAAE0B,iBAAiB;IAAA,EAAI;EAAE;AAClP;AACA;EAAA,QAAAqF,SAAA,oBAAAA,SAAA,KA/+BqG9G,EAAE,CAAAwI,iBAAA,CA++BX6O,aAAa,EAAc,CAAC;IAC5G5O,IAAI,EAAE/G,QAAQ;IACdwI,IAAI,EAAE,CAAC;MACCkM,SAAS,EAAE,CAAC,GAAGD,wBAAwB,EAAE,GAAGK,qBAAqB,CAAC;MAClEwB,OAAO,EAAE,CAACjY,YAAY,EAAE0B,iBAAiB;IAC7C,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEgH,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CxB,IAAI,EAAEhI;IACV,CAAC,EAAE;MACCgI,IAAI,EAAE9G;IACV,CAAC,EAAE;MACC8G,IAAI,EAAEpI,MAAM;MACZ6J,IAAI,EAAE,CAACgN,+BAA+B;IAC1C,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMe,IAAI,CAAC;EACP5U,WAAWA,CAAC+G,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC8N,IAAI,GAAGxY,OAAO,CAAC,CAAC;EACzB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIyY,MAAMA,CAACC,GAAG,EAAEC,aAAa,GAAG,KAAK,EAAE;IAC/B,IAAI,CAACD,GAAG,EACJ,OAAO,IAAI;IACf,OAAO,IAAI,CAACE,mBAAmB,CAACF,GAAG,EAAEC,aAAa,CAAC;EACvD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,OAAOA,CAACC,IAAI,EAAEH,aAAa,GAAG,KAAK,EAAE;IACjC,IAAI,CAACG,IAAI,EACL,OAAO,EAAE;IACb,OAAOA,IAAI,CAACC,MAAM,CAAC,CAACrD,MAAM,EAAEgD,GAAG,KAAK;MAChC,IAAIA,GAAG,EAAE;QACLhD,MAAM,CAAC3N,IAAI,CAAC,IAAI,CAAC6Q,mBAAmB,CAACF,GAAG,EAAEC,aAAa,CAAC,CAAC;MAC7D;MACA,OAAOjD,MAAM;IACjB,CAAC,EAAE,EAAE,CAAC;EACV;EACA;AACJ;AACA;AACA;AACA;AACA;EACIsD,MAAMA,CAACC,YAAY,EAAE;IACjB,IAAI,CAACA,YAAY,EACb,OAAO,IAAI;IACf,OAAO,IAAI,CAACvO,IAAI,CAACnE,aAAa,CAAC,QAAQ0S,YAAY,GAAG,CAAC,IAAI,IAAI;EACnE;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,OAAOA,CAACD,YAAY,EAAE;IAClB,IAAI,CAACA,YAAY,EACb,OAAO,EAAE;IACb,MAAME,IAAI,CAAC,eAAe,IAAI,CAACzO,IAAI,CAACiC,gBAAgB,CAAC,QAAQsM,YAAY,GAAG,CAAC;IAC7E,OAAOE,IAAI,GAAG,EAAE,CAAC1P,KAAK,CAAC2P,IAAI,CAACD,IAAI,CAAC,GAAG,EAAE;EAC1C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIE,SAASA,CAACX,GAAG,EAAEY,QAAQ,EAAE;IACrB,IAAI,CAACZ,GAAG,EACJ,OAAO,IAAI;IACfY,QAAQ,GAAGA,QAAQ,IAAI,IAAI,CAACC,cAAc,CAACb,GAAG,CAAC;IAC/C,MAAMc,IAAI,GAAG,IAAI,CAACR,MAAM,CAACM,QAAQ,CAAC;IAClC,IAAIE,IAAI,EAAE;MACN,OAAO,IAAI,CAACC,yBAAyB,CAACf,GAAG,EAAEc,IAAI,CAAC;IACpD;IACA,OAAO,IAAI,CAACZ,mBAAmB,CAACF,GAAG,EAAE,IAAI,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;EACIgB,SAASA,CAACT,YAAY,EAAE;IACpB,IAAI,CAACU,gBAAgB,CAAC,IAAI,CAACX,MAAM,CAACC,YAAY,CAAC,CAAC;EACpD;EACA;AACJ;AACA;AACA;EACIU,gBAAgBA,CAACH,IAAI,EAAE;IACnB,IAAIA,IAAI,EAAE;MACN,IAAI,CAAChB,IAAI,CAACjU,MAAM,CAACiV,IAAI,CAAC;IAC1B;EACJ;EACAZ,mBAAmBA,CAACY,IAAI,EAAEb,aAAa,GAAG,KAAK,EAAE;IAC7C,IAAI,CAACA,aAAa,EAAE;MAChB,MAAMW,QAAQ,GAAG,IAAI,CAACC,cAAc,CAACC,IAAI,CAAC;MAC1C;MACA;MACA;MACA,MAAMxS,IAAI,GAAG,IAAI,CAACkS,OAAO,CAACI,QAAQ,CAAC,CAACM,MAAM,CAAE5S,IAAI,IAAK,IAAI,CAAC6S,mBAAmB,CAACL,IAAI,EAAExS,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MAC7F,IAAIA,IAAI,KAAKsD,SAAS,EAClB,OAAOtD,IAAI;IACnB;IACA,MAAM2C,OAAO,GAAG,IAAI,CAAC6O,IAAI,CAAC/T,aAAa,CAAC,MAAM,CAAC;IAC/C,IAAI,CAACgV,yBAAyB,CAACD,IAAI,EAAE7P,OAAO,CAAC;IAC7C,MAAM+C,IAAI,GAAG,IAAI,CAAChC,IAAI,CAACoP,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACtDpN,IAAI,CAACc,WAAW,CAAC7D,OAAO,CAAC;IACzB,OAAOA,OAAO;EAClB;EACA8P,yBAAyBA,CAACf,GAAG,EAAEzU,EAAE,EAAE;IAC/B+L,MAAM,CAACxD,IAAI,CAACkM,GAAG,CAAC,CAAC7Q,OAAO,CAAEkS,IAAI,IAAK9V,EAAE,CAACsJ,YAAY,CAAC,IAAI,CAACyM,cAAc,CAACD,IAAI,CAAC,EAAErB,GAAG,CAACqB,IAAI,CAAC,CAAC,CAAC;IACzF,OAAO9V,EAAE;EACb;EACAsV,cAAcA,CAACb,GAAG,EAAE;IAChB,MAAMuB,IAAI,GAAGvB,GAAG,CAACrS,IAAI,GAAG,MAAM,GAAG,UAAU;IAC3C,OAAO,GAAG4T,IAAI,KAAKvB,GAAG,CAACuB,IAAI,CAAC,GAAG;EACnC;EACAJ,mBAAmBA,CAACnB,GAAG,EAAE1R,IAAI,EAAE;IAC3B,OAAOgJ,MAAM,CAACxD,IAAI,CAACkM,GAAG,CAAC,CAACwB,KAAK,CAAEjF,GAAG,IAAKjO,IAAI,CAACR,YAAY,CAAC,IAAI,CAACwT,cAAc,CAAC/E,GAAG,CAAC,CAAC,KAAKyD,GAAG,CAACzD,GAAG,CAAC,CAAC;EACpG;EACA+E,cAAcA,CAACD,IAAI,EAAE;IACjB,OAAOI,aAAa,CAACJ,IAAI,CAAC,IAAIA,IAAI;EACtC;EACA;IAAS,IAAI,CAACxR,IAAI,YAAA6R,aAAA3R,iBAAA;MAAA,YAAAA,iBAAA,IAAyF8P,IAAI,EAnpCdjY,EAAE,CAAA+J,QAAA,CAmpC8BnK,QAAQ;IAAA,CAA6C;EAAE;EACxL;IAAS,IAAI,CAACwI,KAAK,kBAppC8EpI,EAAE,CAAAqI,kBAAA;MAAAC,KAAA,EAopCY2P,IAAI;MAAA1P,OAAA,EAAJ0P,IAAI,CAAAhQ,IAAA;MAAA4F,UAAA,EAAc;IAAM,EAAG;EAAE;AAChJ;AACA;EAAA,QAAA/G,SAAA,oBAAAA,SAAA,KAtpCqG9G,EAAE,CAAAwI,iBAAA,CAspCXyP,IAAI,EAAc,CAAC;IACnGxP,IAAI,EAAEtI,UAAU;IAChB+J,IAAI,EAAE,CAAC;MAAE2D,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEpF,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CxB,IAAI,EAAEpI,MAAM;MACZ6J,IAAI,EAAE,CAACtK,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB;AACA;AACA;AACA,MAAMia,aAAa,GAAG;EAClBE,SAAS,EAAE;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,KAAK,CAAC;EACR3W,WAAWA,CAAC+G,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACA;AACJ;AACA;EACI6P,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC7P,IAAI,CAAC8P,KAAK;EAC1B;EACA;AACJ;AACA;AACA;EACIC,QAAQA,CAACC,QAAQ,EAAE;IACf,IAAI,CAAChQ,IAAI,CAAC8P,KAAK,GAAGE,QAAQ,IAAI,EAAE;EACpC;EACA;IAAS,IAAI,CAACnS,IAAI,YAAAoS,cAAAlS,iBAAA;MAAA,YAAAA,iBAAA,IAAyF6R,KAAK,EA/rCfha,EAAE,CAAA+J,QAAA,CA+rC+BnK,QAAQ;IAAA,CAA6C;EAAE;EACzL;IAAS,IAAI,CAACwI,KAAK,kBAhsC8EpI,EAAE,CAAAqI,kBAAA;MAAAC,KAAA,EAgsCY0R,KAAK;MAAAzR,OAAA,EAALyR,KAAK,CAAA/R,IAAA;MAAA4F,UAAA,EAAc;IAAM,EAAG;EAAE;AACjJ;AACA;EAAA,QAAA/G,SAAA,oBAAAA,SAAA,KAlsCqG9G,EAAE,CAAAwI,iBAAA,CAksCXwR,KAAK,EAAc,CAAC;IACpGvR,IAAI,EAAEtI,UAAU;IAChB+J,IAAI,EAAE,CAAC;MAAE2D,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEpF,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CxB,IAAI,EAAEpI,MAAM;MACZ6J,IAAI,EAAE,CAACtK,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0a,WAAWA,CAACvU,IAAI,EAAEoK,KAAK,EAAE;EAC9B,IAAI,OAAOoK,QAAQ,KAAK,WAAW,IAAI,CAACA,QAAQ,EAAE;IAC9C;IACA;IACA;IACA;IACA,MAAMC,EAAE,GAAIva,OAAO,CAAC,IAAI,CAAC,GAAGA,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAE;IAChDua,EAAE,CAACzU,IAAI,CAAC,GAAGoK,KAAK;EACpB;AACJ;AAEA,MAAMsK,yBAAyB,CAAC;EAC5BpX,WAAWA,CAACqX,SAAS,EAAEC,QAAQ,EAAE;IAC7B,IAAI,CAACD,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,QAAQ,GAAGA,QAAQ;EAC5B;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAClBvX,WAAWA,CAACwX,GAAG,EAAE;IACb,IAAI,CAACC,MAAM,GAAGD,GAAG,CAACE,QAAQ,CAACrR,GAAG,CAAC9H,cAAc,CAAC;EAClD;EACA;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIoZ,mBAAmBA,CAACC,MAAM,EAAE;IACxB,MAAMC,MAAM,GAAGD,MAAM,IAAIA,MAAM,CAAC,QAAQ,CAAC;IACzC,MAAME,WAAW,GAAG,kBAAkB;IACtC;IACA,IAAID,MAAM,IAAI,SAAS,IAAIE,OAAO,IAAI,OAAOA,OAAO,CAACC,OAAO,KAAK,UAAU,EAAE;MACzED,OAAO,CAACC,OAAO,CAACF,WAAW,CAAC;IAChC;IACA,MAAMG,KAAK,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC;IAC/B,IAAIb,QAAQ,GAAG,CAAC;IAChB,OAAOA,QAAQ,GAAG,CAAC,IAAIY,WAAW,CAACC,GAAG,CAAC,CAAC,GAAGF,KAAK,GAAG,GAAG,EAAE;MACpD,IAAI,CAACR,MAAM,CAACW,IAAI,CAAC,CAAC;MAClBd,QAAQ,EAAE;IACd;IACA,MAAMe,GAAG,GAAGH,WAAW,CAACC,GAAG,CAAC,CAAC;IAC7B,IAAIN,MAAM,IAAI,YAAY,IAAIE,OAAO,IAAI,OAAOA,OAAO,CAACO,UAAU,KAAK,UAAU,EAAE;MAC/EP,OAAO,CAACO,UAAU,CAACR,WAAW,CAAC;IACnC;IACA,MAAMT,SAAS,GAAG,CAACgB,GAAG,GAAGJ,KAAK,IAAIX,QAAQ;IAC1CS,OAAO,CAACQ,GAAG,CAAC,OAAOjB,QAAQ,0BAA0B,CAAC;IACtDS,OAAO,CAACQ,GAAG,CAAC,GAAGlB,SAAS,CAACmB,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;IACnD,OAAO,IAAIpB,yBAAyB,CAACC,SAAS,EAAEC,QAAQ,CAAC;EAC7D;AACJ;AAEA,MAAMmB,oBAAoB,GAAG,UAAU;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAAClB,GAAG,EAAE;EAC3BP,WAAW,CAACwB,oBAAoB,EAAE,IAAIlB,eAAe,CAACC,GAAG,CAAC,CAAC;EAC3D,OAAOA,GAAG;AACd;AACA;AACA;AACA;AACA;AACA;AACA,SAASmB,iBAAiBA,CAAA,EAAG;EACzB1B,WAAW,CAACwB,oBAAoB,EAAE,IAAI,CAAC;AAC3C;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMG,EAAE,CAAC;EACL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,GAAGA,CAAA,EAAG;IACT,OAAO,MAAM,IAAI;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOC,GAAGA,CAACnD,QAAQ,EAAE;IACjB,OAAQoD,YAAY,IAAK;MACrB,OAAOA,YAAY,CAACC,aAAa,IAAI,IAAI,GACnCC,cAAc,CAACF,YAAY,CAACC,aAAa,EAAErD,QAAQ,CAAC,GACpD,KAAK;IACf,CAAC;EACL;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI,OAAOuD,SAASA,CAAC9T,IAAI,EAAE;IACnB,OAAQ+T,SAAS,IAAKA,SAAS,CAACC,cAAc,CAACzH,OAAO,CAACvM,IAAI,CAAC,KAAK,CAAC,CAAC;EACvE;AACJ;AACA,SAAS6T,cAAcA,CAACI,CAAC,EAAE1D,QAAQ,EAAE;EACjC,IAAItZ,OAAO,CAAC,CAAC,CAACiF,aAAa,CAAC+X,CAAC,CAAC,EAAE;IAC5B,OAASA,CAAC,CAACC,OAAO,IAAID,CAAC,CAACC,OAAO,CAAC3D,QAAQ,CAAC,IACpC0D,CAAC,CAACE,iBAAiB,IAAIF,CAAC,CAACE,iBAAiB,CAAC5D,QAAQ,CAAE,IACrD0D,CAAC,CAACG,qBAAqB,IAAIH,CAAC,CAACG,qBAAqB,CAAC7D,QAAQ,CAAE;EACtE;EACA,OAAO,KAAK;AAChB;;AAEA;AACA;AACA;AACA,MAAM8D,WAAW,GAAG;EAChB;EACA,KAAK,EAAE,IAAI;EACX,UAAU,EAAE,IAAI;EAChB,SAAS,EAAE,IAAI;EACf,QAAQ,EAAE,IAAI;EACd,WAAW,EAAE,IAAI;EACjB,SAAS,EAAE,IAAI;EACf,UAAU,EAAE,IAAI;EAChB,OAAO,EAAE,IAAI;EACb,SAAS,EAAE,IAAI;EACf;EACA,OAAO,EAAE,IAAI;EACb,YAAY,EAAE,IAAI;EAClB,WAAW,EAAE,IAAI;EACjB,UAAU,EAAE,IAAI;EAChB,aAAa,EAAE,IAAI;EACnB,SAAS,EAAE,IAAI;EACf,UAAU,EAAE,IAAI;EAChB;EACA,OAAO,EAAE,IAAI;EACb,SAAS,EAAE,IAAI;EACf;EACA,QAAQ,EAAE,IAAI;EACd,aAAa,EAAE,IAAI;EACnB,YAAY,EAAE,IAAI;EAClB,WAAW,EAAE,IAAI;EACjB,cAAc,EAAE,IAAI;EACpB;EACA,OAAO,EAAE,IAAI;EACb,WAAW,EAAE,IAAI;EACjB,YAAY,EAAE,IAAI;EAClB,SAAS,EAAE,IAAI;EACf,WAAW,EAAE,IAAI;EACjB;EACA,KAAK,EAAE,IAAI;EACX,WAAW,EAAE;AACjB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,qBAAqB,GAAG,IAAI3c,cAAc,CAAC,qBAAqB,CAAC;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4c,aAAa,GAAG,IAAI5c,cAAc,CAAC,cAAc,CAAC;AACxD;AACA;AACA;AACA;AACA;AACA,MAAM6c,mBAAmB,CAAC;EACtB5Z,WAAWA,CAAA,EAAG;IACV;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC6Z,MAAM,GAAG,EAAE;IAChB;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;EACvB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAAC/T,OAAO,EAAE;IACjB,MAAMgU,EAAE,GAAG,IAAIC,MAAM,CAACjU,OAAO,EAAE,IAAI,CAAC0M,OAAO,CAAC;IAC5CsH,EAAE,CAAC3T,GAAG,CAAC,OAAO,CAAC,CAACG,GAAG,CAAC;MAAE0T,MAAM,EAAE;IAAK,CAAC,CAAC;IACrCF,EAAE,CAAC3T,GAAG,CAAC,QAAQ,CAAC,CAACG,GAAG,CAAC;MAAE0T,MAAM,EAAE;IAAK,CAAC,CAAC;IACtC,KAAK,MAAMjU,SAAS,IAAI,IAAI,CAAC6T,SAAS,EAAE;MACpCE,EAAE,CAAC3T,GAAG,CAACJ,SAAS,CAAC,CAACO,GAAG,CAAC,IAAI,CAACsT,SAAS,CAAC7T,SAAS,CAAC,CAAC;IACpD;IACA,OAAO+T,EAAE;EACb;EACA;IAAS,IAAI,CAACpV,IAAI,YAAAuV,4BAAArV,iBAAA;MAAA,YAAAA,iBAAA,IAAyF8U,mBAAmB;IAAA,CAAoD;EAAE;EACpL;IAAS,IAAI,CAAC7U,KAAK,kBA38C8EpI,EAAE,CAAAqI,kBAAA;MAAAC,KAAA,EA28CY2U,mBAAmB;MAAA1U,OAAA,EAAnB0U,mBAAmB,CAAAhV;IAAA,EAAG;EAAE;AAC3I;AACA;EAAA,QAAAnB,SAAA,oBAAAA,SAAA,KA78CqG9G,EAAE,CAAAwI,iBAAA,CA68CXyU,mBAAmB,EAAc,CAAC;IAClHxU,IAAI,EAAEtI;EACV,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA,MAAMsd,oBAAoB,SAAStT,kBAAkB,CAAC;EAClD9G,WAAWA,CAACgB,GAAG,EAAEqZ,OAAO,EAAEtC,OAAO,EAAEuC,MAAM,EAAE;IACvC,KAAK,CAACtZ,GAAG,CAAC;IACV,IAAI,CAACqZ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACtC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACuC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,cAAc,GAAG,IAAI;EAC9B;EACAhU,QAAQA,CAACN,SAAS,EAAE;IAChB,IAAI,CAACwT,WAAW,CAACe,cAAc,CAACvU,SAAS,CAACiL,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAACuJ,aAAa,CAACxU,SAAS,CAAC,EAAE;MACxF,OAAO,KAAK;IAChB;IACA,IAAI,CAACnE,MAAM,CAACmY,MAAM,IAAI,CAAC,IAAI,CAACK,MAAM,EAAE;MAChC,IAAI,OAAO7W,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;QAC/C,IAAI,CAACsU,OAAO,CAAC2C,IAAI,CAAC,QAAQzU,SAAS,mDAAmD,GAClF,iDAAiD,CAAC;MAC1D;MACA,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf;EACAxF,gBAAgBA,CAACuF,OAAO,EAAEC,SAAS,EAAEC,OAAO,EAAE;IAC1C,MAAMmM,IAAI,GAAG,IAAI,CAACzM,OAAO,CAACQ,OAAO,CAAC,CAAC;IACnCH,SAAS,GAAGA,SAAS,CAACiL,WAAW,CAAC,CAAC;IACnC;IACA;IACA,IAAI,CAACpP,MAAM,CAACmY,MAAM,IAAI,IAAI,CAACK,MAAM,EAAE;MAC/B,IAAI,CAACC,cAAc,GAAG,IAAI,CAACA,cAAc,IAAIlI,IAAI,CAACrB,iBAAiB,CAAC,MAAM,IAAI,CAACsJ,MAAM,CAAC,CAAC,CAAC;MACxF;MACA;MACA;MACA,IAAIK,kBAAkB,GAAG,KAAK;MAC9B,IAAIC,UAAU,GAAGA,CAAA,KAAM;QACnBD,kBAAkB,GAAG,IAAI;MAC7B,CAAC;MACDtI,IAAI,CAACrB,iBAAiB,CAAC,MAAM,IAAI,CAACuJ,cAAc,CAACM,IAAI,CAAC,MAAM;QACxD;QACA,IAAI,CAAC/Y,MAAM,CAACmY,MAAM,EAAE;UAChB,IAAI,OAAOxW,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;YAC/C,IAAI,CAACsU,OAAO,CAAC2C,IAAI,CAAC,mEAAmE,CAAC;UAC1F;UACAE,UAAU,GAAGA,CAAA,KAAM,CAAE,CAAC;UACtB;QACJ;QACA,IAAI,CAACD,kBAAkB,EAAE;UACrB;UACA;UACA;UACAC,UAAU,GAAG,IAAI,CAACna,gBAAgB,CAACuF,OAAO,EAAEC,SAAS,EAAEC,OAAO,CAAC;QACnE;MACJ,CAAC,CAAC,CAAC4U,KAAK,CAAC,MAAM;QACX,IAAI,OAAOrX,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;UAC/C,IAAI,CAACsU,OAAO,CAAC2C,IAAI,CAAC,QAAQzU,SAAS,6CAA6C,GAC5E,0BAA0B,CAAC;QACnC;QACA2U,UAAU,GAAGA,CAAA,KAAM,CAAE,CAAC;MAC1B,CAAC,CAAC,CAAC;MACH;MACA;MACA;MACA,OAAO,MAAM;QACTA,UAAU,CAAC,CAAC;MAChB,CAAC;IACL;IACA,OAAOvI,IAAI,CAACrB,iBAAiB,CAAC,MAAM;MAChC;MACA,MAAMgJ,EAAE,GAAG,IAAI,CAACK,OAAO,CAACN,WAAW,CAAC/T,OAAO,CAAC;MAC5C,MAAMnC,QAAQ,GAAG,SAAAA,CAAUkX,QAAQ,EAAE;QACjC1I,IAAI,CAAChD,UAAU,CAAC,YAAY;UACxBnJ,OAAO,CAAC6U,QAAQ,CAAC;QACrB,CAAC,CAAC;MACN,CAAC;MACDf,EAAE,CAACgB,EAAE,CAAC/U,SAAS,EAAEpC,QAAQ,CAAC;MAC1B,OAAO,MAAM;QACTmW,EAAE,CAACiB,GAAG,CAAChV,SAAS,EAAEpC,QAAQ,CAAC;QAC3B;QACA,IAAI,OAAOmW,EAAE,CAACtN,OAAO,KAAK,UAAU,EAAE;UAClCsN,EAAE,CAACtN,OAAO,CAAC,CAAC;QAChB;MACJ,CAAC;IACL,CAAC,CAAC;EACN;EACA+N,aAAaA,CAACxU,SAAS,EAAE;IACrB,OAAO,IAAI,CAACoU,OAAO,CAACR,MAAM,CAAClI,OAAO,CAAC1L,SAAS,CAAC,GAAG,CAAC,CAAC;EACtD;EACA;IAAS,IAAI,CAACrB,IAAI,YAAAsW,6BAAApW,iBAAA;MAAA,YAAAA,iBAAA,IAAyFsV,oBAAoB,EA1iD9Bzd,EAAE,CAAA+J,QAAA,CA0iD8CnK,QAAQ,GA1iDxDI,EAAE,CAAA+J,QAAA,CA0iDmEgT,qBAAqB,GA1iD1F/c,EAAE,CAAA+J,QAAA,CA0iDqG/J,EAAE,CAAC6B,QAAQ,GA1iDlH7B,EAAE,CAAA+J,QAAA,CA0iD6HiT,aAAa;IAAA,CAA6D;EAAE;EAC5S;IAAS,IAAI,CAAC5U,KAAK,kBA3iD8EpI,EAAE,CAAAqI,kBAAA;MAAAC,KAAA,EA2iDYmV,oBAAoB;MAAAlV,OAAA,EAApBkV,oBAAoB,CAAAxV;IAAA,EAAG;EAAE;AAC5I;AACA;EAAA,QAAAnB,SAAA,oBAAAA,SAAA,KA7iDqG9G,EAAE,CAAAwI,iBAAA,CA6iDXiV,oBAAoB,EAAc,CAAC;IACnHhV,IAAI,EAAEtI;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEsI,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CxB,IAAI,EAAEpI,MAAM;MACZ6J,IAAI,EAAE,CAACtK,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAE6I,IAAI,EAAEwU,mBAAmB;IAAEhT,UAAU,EAAE,CAAC;MAC5CxB,IAAI,EAAEpI,MAAM;MACZ6J,IAAI,EAAE,CAAC6S,qBAAqB;IAChC,CAAC;EAAE,CAAC,EAAE;IAAEtU,IAAI,EAAEzI,EAAE,CAAC6B;EAAS,CAAC,EAAE;IAAE4G,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MACzDxB,IAAI,EAAEhI;IACV,CAAC,EAAE;MACCgI,IAAI,EAAEpI,MAAM;MACZ6J,IAAI,EAAE,CAAC8S,aAAa;IACxB,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwB,YAAY,CAAC;EACf;IAAS,IAAI,CAACvW,IAAI,YAAAwW,qBAAAtW,iBAAA;MAAA,YAAAA,iBAAA,IAAyFqW,YAAY;IAAA,CAAkD;EAAE;EAC3K;IAAS,IAAI,CAAC7G,IAAI,kBAxkD+E3X,EAAE,CAAA4X,gBAAA;MAAAnP,IAAA,EAwkDS+V;IAAY,EAAG;EAAE;EAC7H;IAAS,IAAI,CAAC3G,IAAI,kBAzkD+E7X,EAAE,CAAA8X,gBAAA;MAAA1B,SAAA,EAykDkC,CAC7H;QACIQ,OAAO,EAAElO,qBAAqB;QAC9ByO,QAAQ,EAAEsG,oBAAoB;QAC9B3G,KAAK,EAAE,IAAI;QACXE,IAAI,EAAE,CAACpX,QAAQ,EAAEmd,qBAAqB,EAAElb,QAAQ,EAAE,CAAC,IAAIpB,QAAQ,CAAC,CAAC,EAAEuc,aAAa,CAAC;MACrF,CAAC,EACD;QAAEpG,OAAO,EAAEmG,qBAAqB;QAAE5F,QAAQ,EAAE8F,mBAAmB;QAAEjG,IAAI,EAAE;MAAG,CAAC;IAC9E,EAAG;EAAE;AACd;AACA;EAAA,QAAAlQ,SAAA,oBAAAA,SAAA,KAnlDqG9G,EAAE,CAAAwI,iBAAA,CAmlDXgW,YAAY,EAAc,CAAC;IAC3G/V,IAAI,EAAE/G,QAAQ;IACdwI,IAAI,EAAE,CAAC;MACCkM,SAAS,EAAE,CACP;QACIQ,OAAO,EAAElO,qBAAqB;QAC9ByO,QAAQ,EAAEsG,oBAAoB;QAC9B3G,KAAK,EAAE,IAAI;QACXE,IAAI,EAAE,CAACpX,QAAQ,EAAEmd,qBAAqB,EAAElb,QAAQ,EAAE,CAAC,IAAIpB,QAAQ,CAAC,CAAC,EAAEuc,aAAa,CAAC;MACrF,CAAC,EACD;QAAEpG,OAAO,EAAEmG,qBAAqB;QAAE5F,QAAQ,EAAE8F,mBAAmB;QAAEjG,IAAI,EAAE;MAAG,CAAC;IAEnF,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0H,YAAY,CAAC;EACf;IAAS,IAAI,CAACzW,IAAI,YAAA0W,qBAAAxW,iBAAA;MAAA,YAAAA,iBAAA,IAAyFuW,YAAY;IAAA,CAAoD;EAAE;EAC7K;IAAS,IAAI,CAACtW,KAAK,kBAnoD8EpI,EAAE,CAAAqI,kBAAA;MAAAC,KAAA,EAmoDYoW,YAAY;MAAAnW,OAAA,WAAAoW,qBAAAxW,iBAAA;QAAA,IAAAyW,wBAAA;QAAA,IAAAzW,iBAAA;UAAAyW,wBAAA,QAAAzW,iBAAA,IAAZuW,YAAY;QAAA;UAAAE,wBAAA,GAnoD1B5e,EAAE,CAAA+J,QAAA,CAmoD+E8U,gBAAgB;QAAA;QAAA,OAAAD,wBAAA;MAAA;MAAA/Q,UAAA,EAAzD;IAAM,EAAuD;EAAE;AAC5M;AACA;EAAA,QAAA/G,SAAA,oBAAAA,SAAA,KAroDqG9G,EAAE,CAAAwI,iBAAA,CAqoDXkW,YAAY,EAAc,CAAC;IAC3GjW,IAAI,EAAEtI,UAAU;IAChB+J,IAAI,EAAE,CAAC;MAAE2D,UAAU,EAAE,MAAM;MAAEuJ,WAAW,EAAEtV,UAAU,CAAC,MAAM+c,gBAAgB;IAAE,CAAC;EAClF,CAAC,CAAC;AAAA;AACV,MAAMA,gBAAgB,SAASH,YAAY,CAAC;EACxCrb,WAAWA,CAAC+G,IAAI,EAAE;IACd,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACA0U,QAAQA,CAACC,GAAG,EAAE5O,KAAK,EAAE;IACjB,IAAIA,KAAK,IAAI,IAAI,EACb,OAAO,IAAI;IACf,QAAQ4O,GAAG;MACP,KAAK/c,eAAe,CAACgd,IAAI;QACrB,OAAO7O,KAAK;MAChB,KAAKnO,eAAe,CAACid,IAAI;QACrB,IAAIhd,gCAAgC,CAACkO,KAAK,EAAE,MAAM,CAAC,qBAAqB,CAAC,EAAE;UACvE,OAAOjO,gBAAgB,CAACiO,KAAK,CAAC;QAClC;QACA,OAAO/N,cAAc,CAAC,IAAI,CAACgI,IAAI,EAAE8U,MAAM,CAAC/O,KAAK,CAAC,CAAC,CAACgP,QAAQ,CAAC,CAAC;MAC9D,KAAKnd,eAAe,CAACod,KAAK;QACtB,IAAInd,gCAAgC,CAACkO,KAAK,EAAE,OAAO,CAAC,sBAAsB,CAAC,EAAE;UACzE,OAAOjO,gBAAgB,CAACiO,KAAK,CAAC;QAClC;QACA,OAAOA,KAAK;MAChB,KAAKnO,eAAe,CAACqd,MAAM;QACvB,IAAIpd,gCAAgC,CAACkO,KAAK,EAAE,QAAQ,CAAC,uBAAuB,CAAC,EAAE;UAC3E,OAAOjO,gBAAgB,CAACiO,KAAK,CAAC;QAClC;QACA,MAAM,IAAIjQ,aAAa,CAAC,IAAI,CAAC,mDAAmD,CAAC,OAAO4G,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC1H,uCAAuC,CAAC;MAChD,KAAK9E,eAAe,CAACoE,GAAG;QACpB,IAAInE,gCAAgC,CAACkO,KAAK,EAAE,KAAK,CAAC,oBAAoB,CAAC,EAAE;UACrE,OAAOjO,gBAAgB,CAACiO,KAAK,CAAC;QAClC;QACA,OAAOhO,aAAa,CAAC+c,MAAM,CAAC/O,KAAK,CAAC,CAAC;MACvC,KAAKnO,eAAe,CAACsd,YAAY;QAC7B,IAAIrd,gCAAgC,CAACkO,KAAK,EAAE,aAAa,CAAC,4BAA4B,CAAC,EAAE;UACrF,OAAOjO,gBAAgB,CAACiO,KAAK,CAAC;QAClC;QACA,MAAM,IAAIjQ,aAAa,CAAC,IAAI,CAAC,yDAAyD,CAAC,OAAO4G,SAAS,KAAK,WAAW,IAAIA,SAAS,KAChI,oDAAoD/E,iBAAiB,GAAG,CAAC;MACjF;QACI,MAAM,IAAI7B,aAAa,CAAC,IAAI,CAAC,oDAAoD,CAAC,OAAO4G,SAAS,KAAK,WAAW,IAAIA,SAAS,KAC3H,8BAA8BiY,GAAG,SAAShd,iBAAiB,GAAG,CAAC;IAC3E;EACJ;EACAwd,uBAAuBA,CAACpP,KAAK,EAAE;IAC3B,OAAO9N,4BAA4B,CAAC8N,KAAK,CAAC;EAC9C;EACAqP,wBAAwBA,CAACrP,KAAK,EAAE;IAC5B,OAAO7N,6BAA6B,CAAC6N,KAAK,CAAC;EAC/C;EACAsP,yBAAyBA,CAACtP,KAAK,EAAE;IAC7B,OAAO5N,8BAA8B,CAAC4N,KAAK,CAAC;EAChD;EACAuP,sBAAsBA,CAACvP,KAAK,EAAE;IAC1B,OAAO3N,2BAA2B,CAAC2N,KAAK,CAAC;EAC7C;EACAwP,8BAA8BA,CAACxP,KAAK,EAAE;IAClC,OAAO1N,mCAAmC,CAAC0N,KAAK,CAAC;EACrD;EACA;IAAS,IAAI,CAAClI,IAAI,YAAA2X,yBAAAzX,iBAAA;MAAA,YAAAA,iBAAA,IAAyF0W,gBAAgB,EAnsD1B7e,EAAE,CAAA+J,QAAA,CAmsD0CnK,QAAQ;IAAA,CAA6C;EAAE;EACpM;IAAS,IAAI,CAACwI,KAAK,kBApsD8EpI,EAAE,CAAAqI,kBAAA;MAAAC,KAAA,EAosDYuW,gBAAgB;MAAAtW,OAAA,EAAhBsW,gBAAgB,CAAA5W,IAAA;MAAA4F,UAAA,EAAc;IAAM,EAAG;EAAE;AAC5J;AACA;EAAA,QAAA/G,SAAA,oBAAAA,SAAA,KAtsDqG9G,EAAE,CAAAwI,iBAAA,CAssDXqW,gBAAgB,EAAc,CAAC;IAC/GpW,IAAI,EAAEtI,UAAU;IAChB+J,IAAI,EAAE,CAAC;MAAE2D,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEpF,IAAI,EAAEuB,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/CxB,IAAI,EAAEpI,MAAM;MACZ6J,IAAI,EAAE,CAACtK,QAAQ;IACnB,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA,IAAIigB,oBAAoB;AACxB,CAAC,UAAUA,oBAAoB,EAAE;EAC7BA,oBAAoB,CAACA,oBAAoB,CAAC,qBAAqB,CAAC,GAAG,CAAC,CAAC,GAAG,qBAAqB;EAC7FA,oBAAoB,CAACA,oBAAoB,CAAC,0BAA0B,CAAC,GAAG,CAAC,CAAC,GAAG,0BAA0B;EACvGA,oBAAoB,CAACA,oBAAoB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;EAC7EA,oBAAoB,CAACA,oBAAoB,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,GAAG,aAAa;AACjF,CAAC,EAAEA,oBAAoB,KAAKA,oBAAoB,GAAG,CAAC,CAAC,CAAC,CAAC;AACvD;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,KAAK,EAAEC,UAAU,GAAG,EAAE,EAAEC,QAAQ,GAAG,CAAC,CAAC,EAAE;EAC7D,OAAO;IAAEF,KAAK;IAAEC;EAAW,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,uBAAuBA,CAAA,EAAG;EAC/B;EACA;EACA,OAAOJ,gBAAgB,CAACD,oBAAoB,CAACM,mBAAmB,CAAC;AACrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,4BAA4BA,CAACrK,OAAO,EAAE;EAC3C;EACA,OAAO+J,gBAAgB,CAACD,oBAAoB,CAACQ,wBAAwB,EAAEld,sBAAsB,CAAC4S,OAAO,CAAC,CAAC;AAC3G;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuK,eAAeA,CAAA,EAAG;EACvB,OAAOR,gBAAgB,CAACD,oBAAoB,CAACU,WAAW,EAAE7d,gBAAgB,CAAC,CAAC,CAAC;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8d,eAAeA,CAAA,EAAG;EACvB,OAAOV,gBAAgB,CAACD,oBAAoB,CAACY,WAAW,EAAE9d,gBAAgB,CAAC,CAAC,CAAC;AACjF;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+d,kCAAkCA,CAAA,EAAG;EAC1C,OAAO,CACH;IACI9J,OAAO,EAAEhU,uBAAuB;IAChCiU,QAAQ,EAAEA,CAAA,KAAM;MACZ,MAAMpI,MAAM,GAAG5L,MAAM,CAACxB,MAAM,CAAC;MAC7B,MAAMsf,UAAU,GAAG9d,MAAM,CAACC,iBAAiB,CAAC;MAC5C;MACA;MACA,IAAI,CAAC6d,UAAU,IAAIlS,MAAM,CAACpL,WAAW,KAAKhC,MAAM,EAAE;QAC9C,MAAM+Z,OAAO,GAAGvY,MAAM,CAAChB,QAAQ,CAAC;QAChC,MAAM+e,OAAO,GAAG7d,mBAAmB,CAAC,CAAC,IAAI,CAAC,oDAAoD,iEAAiE,GAC3J,uDAAuD,GACvD,kDAAkD,CAAC;QACvD;QACAqY,OAAO,CAAC2C,IAAI,CAAC6C,OAAO,CAAC;MACzB;IACJ,CAAC;IACD9J,KAAK,EAAE;EACX,CAAC,CACJ;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+J,sBAAsBA,CAAC,GAAGC,QAAQ,EAAE;EACzC,MAAM1K,SAAS,GAAG,EAAE;EACpB,MAAM2K,YAAY,GAAG,IAAInW,GAAG,CAAC,CAAC;EAC9B,MAAMoW,2BAA2B,GAAGD,YAAY,CAACrU,GAAG,CAACmT,oBAAoB,CAACQ,wBAAwB,CAAC;EACnG,KAAK,MAAM;IAAEL,UAAU;IAAED;EAAM,CAAC,IAAIe,QAAQ,EAAE;IAC1CC,YAAY,CAACjV,GAAG,CAACiU,KAAK,CAAC;IACvB,IAAIC,UAAU,CAAC3Y,MAAM,EAAE;MACnB+O,SAAS,CAAC3O,IAAI,CAACuY,UAAU,CAAC;IAC9B;EACJ;EACA,IAAI,OAAOlZ,SAAS,KAAK,WAAW,IAChCA,SAAS,IACTia,YAAY,CAACrU,GAAG,CAACmT,oBAAoB,CAACM,mBAAmB,CAAC,IAC1Da,2BAA2B,EAAE;IAC7B;IACA,MAAM,IAAI1O,KAAK,CAAC,sKAAsK,CAAC;EAC3L;EACA,OAAOtP,wBAAwB,CAAC,CAC5B,OAAO8D,SAAS,KAAK,WAAW,IAAIA,SAAS,GAAG4Z,kCAAkC,CAAC,CAAC,GAAG,EAAE,EACzFzd,iBAAiB,CAAC,CAAC,EACnB8d,YAAY,CAACrU,GAAG,CAACmT,oBAAoB,CAACM,mBAAmB,CAAC,IAAIa,2BAA2B,GACnF,EAAE,GACF7d,sBAAsB,CAAC,CAAC,CAAC,CAAC,EAChCiT,SAAS,CACZ,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6K,OAAO,GAAG,IAAI/d,OAAO,CAAC,SAAS,CAAC;;AAEtC;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,SAASmU,aAAa,EAAE4E,EAAE,EAAEyC,YAAY,EAAEhW,qBAAqB,EAAEC,YAAY,EAAEwB,kBAAkB,EAAE4S,qBAAqB,EAAEC,aAAa,EAAEC,mBAAmB,EAAEuB,YAAY,EAAEqB,oBAAoB,EAAE5H,IAAI,EAAErK,kCAAkC,EAAEoM,KAAK,EAAEiH,OAAO,EAAEpL,oBAAoB,EAAEI,iBAAiB,EAAE+F,iBAAiB,EAAED,gBAAgB,EAAE9E,eAAe,EAAE4J,sBAAsB,EAAEtK,+BAA+B,EAAEiK,eAAe,EAAEJ,4BAA4B,EAAEE,eAAe,EAAEJ,uBAAuB,EAAE1c,iBAAiB,IAAI0d,kBAAkB,EAAE3a,qBAAqB,IAAI4a,sBAAsB,EAAE5N,eAAe,IAAI6N,gBAAgB,EAAE/S,mBAAmB,IAAIgT,oBAAoB,EAAExC,gBAAgB,IAAIyC,iBAAiB,EAAE7D,oBAAoB,IAAI8D,qBAAqB,EAAEjL,mCAAmC,IAAIkL,oCAAoC,EAAExN,eAAe,IAAIyN,gBAAgB,EAAEnX,gBAAgB,IAAIoX,iBAAiB,EAAEjL,cAAc,IAAIkL,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}