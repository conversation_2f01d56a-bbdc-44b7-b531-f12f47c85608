/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { AnimationMetadataType } from '@angular/animations';
import { AnimationStateStyles, AnimationTransitionFactory } from './animation_transition_factory';
export function buildTrigger(name, ast, normalizer) {
    return new AnimationTrigger(name, ast, normalizer);
}
export class AnimationTrigger {
    constructor(name, ast, _normalizer) {
        this.name = name;
        this.ast = ast;
        this._normalizer = _normalizer;
        this.transitionFactories = [];
        this.states = new Map();
        ast.states.forEach((ast) => {
            const defaultParams = (ast.options && ast.options.params) || {};
            this.states.set(ast.name, new AnimationStateStyles(ast.style, defaultParams, _normalizer));
        });
        balanceProperties(this.states, 'true', '1');
        balanceProperties(this.states, 'false', '0');
        ast.transitions.forEach((ast) => {
            this.transitionFactories.push(new AnimationTransitionFactory(name, ast, this.states));
        });
        this.fallbackTransition = createFallbackTransition(name, this.states, this._normalizer);
    }
    get containsQueries() {
        return this.ast.queryCount > 0;
    }
    matchTransition(currentState, nextState, element, params) {
        const entry = this.transitionFactories.find((f) => f.match(currentState, nextState, element, params));
        return entry || null;
    }
    matchStyles(currentState, params, errors) {
        return this.fallbackTransition.buildStyles(currentState, params, errors);
    }
}
function createFallbackTransition(triggerName, states, normalizer) {
    const matchers = [(fromState, toState) => true];
    const animation = { type: AnimationMetadataType.Sequence, steps: [], options: null };
    const transition = {
        type: AnimationMetadataType.Transition,
        animation,
        matchers,
        options: null,
        queryCount: 0,
        depCount: 0,
    };
    return new AnimationTransitionFactory(triggerName, transition, states);
}
function balanceProperties(stateMap, key1, key2) {
    if (stateMap.has(key1)) {
        if (!stateMap.has(key2)) {
            stateMap.set(key2, stateMap.get(key1));
        }
    }
    else if (stateMap.has(key2)) {
        stateMap.set(key1, stateMap.get(key2));
    }
}
//# sourceMappingURL=data:application/json;base64,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