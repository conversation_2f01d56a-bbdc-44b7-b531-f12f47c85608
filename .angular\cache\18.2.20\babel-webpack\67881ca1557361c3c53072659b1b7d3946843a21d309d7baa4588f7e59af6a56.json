{"ast": null, "code": "import { bootstrapApplication } from '@angular/platform-browser';\nimport { provideRouter } from '@angular/router';\nimport { AppComponent } from './app/app.component';\nimport { routes } from './app/app.routes';\nbootstrapApplication(AppComponent, {\n  providers: [provideRouter(routes)]\n}).catch(err => console.error(err));", "map": {"version": 3, "names": ["bootstrapApplication", "provideRouter", "AppComponent", "routes", "providers", "catch", "err", "console", "error"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\1st-Monthsary-Tangi\\monthsary-website\\src\\main.ts"], "sourcesContent": ["import { bootstrapApplication } from '@angular/platform-browser';\nimport { provideRouter } from '@angular/router';\nimport { AppComponent } from './app/app.component';\nimport { routes } from './app/app.routes';\n\nbootstrapApplication(AppComponent, {\n  providers: [\n    provideRouter(routes)\n  ]\n}).catch(err => console.error(err));\n"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,YAAY,QAAQ,qBAAqB;AAClD,SAASC,MAAM,QAAQ,kBAAkB;AAEzCH,oBAAoB,CAACE,YAAY,EAAE;EACjCE,SAAS,EAAE,CACTH,aAAa,CAACE,MAAM,CAAC;CAExB,CAAC,CAACE,KAAK,CAACC,GAAG,IAAIC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}