@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Poppins', sans-serif;
  background: linear-gradient(135deg, #fdf7f7 0%, #f9f4ff 50%, #fff8f8 100%);
  min-height: 100vh;
  overflow-x: hidden;
  line-height: 1.6;
}

/* Custom CSS Variables */
:root {
  --romantic-pink: #f8d7da;
  --soft-lavender: #e8d5ff;
  --warm-cream: #faf8f5;
  --deep-rose: #d63384;
  --gentle-purple: #b794f6;
  --cream-white: #fffffe;
  --text-dark: #2d3748;
  --text-medium: #4a5568;
  --text-light: #718096;
  --accent-gold: #f6e05e;
  --shadow-soft: rgba(0, 0, 0, 0.08);
  --shadow-medium: rgba(0, 0, 0, 0.12);
}

/* Utility classes */
.text-romantic {
  background: linear-gradient(135deg, var(--deep-rose), var(--gentle-purple));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 600;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px var(--shadow-soft);
}

.card-hover {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px var(--shadow-medium);
}

/* 3D flip card effect */
.flip-card {
  perspective: 1200px;
}

.flip-card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transform-style: preserve-3d;
}

.flip-card:hover .flip-card-inner {
  transform: rotateY(180deg);
}

.flip-card-front, .flip-card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  border-radius: 1.5rem;
  box-shadow: 0 10px 30px var(--shadow-soft);
}

.flip-card-back {
  transform: rotateY(180deg);
}

/* Mobile responsive improvements */
@media (max-width: 640px) {
  .flip-card:active .flip-card-inner {
    transform: rotateY(180deg);
  }

  .glass-effect {
    backdrop-filter: blur(15px);
  }
}

/* Smooth transitions for all interactive elements */
button, .card-hover, .flip-card-inner {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Animation delay utilities */
.animation-delay-200 {
  animation-delay: 0.2s;
}

.animation-delay-400 {
  animation-delay: 0.4s;
}

.animation-delay-500 {
  animation-delay: 0.5s;
}

.animation-delay-1000 {
  animation-delay: 1s;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--cream-white);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--romantic-pink), var(--soft-lavender));
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--deep-rose), var(--gentle-purple));
}

/* Loading Screen Animations */
@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes gentle-float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.6;
  }
}

@keyframes soft-pulse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes gentle-glow {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.1);
  }
}

@keyframes loading-progress {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

@keyframes fade-in-out {
  0%, 100% {
    opacity: 0.7;
  }
  50% {
    opacity: 1;
  }
}

@keyframes music-progress {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}

/* Apply animations */
.animate-heartbeat {
  animation: heartbeat 2s ease-in-out infinite;
}

.animate-gentle-float {
  animation: gentle-float 6s ease-in-out infinite;
}

.animate-soft-pulse {
  animation: soft-pulse 3s ease-in-out infinite;
}

.animate-gentle-glow {
  animation: gentle-glow 4s ease-in-out infinite;
}

.animate-loading-progress {
  animation: loading-progress 5s ease-out forwards;
}

.animate-fade-in-out {
  animation: fade-in-out 2s ease-in-out infinite;
}

/* Music progress is now handled dynamically via Angular */

/* Music Player Enhancements */
.music-player-glow {
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.2);
}

.music-player-glow:hover {
  box-shadow: 0 0 30px rgba(34, 197, 94, 0.3);
}
