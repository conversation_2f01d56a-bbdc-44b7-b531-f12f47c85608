import { Component, OnInit } from '@angular/core';
import { RouterOutlet, RouterLink, RouterLinkActive } from '@angular/router';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [RouterOutlet, RouterLink, RouterLinkActive, CommonModule],
  template: `
    <!-- Loading Screen -->
    <div *ngIf="isLoading" class="fixed inset-0 z-[9999] flex items-center justify-center bg-gradient-to-br from-pink-50 via-purple-50 to-pink-100">
      <!-- Animated Background -->
      <div class="absolute inset-0 overflow-hidden">
        <!-- Floating Hearts Background -->
        @for (heart of loadingHearts; track heart.id) {
          <div
            class="absolute text-pink-200 opacity-20 animate-gentle-float"
            [style.left.%]="heart.left"
            [style.top.%]="heart.top"
            [style.animation-delay]="heart.delay + 's'"
            [style.font-size.px]="heart.size">
            💖
          </div>
        }

        <!-- Glowing Orbs -->
        <div class="absolute top-1/4 left-1/4 w-64 h-64 bg-pink-200 rounded-full mix-blend-multiply filter blur-3xl opacity-40 animate-gentle-glow"></div>
        <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-purple-200 rounded-full mix-blend-multiply filter blur-3xl opacity-40 animate-gentle-glow animation-delay-2000"></div>
      </div>

      <!-- Loading Content -->
      <div class="relative z-10 text-center px-6">
        <!-- Main Loading Animation -->
        <div class="mb-8">
          <div class="relative">
            <!-- Pulsing Heart -->
            <div class="text-8xl animate-heartbeat mb-4">💖</div>

            <!-- Loading Ring -->
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="w-32 h-32 border-4 border-pink-200 border-t-pink-400 rounded-full animate-spin"></div>
            </div>
          </div>
        </div>

        <!-- Loading Text -->
        <h1 class="font-dancing text-4xl sm:text-5xl text-romantic mb-4 animate-soft-pulse">
          Loading Our Love Story...
        </h1>

        <div class="flex justify-center space-x-2 mb-8">
          <div class="w-2 h-2 bg-pink-400 rounded-full animate-bounce"></div>
          <div class="w-2 h-2 bg-purple-400 rounded-full animate-bounce animation-delay-200"></div>
          <div class="w-2 h-2 bg-pink-400 rounded-full animate-bounce animation-delay-400"></div>
        </div>

        <!-- Progress Bar -->
        <div class="w-64 h-2 bg-white/30 rounded-full mx-auto mb-6 overflow-hidden">
          <div class="h-full bg-gradient-to-r from-pink-400 to-purple-400 rounded-full animate-loading-progress"></div>
        </div>

        <!-- Cute Loading Messages -->
        <p class="font-poppins text-gray-600 text-lg animate-fade-in-out">
          {{ loadingMessages[currentMessageIndex] }}
        </p>
      </div>
    </div>

    <!-- Main App Content -->
    <div *ngIf="!isLoading" class="min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-pink-100">
      <!-- Spotify Music Player -->
      <div class="fixed bottom-6 right-6 z-40">
        <div class="glass-effect rounded-2xl p-4 shadow-lg border border-white/20 max-w-sm transition-all duration-300"
             [class.music-player-glow]="isPlaying">
          <div class="flex items-center space-x-3">
            <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-xl flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.42 1.56-.299.421-1.02.599-1.559.3z"/>
              </svg>
            </div>
            <div class="flex-1 min-w-0">
              <p class="font-poppins text-sm font-medium text-gray-800 truncate">Now Playing</p>
              <p class="font-poppins text-xs text-gray-600 truncate">Bawat Daan</p>
              <p class="font-poppins text-xs text-gray-500 truncate">Ebe Dancel • Our Special Song 💕</p>
            </div>
            <button
              (click)="togglePlayPause()"
              class="w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-sm hover:shadow-md transition-all duration-200 hover:scale-105">
              <!-- Play Icon -->
              <svg *ngIf="!isPlaying" class="w-4 h-4 text-gray-700 ml-0.5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M8 5v14l11-7z"/>
              </svg>
              <!-- Pause Icon -->
              <svg *ngIf="isPlaying" class="w-4 h-4 text-gray-700" fill="currentColor" viewBox="0 0 24 24">
                <path d="M6 4h4v16H6V4zm8 0h4v16h-4V4z"/>
              </svg>
            </button>
          </div>
          <div class="mt-3">
            <div class="flex items-center space-x-2">
              <span class="text-xs text-gray-500">{{ formatTime(currentTime) }}</span>
              <div class="flex-1 h-1 bg-gray-200 rounded-full overflow-hidden">
                <div
                  class="h-full bg-gradient-to-r from-green-400 to-green-600 rounded-full transition-all duration-300"
                  [style.width.%]="progressPercentage">
                </div>
              </div>
              <span class="text-xs text-gray-500">{{ formatTime(duration) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Navigation -->
      <nav class="fixed top-0 left-0 right-0 z-50 glass-effect border-b border-white/20">
        <div class="max-w-6xl mx-auto px-6 py-4">
          <div class="flex items-center justify-between">
            <!-- Logo -->
            <div class="flex items-center space-x-2">
              <span class="text-2xl animate-soft-pulse">💖</span>
              <span class="font-dancing text-2xl text-romantic">Our Love Story</span>
            </div>
            
            <!-- Navigation Links -->
            <div class="hidden md:flex items-center space-x-8">
              <a routerLink="/" 
                 routerLinkActive="text-romantic font-semibold" 
                 [routerLinkActiveOptions]="{exact: true}"
                 class="font-poppins text-gray-600 hover:text-romantic transition-colors duration-300">
                Home
              </a>
              <a routerLink="/our-story" 
                 routerLinkActive="text-romantic font-semibold"
                 class="font-poppins text-gray-600 hover:text-romantic transition-colors duration-300">
                Our Story
              </a>
              <a routerLink="/love-notes" 
                 routerLinkActive="text-romantic font-semibold"
                 class="font-poppins text-gray-600 hover:text-romantic transition-colors duration-300">
                Love Notes
              </a>
              <a routerLink="/memories" 
                 routerLinkActive="text-romantic font-semibold"
                 class="font-poppins text-gray-600 hover:text-romantic transition-colors duration-300">
                Memories
              </a>
            </div>
            
            <!-- Mobile Menu Button -->
            <button (click)="toggleMobileMenu()" class="md:hidden p-2">
              <span class="text-2xl">💕</span>
            </button>
          </div>
          
          <!-- Mobile Menu -->
          <div *ngIf="mobileMenuOpen" class="md:hidden mt-4 pb-4 border-t border-white/20 pt-4">
            <div class="flex flex-col space-y-4">
              <a routerLink="/" 
                 (click)="closeMobileMenu()"
                 routerLinkActive="text-romantic font-semibold" 
                 [routerLinkActiveOptions]="{exact: true}"
                 class="font-poppins text-gray-600 hover:text-romantic transition-colors duration-300">
                Home
              </a>
              <a routerLink="/our-story" 
                 (click)="closeMobileMenu()"
                 routerLinkActive="text-romantic font-semibold"
                 class="font-poppins text-gray-600 hover:text-romantic transition-colors duration-300">
                Our Story
              </a>
              <a routerLink="/love-notes" 
                 (click)="closeMobileMenu()"
                 routerLinkActive="text-romantic font-semibold"
                 class="font-poppins text-gray-600 hover:text-romantic transition-colors duration-300">
                Love Notes
              </a>
              <a routerLink="/memories" 
                 (click)="closeMobileMenu()"
                 routerLinkActive="text-romantic font-semibold"
                 class="font-poppins text-gray-600 hover:text-romantic transition-colors duration-300">
                Memories
              </a>
            </div>
          </div>
        </div>
      </nav>
      
      <!-- Main Content -->
      <main class="pt-20">
        <router-outlet></router-outlet>
      </main>
      
      <!-- Footer -->
      <footer class="bg-gradient-to-r from-pink-200 via-purple-200 to-pink-200 text-gray-700 py-12 text-center relative overflow-hidden">
        <div class="absolute inset-0">
          <div class="absolute top-0 left-1/4 w-32 h-32 bg-white rounded-full mix-blend-multiply filter blur-2xl opacity-40 animate-gentle-glow"></div>
          <div class="absolute bottom-0 right-1/4 w-40 h-40 bg-white rounded-full mix-blend-multiply filter blur-2xl opacity-40 animate-gentle-glow animation-delay-2000"></div>
        </div>
        
        <div class="relative z-10 px-6 sm:px-8 lg:px-12">
          <p class="font-dancing text-2xl sm:text-3xl lg:text-4xl mb-4 text-romantic">
            Made with 💖 by Your Coding Boyfriend
          </p>
          <p class="font-poppins text-base sm:text-lg opacity-80 mb-6 font-light">
            Happy 1st Monthsary, My Love!
          </p>
          <div class="flex justify-center space-x-6 text-xl sm:text-2xl">
            <span class="animate-soft-pulse">💻</span>
            <span class="animate-soft-pulse animation-delay-500">💕</span>
            <span class="animate-soft-pulse animation-delay-1000">✨</span>
          </div>
        </div>
      </footer>
    </div>
  `,
  styles: []
})
export class AppComponent implements OnInit {
  mobileMenuOpen = false;
  isLoading = true;
  currentMessageIndex = 0;

  // Audio player properties
  audio: HTMLAudioElement | null = null;
  isPlaying = false;
  currentTime = 0;
  duration = 300; // 5:00 in seconds
  progressPercentage = 0;

  loadingMessages = [
    "Preparing our special moments... 💕",
    "Gathering all the love notes... 💌",
    "Setting up the perfect atmosphere... ✨",
    "Almost ready to celebrate... 🎉",
    "Loading our beautiful memories... 📸"
  ];

  loadingHearts = Array.from({ length: 15 }, (_, i) => ({
    id: i,
    left: Math.random() * 100,
    top: Math.random() * 100,
    size: Math.random() * 20 + 20,
    delay: Math.random() * 5
  }));

  ngOnInit() {
    // Initialize audio
    this.initializeAudio();

    // Cycle through loading messages
    const messageInterval = setInterval(() => {
      this.currentMessageIndex = (this.currentMessageIndex + 1) % this.loadingMessages.length;
    }, 1000);

    // Hide loading screen after 5 seconds
    setTimeout(() => {
      this.isLoading = false;
      clearInterval(messageInterval);
    }, 5000);
  }

  initializeAudio() {
    this.audio = new Audio('assets/audio/Ebe Dancel - Bawat Daan (Lyrics).mp3');
    this.audio.loop = true;

    // Update progress
    this.audio.addEventListener('timeupdate', () => {
      if (this.audio) {
        this.currentTime = this.audio.currentTime;
        this.progressPercentage = (this.currentTime / this.duration) * 100;
      }
    });

    // Handle audio loaded
    this.audio.addEventListener('loadedmetadata', () => {
      if (this.audio) {
        this.duration = this.audio.duration;
      }
    });
  }

  togglePlayPause() {
    if (!this.audio) return;

    if (this.isPlaying) {
      this.audio.pause();
    } else {
      this.audio.play().catch(error => {
        console.log('Audio play failed:', error);
      });
    }
    this.isPlaying = !this.isPlaying;
  }

  formatTime(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  toggleMobileMenu() {
    this.mobileMenuOpen = !this.mobileMenuOpen;
  }

  closeMobileMenu() {
    this.mobileMenuOpen = false;
  }
}
