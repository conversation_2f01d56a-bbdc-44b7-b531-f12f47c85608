{"ast": null, "code": "import { RouterOutlet, RouterLink, RouterLinkActive } from '@angular/router';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nconst _forTrack0 = ($index, $item) => $item.id;\nconst _c0 = () => ({\n  exact: true\n});\nfunction AppComponent_div_0_For_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21);\n    i0.ɵɵtext(1, \" \\uD83D\\uDC96 \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const heart_r1 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"left\", heart_r1.left, \"%\")(\"top\", heart_r1.top, \"%\")(\"animation-delay\", heart_r1.delay + \"s\")(\"font-size\", heart_r1.size, \"px\");\n  }\n}\nfunction AppComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3);\n    i0.ɵɵrepeaterCreate(2, AppComponent_div_0_For_3_Template, 2, 8, \"div\", 4, _forTrack0);\n    i0.ɵɵelement(4, \"div\", 5)(5, \"div\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 7)(7, \"div\", 8)(8, \"div\", 9)(9, \"div\", 10);\n    i0.ɵɵtext(10, \"\\uD83D\\uDC96\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 11);\n    i0.ɵɵelement(12, \"div\", 12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(13, \"h1\", 13);\n    i0.ɵɵtext(14, \" Loading Our Love Story... \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 14);\n    i0.ɵɵelement(16, \"div\", 15)(17, \"div\", 16)(18, \"div\", 17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 18);\n    i0.ɵɵelement(20, \"div\", 19);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"p\", 20);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵrepeater(ctx_r1.loadingHearts);\n    i0.ɵɵadvance(20);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.loadingMessages[ctx_r1.currentMessageIndex], \" \");\n  }\n}\nfunction AppComponent_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67)(1, \"div\", 68)(2, \"p\", 69)(3, \"span\");\n    i0.ɵɵtext(4, \"\\uD83C\\uDFB5\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\");\n    i0.ɵɵtext(6, \"Click play to start our song! \\uD83D\\uDC95\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction AppComponent_div_1__svg_svg_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 70);\n    i0.ɵɵelement(1, \"path\", 71);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppComponent_div_1__svg_svg_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"svg\", 72);\n    i0.ɵɵelement(1, \"path\", 73);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppComponent_div_1_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 75)(2, \"a\", 76);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_1_div_46_Template_a_click_2_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.closeMobileMenu());\n    });\n    i0.ɵɵtext(3, \" Home \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"a\", 77);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_1_div_46_Template_a_click_4_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.closeMobileMenu());\n    });\n    i0.ɵɵtext(5, \" Our Story \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"a\", 78);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_1_div_46_Template_a_click_6_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.closeMobileMenu());\n    });\n    i0.ɵɵtext(7, \" Love Notes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"a\", 79);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_1_div_46_Template_a_click_8_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.closeMobileMenu());\n    });\n    i0.ɵɵtext(9, \" Memories \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction0(1, _c0));\n  }\n}\nfunction AppComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 22);\n    i0.ɵɵtemplate(1, AppComponent_div_1_div_1_Template, 7, 0, \"div\", 23);\n    i0.ɵɵelementStart(2, \"div\", 24)(3, \"div\", 25)(4, \"div\", 26)(5, \"div\", 27);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(6, \"svg\", 28);\n    i0.ɵɵelement(7, \"path\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(8, \"div\", 30)(9, \"p\", 31);\n    i0.ɵɵtext(10, \"Now Playing\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"p\", 32);\n    i0.ɵɵtext(12, \"Bawat Daan\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"p\", 33);\n    i0.ɵɵtext(14, \"Ebe Dancel \\u2022 Our Special Song \\uD83D\\uDC95\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_1_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.togglePlayPause());\n    });\n    i0.ɵɵtemplate(16, AppComponent_div_1__svg_svg_16_Template, 2, 0, \"svg\", 35)(17, AppComponent_div_1__svg_svg_17_Template, 2, 0, \"svg\", 36);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 37)(19, \"div\", 38)(20, \"span\", 39);\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 40);\n    i0.ɵɵelement(23, \"div\", 41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\", 39);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(26, \"nav\", 42)(27, \"div\", 43)(28, \"div\", 44)(29, \"div\", 38)(30, \"span\", 45);\n    i0.ɵɵtext(31, \"\\uD83D\\uDC96\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\", 46);\n    i0.ɵɵtext(33, \"Our Love Story\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 47)(35, \"a\", 48);\n    i0.ɵɵtext(36, \" Home \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"a\", 49);\n    i0.ɵɵtext(38, \" Our Story \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"a\", 50);\n    i0.ɵɵtext(40, \" Love Notes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(41, \"a\", 51);\n    i0.ɵɵtext(42, \" Memories \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"button\", 52);\n    i0.ɵɵlistener(\"click\", function AppComponent_div_1_Template_button_click_43_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleMobileMenu());\n    });\n    i0.ɵɵelementStart(44, \"span\", 53);\n    i0.ɵɵtext(45, \"\\uD83D\\uDC95\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(46, AppComponent_div_1_div_46_Template, 10, 2, \"div\", 54);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"main\", 55);\n    i0.ɵɵelement(48, \"router-outlet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"footer\", 56)(50, \"div\", 57);\n    i0.ɵɵelement(51, \"div\", 58)(52, \"div\", 59);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"div\", 60)(54, \"p\", 61);\n    i0.ɵɵtext(55, \" Made with \\uD83D\\uDC96 by Your Coding Boyfriend \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(56, \"p\", 62);\n    i0.ɵɵtext(57, \" Happy 1st Monthsary, My Love! \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"div\", 63)(59, \"span\", 64);\n    i0.ɵɵtext(60, \"\\uD83D\\uDCBB\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"span\", 65);\n    i0.ɵɵtext(62, \"\\uD83D\\uDC95\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(63, \"span\", 66);\n    i0.ɵɵtext(64, \"\\u2728\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showPlayPromptMessage);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"music-player-glow\", ctx_r1.isPlaying)(\"animate-soft-pulse\", ctx_r1.showPlayPromptMessage && !ctx_r1.isPlaying);\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isPlaying);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isPlaying);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.formatTime(ctx_r1.currentTime));\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.progressPercentage, \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatTime(ctx_r1.duration));\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction0(13, _c0));\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.mobileMenuOpen);\n  }\n}\nexport class AppComponent {\n  constructor() {\n    this.mobileMenuOpen = false;\n    this.isLoading = true;\n    this.currentMessageIndex = 0;\n    // Audio player properties\n    this.audio = null;\n    this.isPlaying = false;\n    this.currentTime = 0;\n    this.duration = 300; // 5:00 in seconds\n    this.progressPercentage = 0;\n    this.showPlayPromptMessage = false;\n    this.loadingMessages = [\"Preparing our special moments... 💕\", \"Gathering all the love notes... 💌\", \"Setting up the perfect atmosphere... ✨\", \"Almost ready to celebrate... 🎉\", \"Loading our beautiful memories... 📸\"];\n    this.loadingHearts = Array.from({\n      length: 15\n    }, (_, i) => ({\n      id: i,\n      left: Math.random() * 100,\n      top: Math.random() * 100,\n      size: Math.random() * 20 + 20,\n      delay: Math.random() * 5\n    }));\n  }\n  ngOnInit() {\n    // Initialize audio\n    this.initializeAudio();\n    // Cycle through loading messages\n    const messageInterval = setInterval(() => {\n      this.currentMessageIndex = (this.currentMessageIndex + 1) % this.loadingMessages.length;\n    }, 1000);\n    // Hide loading screen after 5 seconds and auto-play music\n    setTimeout(() => {\n      this.isLoading = false;\n      clearInterval(messageInterval);\n      // Auto-play music after loading\n      this.autoPlayMusic();\n    }, 5000);\n  }\n  initializeAudio() {\n    this.audio = new Audio('assets/audio/Ebe Dancel - Bawat Daan (Lyrics).mp3');\n    this.audio.loop = true;\n    // Update progress\n    this.audio.addEventListener('timeupdate', () => {\n      if (this.audio) {\n        this.currentTime = this.audio.currentTime;\n        this.progressPercentage = this.currentTime / this.duration * 100;\n      }\n    });\n    // Handle audio loaded\n    this.audio.addEventListener('loadedmetadata', () => {\n      if (this.audio) {\n        this.duration = this.audio.duration;\n      }\n    });\n  }\n  togglePlayPause() {\n    if (!this.audio) return;\n    if (this.isPlaying) {\n      this.audio.pause();\n    } else {\n      this.audio.play().catch(error => {\n        console.log('Audio play failed:', error);\n      });\n    }\n    this.isPlaying = !this.isPlaying;\n  }\n  formatTime(seconds) {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n  autoPlayMusic() {\n    if (!this.audio) return;\n    // Try to auto-play the music\n    this.audio.play().then(() => {\n      this.isPlaying = true;\n      console.log('🎵 Auto-playing \"Bawat Daan\" - Your special song! 💕');\n    }).catch(() => {\n      console.log('Auto-play blocked by browser. Click play button to start music! 🎵');\n      // Show a subtle notification that user can click to play\n      this.showPlayPrompt();\n    });\n  }\n  showPlayPrompt() {\n    // Show a cute message encouraging the user to click play\n    this.showPlayPromptMessage = true;\n    // Hide the message after 5 seconds\n    setTimeout(() => {\n      this.showPlayPromptMessage = false;\n    }, 5000);\n  }\n  toggleMobileMenu() {\n    this.mobileMenuOpen = !this.mobileMenuOpen;\n  }\n  closeMobileMenu() {\n    this.mobileMenuOpen = false;\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || AppComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 2,\n      consts: [[\"class\", \"fixed inset-0 z-[9999] flex items-center justify-center bg-gradient-to-br from-pink-50 via-purple-50 to-pink-100\", 4, \"ngIf\"], [\"class\", \"min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-pink-100\", 4, \"ngIf\"], [1, \"fixed\", \"inset-0\", \"z-[9999]\", \"flex\", \"items-center\", \"justify-center\", \"bg-gradient-to-br\", \"from-pink-50\", \"via-purple-50\", \"to-pink-100\"], [1, \"absolute\", \"inset-0\", \"overflow-hidden\"], [1, \"absolute\", \"text-pink-200\", \"opacity-20\", \"animate-gentle-float\", 3, \"left\", \"top\", \"animation-delay\", \"font-size\"], [1, \"absolute\", \"top-1/4\", \"left-1/4\", \"w-64\", \"h-64\", \"bg-pink-200\", \"rounded-full\", \"mix-blend-multiply\", \"filter\", \"blur-3xl\", \"opacity-40\", \"animate-gentle-glow\"], [1, \"absolute\", \"bottom-1/4\", \"right-1/4\", \"w-80\", \"h-80\", \"bg-purple-200\", \"rounded-full\", \"mix-blend-multiply\", \"filter\", \"blur-3xl\", \"opacity-40\", \"animate-gentle-glow\", \"animation-delay-2000\"], [1, \"relative\", \"z-10\", \"text-center\", \"px-6\"], [1, \"mb-8\"], [1, \"relative\"], [1, \"text-8xl\", \"animate-heartbeat\", \"mb-4\"], [1, \"absolute\", \"inset-0\", \"flex\", \"items-center\", \"justify-center\"], [1, \"w-32\", \"h-32\", \"border-4\", \"border-pink-200\", \"border-t-pink-400\", \"rounded-full\", \"animate-spin\"], [1, \"font-dancing\", \"text-4xl\", \"sm:text-5xl\", \"text-romantic\", \"mb-4\", \"animate-soft-pulse\"], [1, \"flex\", \"justify-center\", \"space-x-2\", \"mb-8\"], [1, \"w-2\", \"h-2\", \"bg-pink-400\", \"rounded-full\", \"animate-bounce\"], [1, \"w-2\", \"h-2\", \"bg-purple-400\", \"rounded-full\", \"animate-bounce\", \"animation-delay-200\"], [1, \"w-2\", \"h-2\", \"bg-pink-400\", \"rounded-full\", \"animate-bounce\", \"animation-delay-400\"], [1, \"w-64\", \"h-2\", \"bg-white/30\", \"rounded-full\", \"mx-auto\", \"mb-6\", \"overflow-hidden\"], [1, \"h-full\", \"bg-gradient-to-r\", \"from-pink-400\", \"to-purple-400\", \"rounded-full\", \"animate-loading-progress\"], [1, \"font-poppins\", \"text-gray-600\", \"text-lg\", \"animate-fade-in-out\"], [1, \"absolute\", \"text-pink-200\", \"opacity-20\", \"animate-gentle-float\"], [1, \"min-h-screen\", \"bg-gradient-to-br\", \"from-pink-50\", \"via-purple-50\", \"to-pink-100\"], [\"class\", \"fixed bottom-32 right-6 z-50 animate-soft-pulse\", 4, \"ngIf\"], [1, \"fixed\", \"bottom-6\", \"right-6\", \"z-40\"], [1, \"glass-effect\", \"rounded-2xl\", \"p-4\", \"shadow-lg\", \"border\", \"border-white/20\", \"max-w-sm\", \"transition-all\", \"duration-300\", \"music-player-prompt\"], [1, \"flex\", \"items-center\", \"space-x-3\"], [1, \"w-12\", \"h-12\", \"bg-gradient-to-br\", \"from-green-400\", \"to-green-600\", \"rounded-xl\", \"flex\", \"items-center\", \"justify-center\"], [\"fill\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-6\", \"h-6\", \"text-white\"], [\"d\", \"M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.42 1.56-.299.421-1.02.599-1.559.3z\"], [1, \"flex-1\", \"min-w-0\"], [1, \"font-poppins\", \"text-sm\", \"font-medium\", \"text-gray-800\", \"truncate\"], [1, \"font-poppins\", \"text-xs\", \"text-gray-600\", \"truncate\"], [1, \"font-poppins\", \"text-xs\", \"text-gray-500\", \"truncate\"], [1, \"w-8\", \"h-8\", \"bg-white\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"shadow-sm\", \"hover:shadow-md\", \"transition-all\", \"duration-200\", \"hover:scale-105\", 3, \"click\"], [\"class\", \"w-4 h-4 text-gray-700 ml-0.5\", \"fill\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [\"class\", \"w-4 h-4 text-gray-700\", \"fill\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 4, \"ngIf\"], [1, \"mt-3\"], [1, \"flex\", \"items-center\", \"space-x-2\"], [1, \"text-xs\", \"text-gray-500\"], [1, \"flex-1\", \"h-1\", \"bg-gray-200\", \"rounded-full\", \"overflow-hidden\"], [1, \"h-full\", \"bg-gradient-to-r\", \"from-green-400\", \"to-green-600\", \"rounded-full\", \"transition-all\", \"duration-300\"], [1, \"fixed\", \"top-0\", \"left-0\", \"right-0\", \"z-50\", \"glass-effect\", \"border-b\", \"border-white/20\"], [1, \"max-w-6xl\", \"mx-auto\", \"px-6\", \"py-4\"], [1, \"flex\", \"items-center\", \"justify-between\"], [1, \"text-2xl\", \"animate-soft-pulse\"], [1, \"font-dancing\", \"text-2xl\", \"text-romantic\"], [1, \"hidden\", \"md:flex\", \"items-center\", \"space-x-8\"], [\"routerLink\", \"/\", \"routerLinkActive\", \"text-romantic font-semibold\", 1, \"font-poppins\", \"text-gray-600\", \"hover:text-romantic\", \"transition-colors\", \"duration-300\", 3, \"routerLinkActiveOptions\"], [\"routerLink\", \"/our-story\", \"routerLinkActive\", \"text-romantic font-semibold\", 1, \"font-poppins\", \"text-gray-600\", \"hover:text-romantic\", \"transition-colors\", \"duration-300\"], [\"routerLink\", \"/love-notes\", \"routerLinkActive\", \"text-romantic font-semibold\", 1, \"font-poppins\", \"text-gray-600\", \"hover:text-romantic\", \"transition-colors\", \"duration-300\"], [\"routerLink\", \"/memories\", \"routerLinkActive\", \"text-romantic font-semibold\", 1, \"font-poppins\", \"text-gray-600\", \"hover:text-romantic\", \"transition-colors\", \"duration-300\"], [1, \"md:hidden\", \"p-2\", 3, \"click\"], [1, \"text-2xl\"], [\"class\", \"md:hidden mt-4 pb-4 border-t border-white/20 pt-4\", 4, \"ngIf\"], [1, \"pt-20\"], [1, \"bg-gradient-to-r\", \"from-pink-200\", \"via-purple-200\", \"to-pink-200\", \"text-gray-700\", \"py-12\", \"text-center\", \"relative\", \"overflow-hidden\"], [1, \"absolute\", \"inset-0\"], [1, \"absolute\", \"top-0\", \"left-1/4\", \"w-32\", \"h-32\", \"bg-white\", \"rounded-full\", \"mix-blend-multiply\", \"filter\", \"blur-2xl\", \"opacity-40\", \"animate-gentle-glow\"], [1, \"absolute\", \"bottom-0\", \"right-1/4\", \"w-40\", \"h-40\", \"bg-white\", \"rounded-full\", \"mix-blend-multiply\", \"filter\", \"blur-2xl\", \"opacity-40\", \"animate-gentle-glow\", \"animation-delay-2000\"], [1, \"relative\", \"z-10\", \"px-6\", \"sm:px-8\", \"lg:px-12\"], [1, \"font-dancing\", \"text-2xl\", \"sm:text-3xl\", \"lg:text-4xl\", \"mb-4\", \"text-romantic\"], [1, \"font-poppins\", \"text-base\", \"sm:text-lg\", \"opacity-80\", \"mb-6\", \"font-light\"], [1, \"flex\", \"justify-center\", \"space-x-6\", \"text-xl\", \"sm:text-2xl\"], [1, \"animate-soft-pulse\"], [1, \"animate-soft-pulse\", \"animation-delay-500\"], [1, \"animate-soft-pulse\", \"animation-delay-1000\"], [1, \"fixed\", \"bottom-32\", \"right-6\", \"z-50\", \"animate-soft-pulse\"], [1, \"glass-effect\", \"rounded-xl\", \"p-3\", \"shadow-lg\", \"border\", \"border-pink-200/50\", \"bg-pink-50/80\"], [1, \"font-poppins\", \"text-sm\", \"text-gray-700\", \"flex\", \"items-center\", \"space-x-2\"], [\"fill\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-gray-700\", \"ml-0.5\"], [\"d\", \"M8 5v14l11-7z\"], [\"fill\", \"currentColor\", \"viewBox\", \"0 0 24 24\", 1, \"w-4\", \"h-4\", \"text-gray-700\"], [\"d\", \"M6 4h4v16H6V4zm8 0h4v16h-4V4z\"], [1, \"md:hidden\", \"mt-4\", \"pb-4\", \"border-t\", \"border-white/20\", \"pt-4\"], [1, \"flex\", \"flex-col\", \"space-y-4\"], [\"routerLink\", \"/\", \"routerLinkActive\", \"text-romantic font-semibold\", 1, \"font-poppins\", \"text-gray-600\", \"hover:text-romantic\", \"transition-colors\", \"duration-300\", 3, \"click\", \"routerLinkActiveOptions\"], [\"routerLink\", \"/our-story\", \"routerLinkActive\", \"text-romantic font-semibold\", 1, \"font-poppins\", \"text-gray-600\", \"hover:text-romantic\", \"transition-colors\", \"duration-300\", 3, \"click\"], [\"routerLink\", \"/love-notes\", \"routerLinkActive\", \"text-romantic font-semibold\", 1, \"font-poppins\", \"text-gray-600\", \"hover:text-romantic\", \"transition-colors\", \"duration-300\", 3, \"click\"], [\"routerLink\", \"/memories\", \"routerLinkActive\", \"text-romantic font-semibold\", 1, \"font-poppins\", \"text-gray-600\", \"hover:text-romantic\", \"transition-colors\", \"duration-300\", 3, \"click\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, AppComponent_div_0_Template, 23, 1, \"div\", 0)(1, AppComponent_div_1_Template, 65, 14, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [RouterOutlet, RouterLink, RouterLinkActive, CommonModule, i1.NgIf],\n      encapsulation: 2\n    });\n  }\n}", "map": {"version": 3, "names": ["RouterOutlet", "RouterLink", "RouterLinkActive", "CommonModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵstyleProp", "heart_r1", "left", "top", "delay", "size", "ɵɵrepeaterCreate", "AppComponent_div_0_For_3_Template", "_forTrack0", "ɵɵelement", "ɵɵadvance", "ɵɵrepeater", "ctx_r1", "loadingHearts", "ɵɵtextInterpolate1", "loadingMessages", "currentMessageIndex", "ɵɵlistener", "AppComponent_div_1_div_46_Template_a_click_2_listener", "ɵɵrestoreView", "_r4", "ɵɵnextContext", "ɵɵresetView", "closeMobileMenu", "AppComponent_div_1_div_46_Template_a_click_4_listener", "AppComponent_div_1_div_46_Template_a_click_6_listener", "AppComponent_div_1_div_46_Template_a_click_8_listener", "ɵɵproperty", "ɵɵpureFunction0", "_c0", "ɵɵtemplate", "AppComponent_div_1_div_1_Template", "AppComponent_div_1_Template_button_click_15_listener", "_r3", "togglePlayPause", "AppComponent_div_1__svg_svg_16_Template", "AppComponent_div_1__svg_svg_17_Template", "AppComponent_div_1_Template_button_click_43_listener", "toggleMobileMenu", "AppComponent_div_1_div_46_Template", "showPlayPromptMessage", "ɵɵclassProp", "isPlaying", "ɵɵtextInterpolate", "formatTime", "currentTime", "progressPercentage", "duration", "mobileMenuOpen", "AppComponent", "constructor", "isLoading", "audio", "Array", "from", "length", "_", "i", "id", "Math", "random", "ngOnInit", "initializeAudio", "messageInterval", "setInterval", "setTimeout", "clearInterval", "autoPlayMusic", "Audio", "loop", "addEventListener", "pause", "play", "catch", "error", "console", "log", "seconds", "minutes", "floor", "remainingSeconds", "toString", "padStart", "then", "showPlayPrompt", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "AppComponent_div_0_Template", "AppComponent_div_1_Template", "i1", "NgIf", "encapsulation"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\monthsary-website\\src\\app\\app.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { RouterOutlet, RouterLink, RouterLinkActive } from '@angular/router';\nimport { CommonModule } from '@angular/common';\n\n@Component({\n  selector: 'app-root',\n  standalone: true,\n  imports: [RouterOutlet, RouterLink, RouterLinkActive, CommonModule],\n  template: `\n    <!-- Loading Screen -->\n    <div *ngIf=\"isLoading\" class=\"fixed inset-0 z-[9999] flex items-center justify-center bg-gradient-to-br from-pink-50 via-purple-50 to-pink-100\">\n      <!-- Animated Background -->\n      <div class=\"absolute inset-0 overflow-hidden\">\n        <!-- Floating Hearts Background -->\n        @for (heart of loadingHearts; track heart.id) {\n          <div\n            class=\"absolute text-pink-200 opacity-20 animate-gentle-float\"\n            [style.left.%]=\"heart.left\"\n            [style.top.%]=\"heart.top\"\n            [style.animation-delay]=\"heart.delay + 's'\"\n            [style.font-size.px]=\"heart.size\">\n            💖\n          </div>\n        }\n\n        <!-- Glowing Orbs -->\n        <div class=\"absolute top-1/4 left-1/4 w-64 h-64 bg-pink-200 rounded-full mix-blend-multiply filter blur-3xl opacity-40 animate-gentle-glow\"></div>\n        <div class=\"absolute bottom-1/4 right-1/4 w-80 h-80 bg-purple-200 rounded-full mix-blend-multiply filter blur-3xl opacity-40 animate-gentle-glow animation-delay-2000\"></div>\n      </div>\n\n      <!-- Loading Content -->\n      <div class=\"relative z-10 text-center px-6\">\n        <!-- Main Loading Animation -->\n        <div class=\"mb-8\">\n          <div class=\"relative\">\n            <!-- Pulsing Heart -->\n            <div class=\"text-8xl animate-heartbeat mb-4\">💖</div>\n\n            <!-- Loading Ring -->\n            <div class=\"absolute inset-0 flex items-center justify-center\">\n              <div class=\"w-32 h-32 border-4 border-pink-200 border-t-pink-400 rounded-full animate-spin\"></div>\n            </div>\n          </div>\n        </div>\n\n        <!-- Loading Text -->\n        <h1 class=\"font-dancing text-4xl sm:text-5xl text-romantic mb-4 animate-soft-pulse\">\n          Loading Our Love Story...\n        </h1>\n\n        <div class=\"flex justify-center space-x-2 mb-8\">\n          <div class=\"w-2 h-2 bg-pink-400 rounded-full animate-bounce\"></div>\n          <div class=\"w-2 h-2 bg-purple-400 rounded-full animate-bounce animation-delay-200\"></div>\n          <div class=\"w-2 h-2 bg-pink-400 rounded-full animate-bounce animation-delay-400\"></div>\n        </div>\n\n        <!-- Progress Bar -->\n        <div class=\"w-64 h-2 bg-white/30 rounded-full mx-auto mb-6 overflow-hidden\">\n          <div class=\"h-full bg-gradient-to-r from-pink-400 to-purple-400 rounded-full animate-loading-progress\"></div>\n        </div>\n\n        <!-- Cute Loading Messages -->\n        <p class=\"font-poppins text-gray-600 text-lg animate-fade-in-out\">\n          {{ loadingMessages[currentMessageIndex] }}\n        </p>\n      </div>\n    </div>\n\n    <!-- Main App Content -->\n    <div *ngIf=\"!isLoading\" class=\"min-h-screen bg-gradient-to-br from-pink-50 via-purple-50 to-pink-100\">\n      <!-- Cute Play Prompt Message -->\n      <div *ngIf=\"showPlayPromptMessage\" class=\"fixed bottom-32 right-6 z-50 animate-soft-pulse\">\n        <div class=\"glass-effect rounded-xl p-3 shadow-lg border border-pink-200/50 bg-pink-50/80\">\n          <p class=\"font-poppins text-sm text-gray-700 flex items-center space-x-2\">\n            <span>🎵</span>\n            <span>Click play to start our song! 💕</span>\n          </p>\n        </div>\n      </div>\n\n      <!-- Spotify Music Player -->\n      <div class=\"fixed bottom-6 right-6 z-40\">\n        <div class=\"glass-effect rounded-2xl p-4 shadow-lg border border-white/20 max-w-sm transition-all duration-300 music-player-prompt\"\n             [class.music-player-glow]=\"isPlaying\"\n             [class.animate-soft-pulse]=\"showPlayPromptMessage && !isPlaying\">\n          <div class=\"flex items-center space-x-3\">\n            <div class=\"w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-xl flex items-center justify-center\">\n              <svg class=\"w-6 h-6 text-white\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.42 1.56-.299.421-1.02.599-1.559.3z\"/>\n              </svg>\n            </div>\n            <div class=\"flex-1 min-w-0\">\n              <p class=\"font-poppins text-sm font-medium text-gray-800 truncate\">Now Playing</p>\n              <p class=\"font-poppins text-xs text-gray-600 truncate\">Bawat Daan</p>\n              <p class=\"font-poppins text-xs text-gray-500 truncate\">Ebe Dancel • Our Special Song 💕</p>\n            </div>\n            <button\n              (click)=\"togglePlayPause()\"\n              class=\"w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-sm hover:shadow-md transition-all duration-200 hover:scale-105\">\n              <!-- Play Icon -->\n              <svg *ngIf=\"!isPlaying\" class=\"w-4 h-4 text-gray-700 ml-0.5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M8 5v14l11-7z\"/>\n              </svg>\n              <!-- Pause Icon -->\n              <svg *ngIf=\"isPlaying\" class=\"w-4 h-4 text-gray-700\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M6 4h4v16H6V4zm8 0h4v16h-4V4z\"/>\n              </svg>\n            </button>\n          </div>\n          <div class=\"mt-3\">\n            <div class=\"flex items-center space-x-2\">\n              <span class=\"text-xs text-gray-500\">{{ formatTime(currentTime) }}</span>\n              <div class=\"flex-1 h-1 bg-gray-200 rounded-full overflow-hidden\">\n                <div\n                  class=\"h-full bg-gradient-to-r from-green-400 to-green-600 rounded-full transition-all duration-300\"\n                  [style.width.%]=\"progressPercentage\">\n                </div>\n              </div>\n              <span class=\"text-xs text-gray-500\">{{ formatTime(duration) }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- Navigation -->\n      <nav class=\"fixed top-0 left-0 right-0 z-50 glass-effect border-b border-white/20\">\n        <div class=\"max-w-6xl mx-auto px-6 py-4\">\n          <div class=\"flex items-center justify-between\">\n            <!-- Logo -->\n            <div class=\"flex items-center space-x-2\">\n              <span class=\"text-2xl animate-soft-pulse\">💖</span>\n              <span class=\"font-dancing text-2xl text-romantic\">Our Love Story</span>\n            </div>\n            \n            <!-- Navigation Links -->\n            <div class=\"hidden md:flex items-center space-x-8\">\n              <a routerLink=\"/\" \n                 routerLinkActive=\"text-romantic font-semibold\" \n                 [routerLinkActiveOptions]=\"{exact: true}\"\n                 class=\"font-poppins text-gray-600 hover:text-romantic transition-colors duration-300\">\n                Home\n              </a>\n              <a routerLink=\"/our-story\" \n                 routerLinkActive=\"text-romantic font-semibold\"\n                 class=\"font-poppins text-gray-600 hover:text-romantic transition-colors duration-300\">\n                Our Story\n              </a>\n              <a routerLink=\"/love-notes\" \n                 routerLinkActive=\"text-romantic font-semibold\"\n                 class=\"font-poppins text-gray-600 hover:text-romantic transition-colors duration-300\">\n                Love Notes\n              </a>\n              <a routerLink=\"/memories\" \n                 routerLinkActive=\"text-romantic font-semibold\"\n                 class=\"font-poppins text-gray-600 hover:text-romantic transition-colors duration-300\">\n                Memories\n              </a>\n            </div>\n            \n            <!-- Mobile Menu Button -->\n            <button (click)=\"toggleMobileMenu()\" class=\"md:hidden p-2\">\n              <span class=\"text-2xl\">💕</span>\n            </button>\n          </div>\n          \n          <!-- Mobile Menu -->\n          <div *ngIf=\"mobileMenuOpen\" class=\"md:hidden mt-4 pb-4 border-t border-white/20 pt-4\">\n            <div class=\"flex flex-col space-y-4\">\n              <a routerLink=\"/\" \n                 (click)=\"closeMobileMenu()\"\n                 routerLinkActive=\"text-romantic font-semibold\" \n                 [routerLinkActiveOptions]=\"{exact: true}\"\n                 class=\"font-poppins text-gray-600 hover:text-romantic transition-colors duration-300\">\n                Home\n              </a>\n              <a routerLink=\"/our-story\" \n                 (click)=\"closeMobileMenu()\"\n                 routerLinkActive=\"text-romantic font-semibold\"\n                 class=\"font-poppins text-gray-600 hover:text-romantic transition-colors duration-300\">\n                Our Story\n              </a>\n              <a routerLink=\"/love-notes\" \n                 (click)=\"closeMobileMenu()\"\n                 routerLinkActive=\"text-romantic font-semibold\"\n                 class=\"font-poppins text-gray-600 hover:text-romantic transition-colors duration-300\">\n                Love Notes\n              </a>\n              <a routerLink=\"/memories\" \n                 (click)=\"closeMobileMenu()\"\n                 routerLinkActive=\"text-romantic font-semibold\"\n                 class=\"font-poppins text-gray-600 hover:text-romantic transition-colors duration-300\">\n                Memories\n              </a>\n            </div>\n          </div>\n        </div>\n      </nav>\n      \n      <!-- Main Content -->\n      <main class=\"pt-20\">\n        <router-outlet></router-outlet>\n      </main>\n      \n      <!-- Footer -->\n      <footer class=\"bg-gradient-to-r from-pink-200 via-purple-200 to-pink-200 text-gray-700 py-12 text-center relative overflow-hidden\">\n        <div class=\"absolute inset-0\">\n          <div class=\"absolute top-0 left-1/4 w-32 h-32 bg-white rounded-full mix-blend-multiply filter blur-2xl opacity-40 animate-gentle-glow\"></div>\n          <div class=\"absolute bottom-0 right-1/4 w-40 h-40 bg-white rounded-full mix-blend-multiply filter blur-2xl opacity-40 animate-gentle-glow animation-delay-2000\"></div>\n        </div>\n        \n        <div class=\"relative z-10 px-6 sm:px-8 lg:px-12\">\n          <p class=\"font-dancing text-2xl sm:text-3xl lg:text-4xl mb-4 text-romantic\">\n            Made with 💖 by Your Coding Boyfriend\n          </p>\n          <p class=\"font-poppins text-base sm:text-lg opacity-80 mb-6 font-light\">\n            Happy 1st Monthsary, My Love!\n          </p>\n          <div class=\"flex justify-center space-x-6 text-xl sm:text-2xl\">\n            <span class=\"animate-soft-pulse\">💻</span>\n            <span class=\"animate-soft-pulse animation-delay-500\">💕</span>\n            <span class=\"animate-soft-pulse animation-delay-1000\">✨</span>\n          </div>\n        </div>\n      </footer>\n    </div>\n  `,\n  styles: []\n})\nexport class AppComponent implements OnInit {\n  mobileMenuOpen = false;\n  isLoading = true;\n  currentMessageIndex = 0;\n\n  // Audio player properties\n  audio: HTMLAudioElement | null = null;\n  isPlaying = false;\n  currentTime = 0;\n  duration = 300; // 5:00 in seconds\n  progressPercentage = 0;\n  showPlayPromptMessage = false;\n\n  loadingMessages = [\n    \"Preparing our special moments... 💕\",\n    \"Gathering all the love notes... 💌\",\n    \"Setting up the perfect atmosphere... ✨\",\n    \"Almost ready to celebrate... 🎉\",\n    \"Loading our beautiful memories... 📸\"\n  ];\n\n  loadingHearts = Array.from({ length: 15 }, (_, i) => ({\n    id: i,\n    left: Math.random() * 100,\n    top: Math.random() * 100,\n    size: Math.random() * 20 + 20,\n    delay: Math.random() * 5\n  }));\n\n  ngOnInit() {\n    // Initialize audio\n    this.initializeAudio();\n\n    // Cycle through loading messages\n    const messageInterval = setInterval(() => {\n      this.currentMessageIndex = (this.currentMessageIndex + 1) % this.loadingMessages.length;\n    }, 1000);\n\n    // Hide loading screen after 5 seconds and auto-play music\n    setTimeout(() => {\n      this.isLoading = false;\n      clearInterval(messageInterval);\n      // Auto-play music after loading\n      this.autoPlayMusic();\n    }, 5000);\n  }\n\n  initializeAudio() {\n    this.audio = new Audio('assets/audio/Ebe Dancel - Bawat Daan (Lyrics).mp3');\n    this.audio.loop = true;\n\n    // Update progress\n    this.audio.addEventListener('timeupdate', () => {\n      if (this.audio) {\n        this.currentTime = this.audio.currentTime;\n        this.progressPercentage = (this.currentTime / this.duration) * 100;\n      }\n    });\n\n    // Handle audio loaded\n    this.audio.addEventListener('loadedmetadata', () => {\n      if (this.audio) {\n        this.duration = this.audio.duration;\n      }\n    });\n  }\n\n  togglePlayPause() {\n    if (!this.audio) return;\n\n    if (this.isPlaying) {\n      this.audio.pause();\n    } else {\n      this.audio.play().catch(error => {\n        console.log('Audio play failed:', error);\n      });\n    }\n    this.isPlaying = !this.isPlaying;\n  }\n\n  formatTime(seconds: number): string {\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n\n  autoPlayMusic() {\n    if (!this.audio) return;\n\n    // Try to auto-play the music\n    this.audio.play().then(() => {\n      this.isPlaying = true;\n      console.log('🎵 Auto-playing \"Bawat Daan\" - Your special song! 💕');\n    }).catch(() => {\n      console.log('Auto-play blocked by browser. Click play button to start music! 🎵');\n      // Show a subtle notification that user can click to play\n      this.showPlayPrompt();\n    });\n  }\n\n  showPlayPrompt() {\n    // Show a cute message encouraging the user to click play\n    this.showPlayPromptMessage = true;\n\n    // Hide the message after 5 seconds\n    setTimeout(() => {\n      this.showPlayPromptMessage = false;\n    }, 5000);\n  }\n\n  toggleMobileMenu() {\n    this.mobileMenuOpen = !this.mobileMenuOpen;\n  }\n\n  closeMobileMenu() {\n    this.mobileMenuOpen = false;\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,EAAEC,UAAU,EAAEC,gBAAgB,QAAQ,iBAAiB;AAC5E,SAASC,YAAY,QAAQ,iBAAiB;;;;;;;;;IAapCC,EAAA,CAAAC,cAAA,cAKoC;IAClCD,EAAA,CAAAE,MAAA,qBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAFJH,EAHA,CAAAI,WAAA,SAAAC,QAAA,CAAAC,IAAA,MAA2B,QAAAD,QAAA,CAAAE,GAAA,MACF,oBAAAF,QAAA,CAAAG,KAAA,OACkB,cAAAH,QAAA,CAAAI,IAAA,OACV;;;;;IARvCT,EAFF,CAAAC,cAAA,aAAgJ,aAEhG;IAE5CD,EAAA,CAAAU,gBAAA,IAAAC,iCAAA,kBAAAC,UAAA,CASC;IAIDZ,EADA,CAAAa,SAAA,aAAkJ,aAC2B;IAC/Kb,EAAA,CAAAG,YAAA,EAAM;IAQAH,EALN,CAAAC,cAAA,aAA4C,aAExB,aACM,cAEyB;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGrDH,EAAA,CAAAC,cAAA,eAA+D;IAC7DD,EAAA,CAAAa,SAAA,eAAkG;IAGxGb,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAGNH,EAAA,CAAAC,cAAA,cAAoF;IAClFD,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,eAAgD;IAG9CD,EAFA,CAAAa,SAAA,eAAmE,eACsB,eACF;IACzFb,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,eAA4E;IAC1ED,EAAA,CAAAa,SAAA,eAA6G;IAC/Gb,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,aAAkE;IAChED,EAAA,CAAAE,MAAA,IACF;IAEJF,EAFI,CAAAG,YAAA,EAAI,EACA,EACF;;;;IApDFH,EAAA,CAAAc,SAAA,GASC;IATDd,EAAA,CAAAe,UAAA,CAAAC,MAAA,CAAAC,aAAA,CASC;IAwCCjB,EAAA,CAAAc,SAAA,IACF;IADEd,EAAA,CAAAkB,kBAAA,MAAAF,MAAA,CAAAG,eAAA,CAAAH,MAAA,CAAAI,mBAAA,OACF;;;;;IAUIpB,EAHN,CAAAC,cAAA,cAA2F,cACE,YACf,WAClE;IAAAD,EAAA,CAAAE,MAAA,mBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACfH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,iDAAgC;IAG5CF,EAH4C,CAAAG,YAAA,EAAO,EAC3C,EACA,EACF;;;;;;IAsBEH,EAAA,CAAAC,cAAA,cAAqG;IACnGD,EAAA,CAAAa,SAAA,eAAyB;IAC3Bb,EAAA,CAAAG,YAAA,EAAM;;;;;;IAENH,EAAA,CAAAC,cAAA,cAA6F;IAC3FD,EAAA,CAAAa,SAAA,eAAyC;IAC3Cb,EAAA,CAAAG,YAAA,EAAM;;;;;;IA8DNH,EAFJ,CAAAC,cAAA,cAAsF,cAC/C,YAKsD;IAHtFD,EAAA,CAAAqB,UAAA,mBAAAC,sDAAA;MAAAtB,EAAA,CAAAuB,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAhB,EAAA,CAAAyB,aAAA;MAAA,OAAAzB,EAAA,CAAA0B,WAAA,CAASV,MAAA,CAAAW,eAAA,EAAiB;IAAA,EAAC;IAI5B3B,EAAA,CAAAE,MAAA,aACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,YAGyF;IAFtFD,EAAA,CAAAqB,UAAA,mBAAAO,sDAAA;MAAA5B,EAAA,CAAAuB,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAhB,EAAA,CAAAyB,aAAA;MAAA,OAAAzB,EAAA,CAAA0B,WAAA,CAASV,MAAA,CAAAW,eAAA,EAAiB;IAAA,EAAC;IAG5B3B,EAAA,CAAAE,MAAA,kBACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,YAGyF;IAFtFD,EAAA,CAAAqB,UAAA,mBAAAQ,sDAAA;MAAA7B,EAAA,CAAAuB,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAhB,EAAA,CAAAyB,aAAA;MAAA,OAAAzB,EAAA,CAAA0B,WAAA,CAASV,MAAA,CAAAW,eAAA,EAAiB;IAAA,EAAC;IAG5B3B,EAAA,CAAAE,MAAA,mBACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,YAGyF;IAFtFD,EAAA,CAAAqB,UAAA,mBAAAS,sDAAA;MAAA9B,EAAA,CAAAuB,aAAA,CAAAC,GAAA;MAAA,MAAAR,MAAA,GAAAhB,EAAA,CAAAyB,aAAA;MAAA,OAAAzB,EAAA,CAAA0B,WAAA,CAASV,MAAA,CAAAW,eAAA,EAAiB;IAAA,EAAC;IAG5B3B,EAAA,CAAAE,MAAA,iBACF;IAEJF,EAFI,CAAAG,YAAA,EAAI,EACA,EACF;;;IAvBCH,EAAA,CAAAc,SAAA,GAAyC;IAAzCd,EAAA,CAAA+B,UAAA,4BAAA/B,EAAA,CAAAgC,eAAA,IAAAC,GAAA,EAAyC;;;;;;IAtGtDjC,EAAA,CAAAC,cAAA,cAAsG;IAEpGD,EAAA,CAAAkC,UAAA,IAAAC,iCAAA,kBAA2F;IAerFnC,EALN,CAAAC,cAAA,cAAyC,cAG+B,cAC3B,cAC0E;;IAC/GD,EAAA,CAAAC,cAAA,cAAwE;IACtED,EAAA,CAAAa,SAAA,eAAqlB;IAEzlBb,EADE,CAAAG,YAAA,EAAM,EACF;;IAEJH,EADF,CAAAC,cAAA,cAA4B,YACyC;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAClFH,EAAA,CAAAC,cAAA,aAAuD;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACrEH,EAAA,CAAAC,cAAA,aAAuD;IAAAD,EAAA,CAAAE,MAAA,uDAAgC;IACzFF,EADyF,CAAAG,YAAA,EAAI,EACvF;IACNH,EAAA,CAAAC,cAAA,kBAE+I;IAD7ID,EAAA,CAAAqB,UAAA,mBAAAe,qDAAA;MAAApC,EAAA,CAAAuB,aAAA,CAAAc,GAAA;MAAA,MAAArB,MAAA,GAAAhB,EAAA,CAAAyB,aAAA;MAAA,OAAAzB,EAAA,CAAA0B,WAAA,CAASV,MAAA,CAAAsB,eAAA,EAAiB;IAAA,EAAC;IAO3BtC,EAJA,CAAAkC,UAAA,KAAAK,uCAAA,kBAAqG,KAAAC,uCAAA,kBAIR;IAIjGxC,EADE,CAAAG,YAAA,EAAS,EACL;IAGFH,EAFJ,CAAAC,cAAA,eAAkB,eACyB,gBACH;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxEH,EAAA,CAAAC,cAAA,eAAiE;IAC/DD,EAAA,CAAAa,SAAA,eAGM;IACRb,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAAoC;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAItEF,EAJsE,CAAAG,YAAA,EAAO,EACjE,EACF,EACF,EACF;IAQEH,EALR,CAAAC,cAAA,eAAmF,eACxC,eACQ,eAEJ,gBACG;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnDH,EAAA,CAAAC,cAAA,gBAAkD;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAClEF,EADkE,CAAAG,YAAA,EAAO,EACnE;IAIJH,EADF,CAAAC,cAAA,eAAmD,aAIwC;IACvFD,EAAA,CAAAE,MAAA,cACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,aAEyF;IACvFD,EAAA,CAAAE,MAAA,mBACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,aAEyF;IACvFD,EAAA,CAAAE,MAAA,oBACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,aAEyF;IACvFD,EAAA,CAAAE,MAAA,kBACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACA;IAGNH,EAAA,CAAAC,cAAA,kBAA2D;IAAnDD,EAAA,CAAAqB,UAAA,mBAAAoB,qDAAA;MAAAzC,EAAA,CAAAuB,aAAA,CAAAc,GAAA;MAAA,MAAArB,MAAA,GAAAhB,EAAA,CAAAyB,aAAA;MAAA,OAAAzB,EAAA,CAAA0B,WAAA,CAASV,MAAA,CAAA0B,gBAAA,EAAkB;IAAA,EAAC;IAClC1C,EAAA,CAAAC,cAAA,gBAAuB;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAE7BF,EAF6B,CAAAG,YAAA,EAAO,EACzB,EACL;IAGNH,EAAA,CAAAkC,UAAA,KAAAS,kCAAA,mBAAsF;IA8B1F3C,EADE,CAAAG,YAAA,EAAM,EACF;IAGNH,EAAA,CAAAC,cAAA,gBAAoB;IAClBD,EAAA,CAAAa,SAAA,qBAA+B;IACjCb,EAAA,CAAAG,YAAA,EAAO;IAILH,EADF,CAAAC,cAAA,kBAAmI,eACnG;IAE5BD,EADA,CAAAa,SAAA,eAA6I,eACyB;IACxKb,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,eAAiD,aAC6B;IAC1ED,EAAA,CAAAE,MAAA,yDACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,aAAwE;IACtED,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEFH,EADF,CAAAC,cAAA,eAA+D,gBAC5B;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC1CH,EAAA,CAAAC,cAAA,gBAAqD;IAAAD,EAAA,CAAAE,MAAA,oBAAE;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9DH,EAAA,CAAAC,cAAA,gBAAsD;IAAAD,EAAA,CAAAE,MAAA,cAAC;IAI/DF,EAJ+D,CAAAG,YAAA,EAAO,EAC1D,EACF,EACC,EACL;;;;IAzJEH,EAAA,CAAAc,SAAA,EAA2B;IAA3Bd,EAAA,CAAA+B,UAAA,SAAAf,MAAA,CAAA4B,qBAAA,CAA2B;IAY1B5C,EAAA,CAAAc,SAAA,GAAqC;IACrCd,EADA,CAAA6C,WAAA,sBAAA7B,MAAA,CAAA8B,SAAA,CAAqC,uBAAA9B,MAAA,CAAA4B,qBAAA,KAAA5B,MAAA,CAAA8B,SAAA,CAC2B;IAgBzD9C,EAAA,CAAAc,SAAA,IAAgB;IAAhBd,EAAA,CAAA+B,UAAA,UAAAf,MAAA,CAAA8B,SAAA,CAAgB;IAIhB9C,EAAA,CAAAc,SAAA,EAAe;IAAfd,EAAA,CAAA+B,UAAA,SAAAf,MAAA,CAAA8B,SAAA,CAAe;IAOe9C,EAAA,CAAAc,SAAA,GAA6B;IAA7Bd,EAAA,CAAA+C,iBAAA,CAAA/B,MAAA,CAAAgC,UAAA,CAAAhC,MAAA,CAAAiC,WAAA,EAA6B;IAI7DjD,EAAA,CAAAc,SAAA,GAAoC;IAApCd,EAAA,CAAAI,WAAA,UAAAY,MAAA,CAAAkC,kBAAA,MAAoC;IAGJlD,EAAA,CAAAc,SAAA,GAA0B;IAA1Bd,EAAA,CAAA+C,iBAAA,CAAA/B,MAAA,CAAAgC,UAAA,CAAAhC,MAAA,CAAAmC,QAAA,EAA0B;IAoB3DnD,EAAA,CAAAc,SAAA,IAAyC;IAAzCd,EAAA,CAAA+B,UAAA,4BAAA/B,EAAA,CAAAgC,eAAA,KAAAC,GAAA,EAAyC;IA4B1CjC,EAAA,CAAAc,SAAA,IAAoB;IAApBd,EAAA,CAAA+B,UAAA,SAAAf,MAAA,CAAAoC,cAAA,CAAoB;;;AA8DpC,OAAM,MAAOC,YAAY;EAhOzBC,YAAA;IAiOE,KAAAF,cAAc,GAAG,KAAK;IACtB,KAAAG,SAAS,GAAG,IAAI;IAChB,KAAAnC,mBAAmB,GAAG,CAAC;IAEvB;IACA,KAAAoC,KAAK,GAA4B,IAAI;IACrC,KAAAV,SAAS,GAAG,KAAK;IACjB,KAAAG,WAAW,GAAG,CAAC;IACf,KAAAE,QAAQ,GAAG,GAAG,CAAC,CAAC;IAChB,KAAAD,kBAAkB,GAAG,CAAC;IACtB,KAAAN,qBAAqB,GAAG,KAAK;IAE7B,KAAAzB,eAAe,GAAG,CAChB,qCAAqC,EACrC,oCAAoC,EACpC,wCAAwC,EACxC,iCAAiC,EACjC,sCAAsC,CACvC;IAED,KAAAF,aAAa,GAAGwC,KAAK,CAACC,IAAI,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAE,EAAE,CAACC,CAAC,EAAEC,CAAC,MAAM;MACpDC,EAAE,EAAED,CAAC;MACLvD,IAAI,EAAEyD,IAAI,CAACC,MAAM,EAAE,GAAG,GAAG;MACzBzD,GAAG,EAAEwD,IAAI,CAACC,MAAM,EAAE,GAAG,GAAG;MACxBvD,IAAI,EAAEsD,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE;MAC7BxD,KAAK,EAAEuD,IAAI,CAACC,MAAM,EAAE,GAAG;KACxB,CAAC,CAAC;;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,eAAe,EAAE;IAEtB;IACA,MAAMC,eAAe,GAAGC,WAAW,CAAC,MAAK;MACvC,IAAI,CAAChD,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB,GAAG,CAAC,IAAI,IAAI,CAACD,eAAe,CAACwC,MAAM;IACzF,CAAC,EAAE,IAAI,CAAC;IAER;IACAU,UAAU,CAAC,MAAK;MACd,IAAI,CAACd,SAAS,GAAG,KAAK;MACtBe,aAAa,CAACH,eAAe,CAAC;MAC9B;MACA,IAAI,CAACI,aAAa,EAAE;IACtB,CAAC,EAAE,IAAI,CAAC;EACV;EAEAL,eAAeA,CAAA;IACb,IAAI,CAACV,KAAK,GAAG,IAAIgB,KAAK,CAAC,mDAAmD,CAAC;IAC3E,IAAI,CAAChB,KAAK,CAACiB,IAAI,GAAG,IAAI;IAEtB;IACA,IAAI,CAACjB,KAAK,CAACkB,gBAAgB,CAAC,YAAY,EAAE,MAAK;MAC7C,IAAI,IAAI,CAAClB,KAAK,EAAE;QACd,IAAI,CAACP,WAAW,GAAG,IAAI,CAACO,KAAK,CAACP,WAAW;QACzC,IAAI,CAACC,kBAAkB,GAAI,IAAI,CAACD,WAAW,GAAG,IAAI,CAACE,QAAQ,GAAI,GAAG;MACpE;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACK,KAAK,CAACkB,gBAAgB,CAAC,gBAAgB,EAAE,MAAK;MACjD,IAAI,IAAI,CAAClB,KAAK,EAAE;QACd,IAAI,CAACL,QAAQ,GAAG,IAAI,CAACK,KAAK,CAACL,QAAQ;MACrC;IACF,CAAC,CAAC;EACJ;EAEAb,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACkB,KAAK,EAAE;IAEjB,IAAI,IAAI,CAACV,SAAS,EAAE;MAClB,IAAI,CAACU,KAAK,CAACmB,KAAK,EAAE;IACpB,CAAC,MAAM;MACL,IAAI,CAACnB,KAAK,CAACoB,IAAI,EAAE,CAACC,KAAK,CAACC,KAAK,IAAG;QAC9BC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,KAAK,CAAC;MAC1C,CAAC,CAAC;IACJ;IACA,IAAI,CAAChC,SAAS,GAAG,CAAC,IAAI,CAACA,SAAS;EAClC;EAEAE,UAAUA,CAACiC,OAAe;IACxB,MAAMC,OAAO,GAAGnB,IAAI,CAACoB,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMG,gBAAgB,GAAGrB,IAAI,CAACoB,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IACjD,OAAO,GAAGC,OAAO,IAAIE,gBAAgB,CAACC,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACrE;EAEAf,aAAaA,CAAA;IACX,IAAI,CAAC,IAAI,CAACf,KAAK,EAAE;IAEjB;IACA,IAAI,CAACA,KAAK,CAACoB,IAAI,EAAE,CAACW,IAAI,CAAC,MAAK;MAC1B,IAAI,CAACzC,SAAS,GAAG,IAAI;MACrBiC,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;IACrE,CAAC,CAAC,CAACH,KAAK,CAAC,MAAK;MACZE,OAAO,CAACC,GAAG,CAAC,oEAAoE,CAAC;MACjF;MACA,IAAI,CAACQ,cAAc,EAAE;IACvB,CAAC,CAAC;EACJ;EAEAA,cAAcA,CAAA;IACZ;IACA,IAAI,CAAC5C,qBAAqB,GAAG,IAAI;IAEjC;IACAyB,UAAU,CAAC,MAAK;MACd,IAAI,CAACzB,qBAAqB,GAAG,KAAK;IACpC,CAAC,EAAE,IAAI,CAAC;EACV;EAEAF,gBAAgBA,CAAA;IACd,IAAI,CAACU,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;EAC5C;EAEAzB,eAAeA,CAAA;IACb,IAAI,CAACyB,cAAc,GAAG,KAAK;EAC7B;;;uCApHWC,YAAY;IAAA;EAAA;;;YAAZA,YAAY;MAAAoC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA3F,EAAA,CAAA4F,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA/JrBlG,EA3DA,CAAAkC,UAAA,IAAAkE,2BAAA,kBAAgJ,IAAAC,2BAAA,mBA2D1C;;;UA3DhGrG,EAAA,CAAA+B,UAAA,SAAAoE,GAAA,CAAA5C,SAAA,CAAe;UA2DfvD,EAAA,CAAAc,SAAA,EAAgB;UAAhBd,EAAA,CAAA+B,UAAA,UAAAoE,GAAA,CAAA5C,SAAA,CAAgB;;;qBA9Dd3D,YAAY,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,YAAY,EAAAuG,EAAA,CAAAC,IAAA;MAAAC,aAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}