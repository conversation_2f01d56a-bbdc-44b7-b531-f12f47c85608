import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [CommonModule, RouterLink],
  template: `
    <!-- Hero Section -->
    <section class="min-h-screen flex items-center justify-center relative overflow-hidden">
      <!-- Beautiful Background Image -->
      <div class="absolute inset-0">
        <img
          src="https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2025&q=80"
          alt="Romantic couple silhouette at sunset"
          class="w-full h-full object-cover opacity-15"
        />
        <div class="absolute inset-0 bg-gradient-to-br from-pink-50/90 to-purple-50/90"></div>
        <div class="absolute top-1/3 left-1/4 w-96 h-96 bg-pink-100 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-gentle-glow"></div>
        <div class="absolute bottom-1/3 right-1/4 w-80 h-80 bg-purple-100 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-gentle-glow animation-delay-2000"></div>
      </div>

      <!-- Floating Hearts -->
      <div class="absolute inset-0 pointer-events-none overflow-hidden">
        @for (heart of floatingHearts; track heart.id) {
          <div
            class="absolute text-pink-200 opacity-30 animate-gentle-float"
            [style.left.%]="heart.left"
            [style.top.%]="heart.top"
            [style.animation-delay]="heart.delay + 's'"
            [style.font-size.px]="heart.size">
            💖
          </div>
        }
      </div>

      <!-- Main Content -->
      <div class="relative z-10 text-center px-6 sm:px-8 lg:px-12 max-w-5xl mx-auto">
        <!-- Elegant Glass Card -->
        <div class="glass-effect rounded-3xl p-12 sm:p-16 lg:p-20 shadow-xl border border-white/20">
          <div class="mb-8">
            <span class="text-4xl sm:text-5xl animate-soft-pulse">💖</span>
          </div>
          
          <h1 class="font-dancing text-5xl sm:text-7xl lg:text-8xl text-romantic mb-6 animate-soft-pulse leading-tight">
            Happy 1st Monthsary
          </h1>
          
          <h2 class="font-playfair text-2xl sm:text-3xl lg:text-4xl text-gray-600 mb-8 italic font-medium">
            My Tangi 
          </h2>
          
          <p class="font-poppins text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-12 font-light">
            Welcome to our digital love story, where every moment is treasured
            and every memory is painted with pure affection
          </p>
          
          <!-- Navigation Buttons -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a routerLink="/our-story" 
               class="bg-gradient-to-r from-pink-300 to-purple-300 text-gray-700 px-10 py-4 rounded-full font-poppins font-medium text-lg hover:shadow-xl transform hover:scale-105 transition-all duration-500 hover:from-pink-400 hover:to-purple-400 hover:text-white">
              Our Story ✨
            </a>
            <a routerLink="/love-notes" 
               class="bg-white/50 backdrop-blur-sm text-gray-700 px-10 py-4 rounded-full font-poppins font-medium text-lg hover:shadow-xl transform hover:scale-105 transition-all duration-500 border border-pink-200 hover:bg-pink-100">
              Love Notes 💕
            </a>
          </div>
        </div>
      </div>

      <!-- Minimal Sparkle Effects -->
      <div class="absolute top-20 left-20 text-pink-200 text-2xl animate-gentle-glow">✨</div>
      <div class="absolute bottom-20 right-20 text-purple-200 text-2xl animate-gentle-glow animation-delay-2000">💫</div>
    </section>

    <!-- Quick Preview Section -->
    <section class="py-20 px-6 sm:px-8 lg:px-12">
      <div class="max-w-6xl mx-auto">
        <div class="text-center mb-16">
          <h2 class="font-playfair text-4xl sm:text-5xl text-romantic mb-6">
            Our Journey Together
          </h2>
          <p class="font-poppins text-xl text-gray-600 max-w-2xl mx-auto font-light">
            Every day with you is a new chapter in our beautiful love story
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <!-- Story Preview -->
          <div class="glass-effect rounded-2xl overflow-hidden card-hover border border-white/20 group">
            <div class="relative h-32 overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-1516589178581-6cd7833ae3b2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
                alt="Our Story"
                class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
              <div class="absolute top-2 left-2">
                <div class="text-3xl animate-soft-pulse drop-shadow-lg">📖</div>
              </div>
            </div>
            <div class="p-6 text-center">
              <h3 class="font-playfair text-2xl font-semibold text-gray-700 mb-4">Our Story</h3>
              <p class="font-poppins text-gray-600 mb-6 font-light">From our first glance to this beautiful moment</p>
              <a routerLink="/our-story" class="text-romantic font-medium hover:underline">Read More →</a>
            </div>
          </div>

          <!-- Love Notes Preview -->
          <div class="glass-effect rounded-2xl overflow-hidden card-hover border border-white/20 group">
            <div class="relative h-32 overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-1518568814500-bf0f8d125f46?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
                alt="Love Notes"
                class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
              <div class="absolute top-2 left-2">
                <div class="text-3xl animate-soft-pulse drop-shadow-lg">💌</div>
              </div>
            </div>
            <div class="p-6 text-center">
              <h3 class="font-playfair text-2xl font-semibold text-gray-700 mb-4">Love Notes</h3>
              <p class="font-poppins text-gray-600 mb-6 font-light">All the things I love about you</p>
              <a routerLink="/love-notes" class="text-romantic font-medium hover:underline">Discover →</a>
            </div>
          </div>

          <!-- Memories Preview -->
          <div class="glass-effect rounded-2xl overflow-hidden card-hover border border-white/20 group">
            <div class="relative h-32 overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-1529333166437-7750a6dd5a70?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
                alt="Memories"
                class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
              <div class="absolute top-2 left-2">
                <div class="text-3xl animate-soft-pulse drop-shadow-lg">📸</div>
              </div>
            </div>
            <div class="p-6 text-center">
              <h3 class="font-playfair text-2xl font-semibold text-gray-700 mb-4">Memories</h3>
              <p class="font-poppins text-gray-600 mb-6 font-light">Our precious moments together</p>
              <a routerLink="/memories" class="text-romantic font-medium hover:underline">Explore →</a>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Daily Conyo Love Messages Section -->
    <section class="py-20 px-6 sm:px-8 lg:px-12 bg-gradient-to-br from-pink-25 to-purple-25">
      <div class="max-w-6xl mx-auto">
        <div class="text-center mb-16">
          <h2 class="font-playfair text-4xl sm:text-5xl text-romantic mb-6">
            Daily Love Messages 💕
          </h2>
          <p class="font-poppins text-xl text-gray-600 max-w-2xl mx-auto font-light">
            Special conyo messages that unlock each day of our journey together
          </p>
          <div class="mt-4 text-sm text-gray-500">
            Today is {{ getCurrentDate() }} • {{ getUnlockedCount() }}/{{ dailyMessages.length }} messages unlocked
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          @for (message of dailyMessages; track message.day) {
            <div class="relative">
              <!-- Unlocked Message -->
              <div *ngIf="isMessageUnlocked(message.day)"
                   class="glass-effect rounded-2xl p-6 border border-white/20 card-hover transform transition-all duration-500 hover:scale-105">
                <div class="flex items-center justify-between mb-4">
                  <div class="flex items-center space-x-2">
                    <span class="text-2xl">{{ message.emoji }}</span>
                    <span class="font-poppins font-semibold text-gray-700">Day {{ message.day }}</span>
                  </div>
                  <div class="text-green-500 text-xl">🔓</div>
                </div>
                <div class="text-center">
                  <h3 class="font-dancing text-2xl text-romantic mb-3">{{ message.title }}</h3>
                  <p class="font-poppins text-gray-600 leading-relaxed italic">
                    "{{ message.message }}"
                  </p>
                  <div class="mt-4 text-xs text-gray-500">
                    Unlocked on {{ message.unlockDate }}
                  </div>
                </div>
              </div>

              <!-- Locked Message -->
              <div *ngIf="!isMessageUnlocked(message.day)"
                   class="glass-effect rounded-2xl p-6 border border-gray-200/50 opacity-60 relative overflow-hidden">
                <div class="absolute inset-0 bg-gradient-to-br from-gray-100/50 to-gray-200/50 backdrop-blur-sm"></div>
                <div class="relative z-10 text-center">
                  <div class="flex items-center justify-center mb-4">
                    <div class="text-4xl text-gray-400">🔒</div>
                  </div>
                  <h3 class="font-dancing text-xl text-gray-400 mb-2">Day {{ message.day }}</h3>
                  <p class="font-poppins text-gray-400 text-sm">
                    Unlocks on {{ message.unlockDate }}
                  </p>
                  <div class="mt-3 text-xs text-gray-400">
                    {{ getDaysUntilUnlock(message.day) }} days to go...
                  </div>
                </div>
              </div>
            </div>
          }
        </div>

        <!-- Progress Bar -->
        <div class="mt-12 text-center">
          <div class="max-w-md mx-auto">
            <div class="flex justify-between text-sm text-gray-600 mb-2">
              <span>Progress</span>
              <span>{{ getUnlockedCount() }}/{{ dailyMessages.length }}</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
              <div class="bg-gradient-to-r from-pink-400 to-purple-400 h-full rounded-full transition-all duration-1000"
                   [style.width.%]="getProgressPercentage()">
              </div>
            </div>
            <p class="mt-3 text-sm text-gray-500">
              {{ getProgressMessage() }}
            </p>
          </div>
        </div>
      </div>
    </section>
  `,
  styles: []
})
export class HomeComponent implements OnInit {
  floatingHearts = [
    { id: 1, left: 10, top: 20, size: 20, delay: 0 },
    { id: 2, left: 80, top: 10, size: 25, delay: 1 },
    { id: 3, left: 60, top: 70, size: 18, delay: 2 },
    { id: 4, left: 30, top: 50, size: 22, delay: 0.5 }
  ];

  // Starting date of your relationship (adjust this to your actual start date)
  relationshipStartDate = new Date('2024-06-10'); // Example: June 10, 2024

  dailyMessages = [
    {
      day: 1,
      emoji: '💕',
      title: 'First Day Magic',
      message: 'Babe, you know what? From day one, you already had me feeling kilig na sobra! Like, ang ganda mo talaga and I knew na you\'re gonna be special sa life ko.',
      unlockDate: this.getUnlockDate(1)
    },
    {
      day: 2,
      emoji: '✨',
      title: 'Getting to Know You',
      message: 'Day 2 and I\'m already thinking about you non-stop! Grabe, you\'re so smart and funny. I love how you make me laugh kahit ang corny ng jokes mo sometimes, hehe.',
      unlockDate: this.getUnlockDate(2)
    },
    {
      day: 3,
      emoji: '🌟',
      title: 'Deeper Connection',
      message: 'Okay lang, I\'m officially smitten na talaga! You\'re not just pretty, you\'re also so genuine and kind. I love how you care about everyone around you.',
      unlockDate: this.getUnlockDate(3)
    },
    {
      day: 4,
      emoji: '💖',
      title: 'Growing Feelings',
      message: 'Babe, I think I\'m falling na talaga. You make my heart do somersaults every time you smile. Ang cute mo kasi when you laugh at your own jokes!',
      unlockDate: this.getUnlockDate(4)
    },
    {
      day: 5,
      emoji: '🥰',
      title: 'Sweet Moments',
      message: 'You know what I love about you? You\'re so thoughtful and caring. Even the little things you do make me feel so special. You\'re amazing, love!',
      unlockDate: this.getUnlockDate(5)
    },
    {
      day: 6,
      emoji: '💝',
      title: 'Appreciation Day',
      message: 'I appreciate you so much, mahal. You listen to me when I rant, you support my dreams, and you make me want to be a better person. Thank you for being you!',
      unlockDate: this.getUnlockDate(6)
    },
    {
      day: 7,
      emoji: '🌈',
      title: 'One Week Strong',
      message: 'One week na tayo and I\'m already so attached sa you! You bring so much color to my life. Every day with you feels like a new adventure!',
      unlockDate: this.getUnlockDate(7)
    },
    {
      day: 8,
      emoji: '💫',
      title: 'Dream Girl',
      message: 'Honestly, you\'re like my dream girl come true. Smart, beautiful, funny, and so down-to-earth. I feel so lucky na you chose me din!',
      unlockDate: this.getUnlockDate(8)
    },
    {
      day: 9,
      emoji: '🦋',
      title: 'Butterfly Feelings',
      message: 'You still give me butterflies, you know? Every text, every call, every time I see you - my heart just goes crazy! You have this effect on me talaga.',
      unlockDate: this.getUnlockDate(9)
    },
    {
      day: 10,
      emoji: '💐',
      title: 'Perfect Match',
      message: 'I love how we just click, babe. We can talk about anything and everything. You get my humor, you understand my weirdness - we\'re perfect for each other!',
      unlockDate: this.getUnlockDate(10)
    },
    {
      day: 11,
      emoji: '🌸',
      title: 'Growing Love',
      message: 'My feelings for you keep growing every day. You\'re not just my girlfriend, you\'re my best friend, my confidant, my happy place. I love you so much!',
      unlockDate: this.getUnlockDate(11)
    },
    {
      day: 12,
      emoji: '💞',
      title: 'Grateful Heart',
      message: 'I\'m so grateful na you came into my life, love. You make everything better just by being there. Thank you for choosing to love me back!',
      unlockDate: this.getUnlockDate(12)
    },
    {
      day: 13,
      emoji: '🎀',
      title: 'Lucky Number',
      message: '13 might be unlucky for others, but for us? It\'s another day of being blessed with your love. You\'re my good luck charm, always!',
      unlockDate: this.getUnlockDate(13)
    },
    {
      day: 14,
      emoji: '💗',
      title: 'Two Weeks of Bliss',
      message: 'Two weeks na and I\'m still amazed by you every day. You\'re so strong, so independent, yet so sweet and caring. You\'re everything I ever wanted!',
      unlockDate: this.getUnlockDate(14)
    },
    {
      day: 15,
      emoji: '🌺',
      title: 'Halfway to a Month',
      message: 'Halfway to our first month na! Time flies when you\'re happy talaga. Every moment with you is a treasure that I\'ll keep in my heart forever.',
      unlockDate: this.getUnlockDate(15)
    },
    {
      day: 16,
      emoji: '💘',
      title: 'Sweet Sixteen',
      message: 'Sweet sixteen days of loving you! You make my world so much brighter, babe. I can\'t imagine my days without your sweet messages and beautiful smile.',
      unlockDate: this.getUnlockDate(16)
    },
    {
      day: 17,
      emoji: '🌻',
      title: 'Sunshine Love',
      message: 'You\'re my sunshine on cloudy days, love. Even when I\'m stressed or tired, just thinking about you makes everything okay. You\'re my peace and happiness!',
      unlockDate: this.getUnlockDate(17)
    },
    {
      day: 18,
      emoji: '💓',
      title: 'Heartbeat',
      message: 'You make my heart beat faster every time I see you. After all these days, you still have the same effect on me. That\'s how I know this is real!',
      unlockDate: this.getUnlockDate(18)
    },
    {
      day: 19,
      emoji: '🎈',
      title: 'Floating on Cloud Nine',
      message: 'I\'m literally floating on cloud nine because of you! You make me feel so loved and special. Thank you for being the most amazing girlfriend ever!',
      unlockDate: this.getUnlockDate(19)
    },
    {
      day: 20,
      emoji: '💎',
      title: 'Precious Gem',
      message: 'You\'re like a precious gem, love - rare, beautiful, and absolutely priceless. I promise to treasure you always and never take you for granted.',
      unlockDate: this.getUnlockDate(20)
    },
    {
      day: 21,
      emoji: '🌙',
      title: 'Three Weeks Strong',
      message: 'Three weeks na tayo and my love for you just keeps growing! You\'re my moon and stars, lighting up my darkest nights. I love you to the moon and back!',
      unlockDate: this.getUnlockDate(21)
    },
    {
      day: 22,
      emoji: '🦄',
      title: 'Unicorn Love',
      message: 'You\'re like a unicorn - magical, rare, and absolutely perfect! I still can\'t believe na you\'re mine. Thank you for making my fairy tale dreams come true!',
      unlockDate: this.getUnlockDate(22)
    },
    {
      day: 23,
      emoji: '🌷',
      title: 'Blooming Love',
      message: 'Our love keeps blooming like a beautiful flower, getting more beautiful each day. You nurture my heart with your kindness and love, mahal.',
      unlockDate: this.getUnlockDate(23)
    },
    {
      day: 24,
      emoji: '⭐',
      title: 'Shining Star',
      message: 'You\'re my shining star, guiding me through life\'s journey. With you by my side, I feel like I can conquer anything. You\'re my inspiration and motivation!',
      unlockDate: this.getUnlockDate(24)
    },
    {
      day: 25,
      emoji: '🎁',
      title: 'Gift from Heaven',
      message: 'You\'re truly a gift from heaven, love. Every day with you feels like Christmas morning - full of joy, excitement, and endless possibilities!',
      unlockDate: this.getUnlockDate(25)
    },
    {
      day: 26,
      emoji: '🌊',
      title: 'Ocean of Love',
      message: 'My love for you is like an ocean - deep, endless, and powerful. You\'ve completely swept me off my feet and I\'m drowning in the best way possible!',
      unlockDate: this.getUnlockDate(26)
    },
    {
      day: 27,
      emoji: '🔥',
      title: 'Burning Passion',
      message: 'The fire of my love for you burns brighter each day. You ignite something special in me, babe. You make me feel alive and passionate about life!',
      unlockDate: this.getUnlockDate(27)
    },
    {
      day: 28,
      emoji: '🎪',
      title: 'Circus of Joy',
      message: 'Life with you is like a beautiful circus - full of wonder, laughter, and amazing surprises! You\'re the ringmaster of my heart, love!',
      unlockDate: this.getUnlockDate(28)
    },
    {
      day: 29,
      emoji: '🎭',
      title: 'Perfect Performance',
      message: 'Almost a month na and you continue to amaze me every day! You\'re not acting or pretending - you\'re genuinely this wonderful, and I love every bit of you!',
      unlockDate: this.getUnlockDate(29)
    },
    {
      day: 30,
      emoji: '🎉',
      title: 'One Month Milestone!',
      message: 'WE DID IT, BABE! One whole month of pure happiness, love, and amazing memories! This is just the beginning of our beautiful forever. I love you so much, my one and only! 💕✨',
      unlockDate: this.getUnlockDate(30)
    }
  ];

  ngOnInit() {
    // Component initialization
  }

  getUnlockDate(day: number): string {
    const unlockDate = new Date(this.relationshipStartDate);
    unlockDate.setDate(unlockDate.getDate() + (day - 1));
    return unlockDate.toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  }

  isMessageUnlocked(day: number): boolean {
    const today = new Date();
    const unlockDate = new Date(this.relationshipStartDate);
    unlockDate.setDate(unlockDate.getDate() + (day - 1));
    return today >= unlockDate;
  }

  getDaysUntilUnlock(day: number): number {
    const today = new Date();
    const unlockDate = new Date(this.relationshipStartDate);
    unlockDate.setDate(unlockDate.getDate() + (day - 1));
    const diffTime = unlockDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return Math.max(0, diffDays);
  }

  getCurrentDate(): string {
    return new Date().toLocaleDateString('en-US', {
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  }

  getUnlockedCount(): number {
    return this.dailyMessages.filter(message => this.isMessageUnlocked(message.day)).length;
  }

  getProgressPercentage(): number {
    return (this.getUnlockedCount() / this.dailyMessages.length) * 100;
  }

  getProgressMessage(): string {
    const unlockedCount = this.getUnlockedCount();
    const totalCount = this.dailyMessages.length;

    if (unlockedCount === 0) {
      return "Your love journey is just beginning! 💕";
    } else if (unlockedCount < 7) {
      return "First week vibes! Getting to know each other 🥰";
    } else if (unlockedCount < 14) {
      return "Two weeks strong! The feelings are growing 💖";
    } else if (unlockedCount < 21) {
      return "Three weeks in! This is getting serious 😍";
    } else if (unlockedCount < 30) {
      return "Almost one month! The love is real 💞";
    } else {
      return "One month milestone achieved! Forever to go! 🎉";
    }
  }
}
