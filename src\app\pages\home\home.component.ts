import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [CommonModule, RouterLink],
  template: `
    <!-- Hero Section -->
    <section class="min-h-screen flex items-center justify-center relative overflow-hidden">
      <!-- Beautiful Background Image -->
      <div class="absolute inset-0">
        <img
          src="https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2025&q=80"
          alt="Romantic couple silhouette at sunset"
          class="w-full h-full object-cover opacity-15"
        />
        <div class="absolute inset-0 bg-gradient-to-br from-pink-50/90 to-purple-50/90"></div>
        <div class="absolute top-1/3 left-1/4 w-96 h-96 bg-pink-100 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-gentle-glow"></div>
        <div class="absolute bottom-1/3 right-1/4 w-80 h-80 bg-purple-100 rounded-full mix-blend-multiply filter blur-3xl opacity-30 animate-gentle-glow animation-delay-2000"></div>
      </div>

      <!-- Floating Hearts -->
      <div class="absolute inset-0 pointer-events-none overflow-hidden">
        @for (heart of floatingHearts; track heart.id) {
          <div
            class="absolute text-pink-200 opacity-30 animate-gentle-float"
            [style.left.%]="heart.left"
            [style.top.%]="heart.top"
            [style.animation-delay]="heart.delay + 's'"
            [style.font-size.px]="heart.size">
            💖
          </div>
        }
      </div>

      <!-- Main Content -->
      <div class="relative z-10 text-center px-6 sm:px-8 lg:px-12 max-w-5xl mx-auto">
        <!-- Elegant Glass Card -->
        <div class="glass-effect rounded-3xl p-12 sm:p-16 lg:p-20 shadow-xl border border-white/20">
          <div class="mb-8">
            <span class="text-4xl sm:text-5xl animate-soft-pulse">💖</span>
          </div>
          
          <h1 class="font-dancing text-5xl sm:text-7xl lg:text-8xl text-romantic mb-6 animate-soft-pulse leading-tight">
            Happy 1st Monthsary
          </h1>
          
          <h2 class="font-playfair text-2xl sm:text-3xl lg:text-4xl text-gray-600 mb-8 italic font-medium">
            My Tangi 
          </h2>
          
          <p class="font-poppins text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-12 font-light">
            Welcome to our digital love story, where every moment is treasured
            and every memory is painted with pure affection
          </p>
          
          <!-- Navigation Buttons -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <a routerLink="/our-story" 
               class="bg-gradient-to-r from-pink-300 to-purple-300 text-gray-700 px-10 py-4 rounded-full font-poppins font-medium text-lg hover:shadow-xl transform hover:scale-105 transition-all duration-500 hover:from-pink-400 hover:to-purple-400 hover:text-white">
              Our Story ✨
            </a>
            <a routerLink="/love-notes" 
               class="bg-white/50 backdrop-blur-sm text-gray-700 px-10 py-4 rounded-full font-poppins font-medium text-lg hover:shadow-xl transform hover:scale-105 transition-all duration-500 border border-pink-200 hover:bg-pink-100">
              Love Notes 💕
            </a>
          </div>
        </div>
      </div>

      <!-- Minimal Sparkle Effects -->
      <div class="absolute top-20 left-20 text-pink-200 text-2xl animate-gentle-glow">✨</div>
      <div class="absolute bottom-20 right-20 text-purple-200 text-2xl animate-gentle-glow animation-delay-2000">💫</div>
    </section>

    <!-- Quick Preview Section -->
    <section class="py-20 px-6 sm:px-8 lg:px-12">
      <div class="max-w-6xl mx-auto">
        <div class="text-center mb-16">
          <h2 class="font-playfair text-4xl sm:text-5xl text-romantic mb-6">
            Our Journey Together
          </h2>
          <p class="font-poppins text-xl text-gray-600 max-w-2xl mx-auto font-light">
            Every day with you is a new chapter in our beautiful love story
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <!-- Story Preview -->
          <div class="glass-effect rounded-2xl overflow-hidden card-hover border border-white/20 group">
            <div class="relative h-32 overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-1516589178581-6cd7833ae3b2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
                alt="Our Story"
                class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
              <div class="absolute top-2 left-2">
                <div class="text-3xl animate-soft-pulse drop-shadow-lg">📖</div>
              </div>
            </div>
            <div class="p-6 text-center">
              <h3 class="font-playfair text-2xl font-semibold text-gray-700 mb-4">Our Story</h3>
              <p class="font-poppins text-gray-600 mb-6 font-light">From our first glance to this beautiful moment</p>
              <a routerLink="/our-story" class="text-romantic font-medium hover:underline">Read More →</a>
            </div>
          </div>

          <!-- Love Notes Preview -->
          <div class="glass-effect rounded-2xl overflow-hidden card-hover border border-white/20 group">
            <div class="relative h-32 overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-1518568814500-bf0f8d125f46?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
                alt="Love Notes"
                class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
              <div class="absolute top-2 left-2">
                <div class="text-3xl animate-soft-pulse drop-shadow-lg">💌</div>
              </div>
            </div>
            <div class="p-6 text-center">
              <h3 class="font-playfair text-2xl font-semibold text-gray-700 mb-4">Love Notes</h3>
              <p class="font-poppins text-gray-600 mb-6 font-light">All the things I love about you</p>
              <a routerLink="/love-notes" class="text-romantic font-medium hover:underline">Discover →</a>
            </div>
          </div>

          <!-- Memories Preview -->
          <div class="glass-effect rounded-2xl overflow-hidden card-hover border border-white/20 group">
            <div class="relative h-32 overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-1529333166437-7750a6dd5a70?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80"
                alt="Memories"
                class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
              <div class="absolute top-2 left-2">
                <div class="text-3xl animate-soft-pulse drop-shadow-lg">📸</div>
              </div>
            </div>
            <div class="p-6 text-center">
              <h3 class="font-playfair text-2xl font-semibold text-gray-700 mb-4">Memories</h3>
              <p class="font-poppins text-gray-600 mb-6 font-light">Our precious moments together</p>
              <a routerLink="/memories" class="text-romantic font-medium hover:underline">Explore →</a>
            </div>
          </div>
        </div>
      </div>
    </section>
  `,
  styles: []
})
export class HomeComponent {
  floatingHearts = [
    { id: 1, left: 10, top: 20, size: 20, delay: 0 },
    { id: 2, left: 80, top: 10, size: 25, delay: 1 },
    { id: 3, left: 60, top: 70, size: 18, delay: 2 },
    { id: 4, left: 30, top: 50, size: 22, delay: 0.5 }
  ];
}
